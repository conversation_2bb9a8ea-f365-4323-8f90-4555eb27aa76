#include "StreamCaptureController.h"
#include "Win7Compatibility.h"
#include <iostream>
#include <signal.h>
#include <tlhelp32.h>

using namespace StreamCapture;

// 全局控制器实例
std::unique_ptr<StreamCaptureController> g_controller;
std::atomic<bool> g_running(true);

// 信号处理函数
void SignalHandler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_INFO("Received shutdown signal");
            g_running = false;
            if (g_controller) {
                g_controller->StopCapture();
            }
            break;
    }
}

// 显示帮助信息
void ShowHelp() {
    std::cout << R"(
StreamCapture - Multi-stream Video Capture and Streaming System

Usage: StreamCapture.exe [options]

Options:
  -c, --config <file>    Configuration file path (default: config/capture.json)
  -h, --help            Show this help message
  -v, --version         Show version information
  -d, --daemon          Run as daemon (background service)
  --log-level <level>   Set log level (debug, info, warning, error)
  --log-file <file>     Set log file path
  --list-targets        List available capture targets
  --test-config         Test configuration file validity

Examples:
  StreamCapture.exe
  StreamCapture.exe -c custom_config.json
  StreamCapture.exe --log-level debug --log-file debug.log
  StreamCapture.exe --list-targets

For more information, visit: https://github.com/your-repo/streamcapture
)";
}

// 显示版本信息
void ShowVersion() {
    std::cout << R"(
StreamCapture v1.0.0
Build: )" << __DATE__ << " " << __TIME__ << R"(
Platform: Windows (x64)
Compatibility: Windows 7 SP1 and later

Features:
  - Direct3D 11 frame capture
  - Hardware/Software video encoding (H.264/H.265)
  - Multi-stream RTMP/SRT/File output
  - Windows 7 compatibility layer
  - Real-time performance monitoring

Copyright (c) 2025 StreamCapture Team
)";
}

// 列出可用的捕获目标
void ListTargets() {
    std::cout << "\nScanning for capture targets...\n";
    std::cout << "==========================================\n";
    
    // 简化的进程扫描
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        std::cout << "Error: Unable to create process snapshot\n";
        return;
    }
    
    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);
    
    std::vector<std::string> targets;
    
    if (Process32First(hSnapshot, &pe32)) {
        do {
            // Convert TCHAR* to std::string properly
            std::string processName;
            #ifdef UNICODE
            std::wstring wProcessName(pe32.szExeFile);
            processName.assign(wProcessName.begin(), wProcessName.end());
            #else
            processName = pe32.szExeFile;
            #endif
            
            // 过滤常见的游戏和应用程序
            if (processName.find("Unity") != std::string::npos ||
                processName.find(".exe") != std::string::npos) {
                
                bool isDuplicate = false;
                for (const auto& existing : targets) {
                    if (existing == processName) {
                        isDuplicate = true;
                        break;
                    }
                }
                
                if (!isDuplicate) {
                    targets.push_back(processName);
                }
            }
        } while (Process32Next(hSnapshot, &pe32));
    }
    
    CloseHandle(hSnapshot);
    
    if (targets.empty()) {
        std::cout << "No suitable capture targets found.\n";
    } else {
        std::cout << "Found " << targets.size() << " potential capture targets:\n\n";
        for (size_t i = 0; i < targets.size(); ++i) {
            std::cout << "  " << (i + 1) << ". " << targets[i] << "\n";
        }
    }
    
    std::cout << "\nNote: Only processes with Direct3D 11 support can be captured.\n";
    std::cout << "==========================================\n";
}

// 测试配置文件
bool TestConfig(const std::string& configPath) {
    std::cout << "\nTesting configuration file: " << configPath << "\n";
    std::cout << "==========================================\n";
    
    std::vector<TargetConfig> targets;
    if (!ConfigLoader::LoadConfig(configPath, targets)) {
        std::cout << "ERROR: Failed to load configuration file\n";
        return false;
    }
    
    std::cout << "Configuration loaded successfully\n";
    std::cout << "Found " << targets.size() << " target(s):\n\n";
    
    bool allValid = true;
    for (size_t i = 0; i < targets.size(); ++i) {
        const auto& target = targets[i];
        std::cout << "Target " << (i + 1) << ": " << target.name << "\n";
        
        std::string errorMsg;
        if (ConfigLoader::ValidateConfig(target, errorMsg)) {
            std::cout << "  Status: VALID\n";
            std::cout << "  Process: " << target.capture.processName << "\n";
            std::cout << "  FPS: " << target.capture.targetFPS << "\n";
            std::cout << "  Codec: " << target.encoder.codec << "\n";
            std::cout << "  Bitrate: " << target.encoder.bitrate << " kbps\n";
            std::cout << "  Outputs: " << target.outputs.size() << "\n";
        } else {
            std::cout << "  Status: INVALID\n";
            std::cout << "  Error: " << errorMsg << "\n";
            allValid = false;
        }
        std::cout << "\n";
    }
    
    if (allValid) {
        std::cout << "Configuration validation: PASSED\n";
    } else {
        std::cout << "Configuration validation: FAILED\n";
    }
    
    std::cout << "==========================================\n";
    return allValid;
}

// 设置控制台
void SetupConsole() {
    // 设置控制台标题
    SetConsoleTitleA("StreamCapture - Video Capture System");
    
    // 启用UTF-8输出
    SetConsoleOutputCP(CP_UTF8);
    
    // 设置控制台颜色
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hConsole != INVALID_HANDLE_VALUE) {
        SetConsoleTextAttribute(hConsole, FOREGROUND_GREEN | FOREGROUND_BLUE | FOREGROUND_INTENSITY);
    }
}

// 显示启动横幅
void ShowBanner() {
    std::cout << R"(
    ███████╗████████╗██████╗ ███████╗ █████╗ ███╗   ███╗ ██████╗ █████╗ ██████╗ ████████╗██╗   ██╗██████╗ ███████╗
    ███╔══██║╚══██╔══╝██╔══██╗██╔════╝██╔══██╗████╗ ████║██╔════╝██╔══██╗██╔══██╗╚══██╔══╝██║   ██║██╔══██╗██╔════╝
    ███████╔╝   ██║   ██████╔╝█████╗  ███████║██╔████╔██║██║     ███████║██████╔╝   ██║   ██║   ██║██████╔╝█████╗  
    ███╔══██╗   ██║   ██╔══██╗██╔══╝  ██╔══██║██║╚██╔╝██║██║     ██╔══██║██╔═══╝    ██║   ██║   ██║██╔══██╗██╔══╝  
    ███████╔╝   ██║   ██║  ██║███████╗██║  ██║██║ ╚═╝ ██║╚██████╗██║  ██║██║        ██║   ╚██████╔╝██║  ██║███████╗
    ╚══════╝    ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝ ╚═════╝╚═╝  ╚═╝╚═╝        ╚═╝    ╚═════╝ ╚═╝  ╚═╝╚══════╝
    
    Multi-stream Video Capture and Streaming System v1.0.0
    Windows 7+ Compatible | Hardware Accelerated | Real-time Streaming
    )" << std::endl;
}

// 显示系统信息
void ShowSystemInfo() {
    auto& compat = Win7Compatibility::Instance();
    if (!compat.Initialize()) {
        std::cout << "Warning: Failed to initialize compatibility layer\n";
        return;
    }
    
    auto sysInfo = compat.GetSystemInfo();
    
    std::cout << "\n=== System Information ===\n";
    std::cout << "OS: " << (sysInfo.isWindows7 ? "Windows 7" : "Windows 8+") << "\n";
    std::cout << "Memory: " << sysInfo.totalMemoryMB << " MB total, " 
              << sysInfo.availableMemoryMB << " MB available\n";
    std::cout << "GPU: " << sysInfo.gpuName << "\n";
    
    std::cout << "\nHardware Encoding Support:\n";
    std::cout << "  NVIDIA NVENC: " << (sysInfo.hasNVENC ? "YES" : "NO") << "\n";
    std::cout << "  Intel QSV: " << (sysInfo.hasQSV ? "YES" : "NO") << "\n";
    std::cout << "  AMD AMF: " << (sysInfo.hasAMF ? "YES" : "NO") << "\n";
    
    if (!sysInfo.hasNVENC && !sysInfo.hasQSV && !sysInfo.hasAMF) {
        std::cout << "  Note: Software encoding will be used\n";
    }
    
    std::cout << "===========================\n\n";
}

// 主循环
void RunMainLoop() {
    std::cout << "StreamCapture is running...\n";
    std::cout << "Press Ctrl+C to stop\n\n";
    
    auto lastStatsTime = std::chrono::steady_clock::now();
    
    while (g_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 每5秒显示统计信息
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastStatsTime).count() >= 5) {
            if (g_controller) {
                auto stats = g_controller->GetGlobalStats();
                
                std::cout << "\r=== Status ===  "
                          << "Active: " << stats.activeTargets 
                          << " | Captured: " << stats.totalFramesCaptured
                          << " | Dropped: " << stats.droppedFrames
                          << " | Memory: " << stats.systemStatus.availableMemoryMB << "MB"
                          << "                    ";
                std::cout.flush();
            }
            lastStatsTime = now;
        }
    }
}

// 主函数
int main(int argc, char* argv[]) {
    // 设置控制台
    SetupConsole();
    
    // 解析命令行参数
    std::string configPath = "config/capture.json";
    std::string logLevel = "info";
    std::string logFile = "";
    bool showHelp = false;
    bool showVersion = false;
    bool listTargets = false;
    bool testConfig = false;
    bool daemonMode = false;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            showHelp = true;
        } else if (arg == "-v" || arg == "--version") {
            showVersion = true;
        } else if (arg == "--list-targets") {
            listTargets = true;
        } else if (arg == "--test-config") {
            testConfig = true;
        } else if (arg == "-d" || arg == "--daemon") {
            daemonMode = true;
        } else if ((arg == "-c" || arg == "--config") && i + 1 < argc) {
            configPath = argv[++i];
        } else if (arg == "--log-level" && i + 1 < argc) {
            logLevel = argv[++i];
        } else if (arg == "--log-file" && i + 1 < argc) {
            logFile = argv[++i];
        }
    }
    
    // 处理特殊命令
    if (showHelp) {
        ShowHelp();
        return 0;
    }
    
    if (showVersion) {
        ShowVersion();
        return 0;
    }
    
    if (listTargets) {
        ListTargets();
        return 0;
    }
    
    if (testConfig) {
        return TestConfig(configPath) ? 0 : 1;
    }
    
    // 显示启动信息
    if (!daemonMode) {
        ShowBanner();
        ShowSystemInfo();
    }
    
    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    try {
        // 初始化日志系统
        StreamCapture::Logger::Level level = StreamCapture::Logger::Level::Info;
        if (logLevel == "debug") level = StreamCapture::Logger::Level::Debug;
        else if (logLevel == "warning") level = StreamCapture::Logger::Level::Warning;
        else if (logLevel == "error") level = StreamCapture::Logger::Level::Error;
        
        if (!StreamCapture::Logger::Instance().Initialize(logFile, level)) {
            std::cerr << "Warning: Failed to initialize logger\n";
        }
        
        LOG_INFO("StreamCapture starting...");
        
        // 创建控制器
        g_controller = std::make_unique<StreamCaptureController>();
        
        // 设置回调
        g_controller->SetStateCallback([](auto oldState, auto newState) {
            LOG_INFO_F("State changed: %d -> %d", (int)oldState, (int)newState);
        });
        
        g_controller->SetErrorCallback([](const ErrorInfo& error) {
            LOG_ERROR_F("Controller error: %s", error.message.c_str());
        });
        
        g_controller->SetStatusCallback([daemonMode](const std::string& message) {
            if (!daemonMode) {
                std::cout << "\nStatus: " << message << std::endl;
            }
            LOG_INFO(message);
        });
        
        // 初始化控制器
        if (!g_controller->Initialize(configPath)) {
            LOG_FATAL("Failed to initialize controller");
            std::cerr << "Error: Failed to initialize StreamCapture\n";
            return 1;
        }
        
        // 启动捕获
        if (!g_controller->StartCapture()) {
            LOG_ERROR("Failed to start capture");
            std::cerr << "Error: Failed to start capture\n";
            return 1;
        }
        
        LOG_INFO("StreamCapture started successfully");
        
        // 进入主循环
        if (!daemonMode) {
            RunMainLoop();
        } else {
            // 守护进程模式 - 简单等待
            while (g_running) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
        
        // 清理
        LOG_INFO("Shutting down StreamCapture...");
        g_controller->Shutdown();
        g_controller.reset();
        
        LOG_INFO("StreamCapture shutdown complete");
        
        if (!daemonMode) {
            std::cout << "\nStreamCapture stopped.\n";
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        LOG_FATAL_F("Exception: %s", e.what());
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        LOG_FATAL("Unknown exception");
        std::cerr << "Fatal error: Unknown exception\n";
        return 1;
    }
}