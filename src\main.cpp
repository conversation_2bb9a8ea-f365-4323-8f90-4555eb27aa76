#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <fstream>
#include <thread>
#include <csignal>
#include <atomic>
#include <memory>
#include <vector>
#include <string>

#include "StreamCaptureController.h"
#include "Common.h"
#include "ConfigManager.h"
#include "Win7Compatibility.h"

using namespace std;

// 全局变量
static std::atomic<bool> g_running(true);
static std::unique_ptr<StreamCapture::StreamCaptureController> g_controller;

// 信号处理函数
void SignalHandler(int signal)
{
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    g_running = false;

    // 如果控制器存在，通知它停止
    if (g_controller)
    {
        g_controller->StopCapture();
    }
}

// 主循环函数
void RunMainLoop()
{
    std::cout << "\nStreamCapture is running. Press Ctrl+C to stop.\n"
              << std::endl;

    while (g_running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    std::cout << "Shutting down..." << std::endl;
}

// 显示帮助信息
void ShowHelp()
{
    std::cout << R"(
StreamCapture - Multi-stream Video Capture and Streaming System

Usage: StreamCapture.exe [options]

Options:
  -c, --config <file>    Configuration file path (default: config/capture.json)
  -h, --help            Show this help message
  -v, --version         Show version information
  -d, --daemon          Run as daemon (background service)
  --log-level <level>   Set log level (debug, info, warning, error)
  --log-file <file>     Set log file path
  --list-targets        List available capture targets
  --test-config         Test configuration file validity

Examples:
  StreamCapture.exe
  StreamCapture.exe -c custom_config.json
  StreamCapture.exe --log-level debug --log-file debug.log
  StreamCapture.exe --list-targets

For more information, visit: https://github.com/your-repo/streamcapture
)";
}

// 显示版本信息
void ShowVersion()
{
    std::cout << R"(
StreamCapture v1.0.0
Build: )" << __DATE__
              << " " << __TIME__ << R"(
Platform: Windows (x64)
Compatibility: Windows 7 SP1 and later

Features:
  - Direct3D 11 frame capture
  - Hardware/Software video encoding (H.264/H.265)
  - Multi-stream RTMP/SRT/File output
  - Windows 7 compatibility layer
  - Real-time performance monitoring

Copyright (c) 2025 StreamCapture Team
)";
}

// 列出可用的捕获目标
void ListTargets()
{
    std::cout << "\nScanning for capture targets...\n";
    std::cout << "==========================================\n";

    // 简化的进程扫描
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE)
    {
        std::cout << "Error: Unable to create process snapshot\n";
        return;
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    std::vector<std::string> targets;

    if (Process32First(hSnapshot, &pe32))
    {
        do
        {
            // Convert TCHAR* to std::string properly
            std::string processName;
#ifdef UNICODE
            std::wstring wProcessName(pe32.szExeFile);
            processName.assign(wProcessName.begin(), wProcessName.end());
#else
            processName = pe32.szExeFile;
#endif

            // 过滤常见的游戏和应用程序
            if (processName.find("Unity") != std::string::npos ||
                processName.find(".exe") != std::string::npos)
            {

                bool isDuplicate = false;
                for (const auto &existing : targets)
                {
                    if (existing == processName)
                    {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate)
                {
                    targets.push_back(processName);
                }
            }
        } while (Process32Next(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);

    if (targets.empty())
    {
        std::cout << "No suitable capture targets found.\n";
    }
    else
    {
        std::cout << "Found " << targets.size() << " potential capture targets:\n\n";
        for (size_t i = 0; i < targets.size(); ++i)
        {
            std::cout << "  " << (i + 1) << ". " << targets[i] << "\n";
        }
    }

    std::cout << "\nNote: Only processes with Direct3D 11 support can be captured.\n";
    std::cout << "==========================================\n";
}

// 测试配置文件
bool TestConfig(const std::string &configPath)
{
    std::cout << "\nTesting configuration file: " << configPath << "\n";
    std::cout << "==========================================\n";

    // 检查文件是否存在
    std::ifstream file(configPath);
    if (!file.good())
    {
        std::cout << "Error: Configuration file not found or not accessible\n";
        return false;
    }
    file.close();

    // 尝试加载配置
    std::vector<StreamCapture::TargetConfig> targets;
    StreamCapture::ConfigLoader loader;
    if (!loader.LoadConfig(configPath, targets))
    {
        std::cout << "Error: Failed to parse configuration file\n";
        return false;
    }

    std::cout << "Success: Configuration file loaded successfully\n";
    std::cout << "Found " << targets.size() << " target(s)\n\n";

    for (size_t i = 0; i < targets.size(); ++i)
    {
        const auto &target = targets[i];
        std::cout << "Target " << (i + 1) << ":\n";
        std::cout << "  Name: " << target.name << "\n";
        std::cout << "  Enabled: " << (target.enabled ? "Yes" : "No") << "\n";
        std::cout << "  Process: " << target.capture.processName << "\n";
        std::cout << "  FPS: " << target.capture.targetFPS << "\n";
        std::cout << "  Encoder: " << target.encoder.type << " " << target.encoder.codec << "\n";
        std::cout << "  Bitrate: " << target.encoder.bitrate << " kbps\n";
        std::cout << "  Outputs: " << target.outputs.size() << "\n";
        for (const auto &output : target.outputs)
        {
            std::cout << "    - " << output.type << ": " << output.url << "\n";
        }
        std::cout << "\n";
    }

    std::cout << "==========================================\n";
    return true;
}

// 设置控制台
void SetupConsole()
{
    // 设置控制台标题
    SetConsoleTitleA("StreamCapture - Video Capture System");

    // 启用UTF-8输出
    SetConsoleOutputCP(CP_UTF8);

    // 设置控制台颜色
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hConsole != INVALID_HANDLE_VALUE)
    {
        SetConsoleTextAttribute(hConsole, FOREGROUND_GREEN | FOREGROUND_BLUE | FOREGROUND_INTENSITY);
    }
}

// 显示启动横幅
void ShowBanner()
{
    std::cout << R"(
    ███████╗████████╗██████╗ ███████╗ █████╗ ███╗   ███╗ ██████╗ █████╗ ██████╗ ████████╗██╗   ██╗██████╗ ███████╗
    ███╔══██║╚══██╔══╝██╔══██╗██╔════╝██╔══██╗████╗ ████║██╔════╝██╔══██╗██╔══██╗╚══██╔══╝██║   ██║██╔══██╗██╔════╝
    ███████╔╝   ██║   ██████╔╝█████╗  ███████║██╔████╔██║██║     ███████║██████╔╝   ██║   ██║   ██║██████╔╝█████╗  
    ███╔══██╗   ██║   ██╔══██╗██╔══╝  ██╔══██║██║╚██╔╝██║██║     ██╔══██║██╔═══╝    ██║   ██║   ██║██╔══██╗██╔══╝  
    ███████╔╝   ██║   ██║  ██║███████╗██║  ██║██║ ╚═╝ ██║╚██████╗██║  ██║██║        ██║   ╚██████╔╝██║  ██║███████╗
    ╚══════╝    ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝ ╚═════╝╚═╝  ╚═╝╚═╝        ╚═╝    ╚═════╝ ╚═╝  ╚═╝╚══════╝
    
    Multi-stream Video Capture and Streaming System v1.0.0
    Windows 7+ Compatible | Hardware Accelerated | Real-time Streaming
    )" << std::endl;
}

// 显示系统信息
void ShowSystemInfo()
{
    auto &compat = StreamCapture::Win7Compatibility::Instance();
    if (!compat.Initialize())
    {
        std::cout << "Warning: Failed to initialize compatibility layer\n";
        return;
    }

    auto sysInfo = compat.GetSystemInfo();

    std::cout << "\n=== System Information ===\n";
    std::cout << "OS: " << (sysInfo.isWindows7 ? "Windows 7" : "Windows 8+") << "\n";
    std::cout << "Memory: " << sysInfo.totalMemoryMB << " MB total, "
              << sysInfo.availableMemoryMB << " MB available\n";
    std::cout << "GPU: " << sysInfo.gpuName << "\n";

    std::cout << "\nHardware Encoding Support:\n";
    std::cout << "  NVIDIA NVENC: " << (sysInfo.hasNVENC ? "YES" : "NO") << "\n";
    std::cout << "  Intel QSV: " << (sysInfo.hasQSV ? "YES" : "NO") << "\n";
    std::cout << "  AMD AMF: " << (sysInfo.hasAMF ? "YES" : "NO") << "\n";

    if (!sysInfo.hasNVENC && !sysInfo.hasQSV && !sysInfo.hasAMF)
    {
        std::cout << "  Note: Software encoding will be used\n";
    }

    std::cout << "===========================\n\n";
}

// 主函数
int main(int argc, char *argv[])
{
    // 设置控制台
    SetupConsole();

    // 解析命令行参数
    std::string configPath = "config/capture.json";
    std::string logLevel = "info";
    std::string logFile = "";
    bool showHelp = false;
    bool showVersion = false;
    bool listTargets = false;
    bool testConfig = false;
    bool daemonMode = false;

    for (int i = 1; i < argc; ++i)
    {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help")
        {
            showHelp = true;
        }
        else if (arg == "-v" || arg == "--version")
        {
            showVersion = true;
        }
        else if (arg == "--list-targets")
        {
            listTargets = true;
        }
        else if (arg == "--test-config")
        {
            testConfig = true;
        }
        else if (arg == "-d" || arg == "--daemon")
        {
            daemonMode = true;
        }
        else if ((arg == "-c" || arg == "--config") && i + 1 < argc)
        {
            configPath = argv[++i];
        }
        else if (arg == "--log-level" && i + 1 < argc)
        {
            logLevel = argv[++i];
        }
        else if (arg == "--log-file" && i + 1 < argc)
        {
            logFile = argv[++i];
        }
    }

    // 处理特殊命令
    if (showHelp)
    {
        ShowHelp();
        return 0;
    }

    if (showVersion)
    {
        ShowVersion();
        return 0;
    }

    if (listTargets)
    {
        ListTargets();
        return 0;
    }

    if (testConfig)
    {
        return TestConfig(configPath) ? 0 : 1;
    }

    // 显示启动信息
    if (!daemonMode)
    {
        ShowBanner();
        ShowSystemInfo();
    }

    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

    std::unique_ptr<StreamCapture::StreamCaptureController> controller;

    try
    {
        // 初始化日志系统
        StreamCapture::Logger::Level level = StreamCapture::Logger::Level::Info;
        if (logLevel == "debug")
            level = StreamCapture::Logger::Level::Debug;
        else if (logLevel == "warning")
            level = StreamCapture::Logger::Level::Warning;
        else if (logLevel == "error")
            level = StreamCapture::Logger::Level::Error;

        if (!StreamCapture::Logger::Instance().Initialize(logFile, level))
        {
            std::cerr << "Warning: Failed to initialize logger\n";
        }

        StreamCapture::Logger::Instance().Info("StreamCapture starting...");

        // 创建控制器
        controller = std::make_unique<StreamCapture::StreamCaptureController>();

        // 设置回调
        controller->SetStateCallback([](auto oldState, auto newState)
                                     { StreamCapture::Logger::Instance().Info("State changed: " + std::to_string((int)oldState) + " -> " + std::to_string((int)newState)); });

        controller->SetErrorCallback([](const StreamCapture::ErrorInfo &error)
                                     { StreamCapture::Logger::Instance().Error("Controller error: " + error.message); });

        controller->SetStatusCallback([daemonMode](const std::string &message)
                                      {
            if (!daemonMode) {
                std::cout << "\nStatus: " << message << std::endl;
            }
            StreamCapture::Logger::Instance().Info(message); });

        // 初始化控制器
        if (!controller->Initialize(configPath))
        {
            StreamCapture::Logger::Instance().Error("Failed to initialize controller");
            std::cerr << "Error: Failed to initialize StreamCapture\n";
            return 1;
        }

        // 启动捕获
        if (!controller->StartCapture())
        {
            StreamCapture::Logger::Instance().Error("Failed to start capture");
            std::cerr << "Error: Failed to start capture\n";
            return 1;
        }

        StreamCapture::Logger::Instance().Info("StreamCapture started successfully");

        // 进入主循环
        if (!daemonMode)
        {
            RunMainLoop();
        }
        else
        {
            // 守护进程模式 - 简单等待
            while (g_running)
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }

        // 清理
        StreamCapture::Logger::Instance().Info("Shutting down StreamCapture...");
        controller->Shutdown();
        controller.reset();

        StreamCapture::Logger::Instance().Info("StreamCapture shutdown complete");

        if (!daemonMode)
        {
            std::cout << "\nStreamCapture stopped.\n";
        }

        // 显式关闭日志系统
        StreamCapture::Logger::Instance().Shutdown();

        return 0;
    }
    catch (const std::exception &e)
    {
        StreamCapture::Logger::Instance().Error("Exception: " + std::string(e.what()));
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        StreamCapture::Logger::Instance().Error("Unknown exception");
        std::cerr << "Fatal error: Unknown exception\n";
        return 1;
    }
}