@echo off
setlocal enabledelayedexpansion

echo ========================================
echo StreamCapture Multi-Stream Test Script
echo ========================================
echo.

REM 设置工作目录
cd /d "%~dp0\.."

REM 检查编译是否完成
if not exist "redist_desk\StreamCaptureTest.exe" (
    echo Error: StreamCaptureTest.exe not found
    echo Please build the project first using CMake
    echo.
    echo Build commands:
    echo   cd build_desk
    echo   cmake --build . --config Release
    echo.
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config\stream_config.json" (
    echo Error: Configuration file not found: config\stream_config.json
    echo Please ensure the configuration file exists
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "recordings" mkdir recordings
if not exist "debug" mkdir debug

echo Configuration loaded from: config\stream_config.json
echo.

REM 显示配置信息
echo Current configuration:
echo - Target process: test1.exe
echo - Capture method: DirectX Hook
echo - Encoders: Hardware (NVENC), Software backup
echo - Streams: RTMP, File recording
echo.

REM 检查目标进程
echo Checking for target process (test1.exe)...
tasklist /FI "IMAGENAME eq test1.exe" 2>NUL | find /I /N "test1.exe" >NUL
if %ERRORLEVEL% EQU 0 (
    echo ✓ Target process test1.exe is running
) else (
    echo ⚠ Warning: Target process test1.exe is not running
    echo   The test will generate synthetic frames instead
)
echo.

REM 检查 FFmpeg
where ffmpeg >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ FFmpeg found in PATH
) else (
    echo ⚠ Warning: FFmpeg not found in PATH
    echo   Hardware encoding may not work properly
)
echo.

REM 检查硬件编码支持
echo Checking hardware encoding support...
ffmpeg -hide_banner -encoders 2>nul | findstr "nvenc" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ NVIDIA NVENC support detected
) else (
    echo ⚠ NVIDIA NVENC not available, will use software encoding
)

ffmpeg -hide_banner -encoders 2>nul | findstr "qsv" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ Intel Quick Sync support detected
) else (
    echo ⚠ Intel Quick Sync not available
)
echo.

REM 询问是否启动 RTMP 服务器
echo Do you want to start a local RTMP server for testing? (Y/N)
set /p start_rtmp="Enter choice: "
if /i "!start_rtmp!"=="Y" (
    echo Starting RTMP server in background...
    start "RTMP Server" cmd /c "scripts\start_rtmp_server.bat"
    echo Waiting for RTMP server to start...
    timeout /t 3 /nobreak >nul
    echo.
)

REM 显示测试信息
echo ========================================
echo Starting StreamCapture Test
echo ========================================
echo.
echo Test features:
echo - Multi-encoder support (Hardware + Software)
echo - Multi-stream publishing (RTMP + File)
echo - Real-time statistics monitoring
echo - Automatic reconnection handling
echo - Frame rate control (30 FPS)
echo.
echo Output locations:
echo - RTMP Stream: rtmp://localhost:1935/live/stream1
echo - File Recording: recordings\capture_YYYYMMDD_HHMMSS.mp4
echo - Debug Logs: logs\stream_capture.log
echo.
echo Press Enter to start the test...
pause >nul

REM 启动测试程序
echo Starting StreamCapture test application...
echo.
echo ========================================
echo StreamCapture Test Running
echo ========================================
echo.
echo Instructions:
echo - The test will generate synthetic video frames
echo - Statistics will be displayed every 5 seconds
echo - Press Enter in the test window to stop
echo.
echo If test1.exe is running, it will attempt to hook and capture real frames
echo Otherwise, it will generate colorful test patterns
echo.

REM 运行测试程序
"redist_desk\StreamCaptureTest.exe"

echo.
echo ========================================
echo Test Completed
echo ========================================
echo.

REM 显示结果文件
if exist "recordings\*.mp4" (
    echo ✓ Recorded files found:
    dir /b "recordings\*.mp4" 2>nul
    echo.
)

if exist "logs\stream_capture.log" (
    echo ✓ Log file created: logs\stream_capture.log
    echo.
)

REM 询问是否查看日志
echo Do you want to view the log file? (Y/N)
set /p view_log="Enter choice: "
if /i "!view_log!"=="Y" (
    if exist "logs\stream_capture.log" (
        notepad "logs\stream_capture.log"
    ) else (
        echo Log file not found
    )
)

echo.
echo Test completed. Check the output files for results.
echo.
echo Troubleshooting:
echo - If encoding fails: Check FFmpeg installation and hardware support
echo - If RTMP fails: Ensure port 1935 is not blocked by firewall
echo - If hook fails: Run as administrator and ensure test1.exe is running
echo.
pause
