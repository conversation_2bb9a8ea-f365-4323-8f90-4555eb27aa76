@echo off
echo Starting RTMP Server for Testing...
echo.

REM 检查是否安装了 FFmpeg
where ffmpeg >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: FFmpeg not found in PATH
    echo Please install FFmpeg and add it to your PATH
    echo Download from: https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM 检查是否安装了 Node.js (用于简单的 RTMP 服务器)
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Warning: Node.js not found. Using FFmpeg as RTMP relay instead.
    goto :ffmpeg_relay
)

REM 检查是否安装了 node-media-server
if not exist "node_modules\node-media-server" (
    echo Installing node-media-server...
    npm install node-media-server
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install node-media-server, using FFmpeg relay instead.
        goto :ffmpeg_relay
    )
)

REM 创建简单的 RTMP 服务器脚本
echo Creating RTMP server script...
(
echo const NodeMediaServer = require('node-media-server'^);
echo.
echo const config = {
echo   rtmp: {
echo     port: 1935,
echo     chunk_size: 60000,
echo     gop_cache: true,
echo     ping: 30,
echo     ping_timeout: 60
echo   },
echo   http: {
echo     port: 8000,
echo     allow_origin: '*'
echo   }
echo };
echo.
echo var nms = new NodeMediaServer(config^);
echo nms.run(^);
echo.
echo console.log('RTMP Server started on port 1935'^);
echo console.log('HTTP Server started on port 8000'^);
echo console.log('Stream URL: rtmp://localhost:1935/live/stream1'^);
echo console.log('Play URL: http://localhost:8000/live/stream1.flv'^);
) > rtmp_server.js

echo Starting Node.js RTMP Server...
echo Stream URL: rtmp://localhost:1935/live/stream1
echo Play URL: http://localhost:8000/live/stream1.flv
echo.
echo Press Ctrl+C to stop the server
node rtmp_server.js
goto :end

:ffmpeg_relay
echo Starting FFmpeg RTMP relay...
echo This will create a simple RTMP server that saves streams to files
echo.

REM 创建输出目录
if not exist "rtmp_output" mkdir rtmp_output

echo Stream URL: rtmp://localhost:1935/live/stream1
echo Output will be saved to: rtmp_output\stream1.flv
echo.
echo Press Ctrl+C to stop the relay

REM 使用 FFmpeg 作为 RTMP 服务器
ffmpeg -f flv -listen 1 -i rtmp://localhost:1935/live/stream1 -c copy rtmp_output\stream1_%%Y%%m%%d_%%H%%M%%S.flv

:end
echo RTMP Server stopped.
pause
