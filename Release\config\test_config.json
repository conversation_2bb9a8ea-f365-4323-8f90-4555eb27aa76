{"global": {"logLevel": "info", "logFile": "logs/streamcapture.log", "enableConsoleOutput": true, "maxLogFileSize": 10485760, "maxLogFiles": 5}, "capture": {"enabled": true, "targetProcess": "test1.exe", "captureMethod": "d3d11", "frameRate": 30, "enableAudio": false, "audioDevice": "default", "bufferSize": 10, "enableGPUOptimization": true}, "encoders": [{"id": "main_stream", "enabled": true, "type": "software", "codec": "h264", "width": 1920, "height": 1080, "fps": 30, "bitrate": 4000, "preset": "fast", "profile": "main", "keyframeInterval": 60, "enableBFrames": false, "bufferSize": 10, "hardwareType": "nvenc"}], "streams": [{"id": "rtmp_main", "enabled": true, "encoderId": "main_stream", "type": "rtmp", "url": "rtmp://localhost:1935/live/stream1", "reconnectInterval": 5000, "maxReconnectAttempts": 5, "enableAutoReconnect": true, "connectionTimeout": 10000, "sendTimeout": 5000, "metadata": {"title": "Unity3D Test Stream", "description": "Direct3D11 Hook Capture Stream"}}]}