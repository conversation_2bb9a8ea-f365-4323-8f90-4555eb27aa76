#include "ConfigManager.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <nlohmann/json.hpp>

#define ASSIGN_IF_CONTAINS(json, field, var) \
    if (json.contains(#field)) var = json[#field]

namespace StreamCapture
{

  using json = nlohmann::json;

  ConfigManager::ConfigManager()
  {
    SetDefaultConfig();
  }

  ConfigManager::~ConfigManager() = default;

  bool ConfigManager::LoadFromFile(const std::string& filePath)
  {
    std::lock_guard<std::mutex> lock(mutex_);

    std::ifstream file(filePath);
    if (!file.is_open()) return false;

    std::stringstream buffer;
    buffer << file.rdbuf();
    return LoadFromString(buffer.str());
  }

  bool ConfigManager::SaveToFile(const std::string& filePath) const
  {
    std::lock_guard<std::mutex> lock(mutex_);

    std::ofstream file(filePath);
    if (!file.is_open()) return false;

    file << SaveToString();
    return true;
  }

  bool ConfigManager::LoadFromString(const std::string& jsonString)
  {
    try {
      std::lock_guard<std::mutex> lock(mutex_);
      json j = json::parse(jsonString);

      // 全局配置
      if (j.contains("global")) {
        auto& g = j["global"];
        ASSIGN_IF_CONTAINS(g, logLevel, config_.global.logLevel);
        ASSIGN_IF_CONTAINS(g, logFile, config_.global.logFile);
        ASSIGN_IF_CONTAINS(g, enableConsoleLog, config_.global.enableConsoleLog);
      }

      // 采集配置
      if (j.contains("capture")) {
        auto& c = j["capture"];
        ASSIGN_IF_CONTAINS(c, enabled, config_.capture.enabled);
        ASSIGN_IF_CONTAINS(c, targetProcess, config_.capture.targetProcess);
        ASSIGN_IF_CONTAINS(c, captureMethod, config_.capture.captureMethod);
        ASSIGN_IF_CONTAINS(c, bufferSize, config_.capture.bufferSize);
      }

      // 编码器配置
      if (j.contains("encoders")) {
        config_.encoders.clear();
        for (const auto& item : j["encoders"]) {
          EncoderConfigEx enc;
          ASSIGN_IF_CONTAINS(item, id, enc.id);
          ASSIGN_IF_CONTAINS(item, enabled, enc.enabled);
          ASSIGN_IF_CONTAINS(item, type, enc.type);
          ASSIGN_IF_CONTAINS(item, codec, enc.codec);
          ASSIGN_IF_CONTAINS(item, width, enc.width);
          ASSIGN_IF_CONTAINS(item, height, enc.height);
          ASSIGN_IF_CONTAINS(item, fps, enc.fps);
          ASSIGN_IF_CONTAINS(item, bitrate, enc.bitrate);
          ASSIGN_IF_CONTAINS(item, preset, enc.preset);
          ASSIGN_IF_CONTAINS(item, profile, enc.profile);
          ASSIGN_IF_CONTAINS(item, keyframeInterval, enc.keyframeInterval);
          ASSIGN_IF_CONTAINS(item, enableBFrames, enc.enableBFrames);
          ASSIGN_IF_CONTAINS(item, bufferSize, enc.bufferSize);
          ASSIGN_IF_CONTAINS(item, hardwareType, enc.hardwareType);
          config_.encoders.push_back(enc);
        }
      }

      // 流配置
      if (j.contains("streams")) {
        config_.streams.clear();
        for (const auto& item : j["streams"]) {
          StreamConfigEx stream;
          ASSIGN_IF_CONTAINS(item, id, stream.id);
          ASSIGN_IF_CONTAINS(item, enabled, stream.enabled);
          ASSIGN_IF_CONTAINS(item, encoderId, stream.encoderId);
          ASSIGN_IF_CONTAINS(item, type, stream.type);
          ASSIGN_IF_CONTAINS(item, url, stream.url);
          config_.streams.push_back(stream);
        }
      }

      return true;
    } catch (...) {
      return false;
    }
  }

  std::string ConfigManager::SaveToString() const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    json j;

    // 全局配置
    j["global"] = {
        {"logLevel", config_.global.logLevel},
        {"logFile", config_.global.logFile},
        {"enableConsoleLog", config_.global.enableConsoleLog}
    };

    // 采集配置
    j["capture"] = {
        {"enabled", config_.capture.enabled},
        {"targetProcess", config_.capture.targetProcess},
        {"captureMethod", config_.capture.captureMethod},
        {"bufferSize", config_.capture.bufferSize}
    };

    // 编码器配置
    json encodersJson = json::array();
    for (const auto& enc : config_.encoders) {
      encodersJson.push_back({
          {"id", enc.id},
          {"enabled", enc.enabled},
          {"type", enc.type},
          {"codec", enc.codec},
          {"width", enc.width},
          {"height", enc.height},
          {"fps", enc.fps},
          {"bitrate", enc.bitrate},
          {"preset", enc.preset},
          {"profile", enc.profile},
          {"keyframeInterval", enc.keyframeInterval},
          {"enableBFrames", enc.enableBFrames},
          {"bufferSize", enc.bufferSize},
          {"hardwareType", enc.hardwareType}
        });
    }
    j["encoders"] = encodersJson;

    // 流配置
    json streamsJson = json::array();
    for (const auto& stream : config_.streams) {
      streamsJson.push_back({
          {"id", stream.id},
          {"enabled", stream.enabled},
          {"encoderId", stream.encoderId},
          {"type", stream.type},
          {"url", stream.url}
        });
    }
    j["streams"] = streamsJson;

    return j.dump(4);
  }

  const EncoderConfigEx* ConfigManager::FindEncoderConfig(const std::string& id) const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = std::find_if(config_.encoders.begin(), config_.encoders.end(),
      [&id](const EncoderConfigEx& enc) { return enc.id == id; });
    return it != config_.encoders.end() ? &(*it) : nullptr;
  }

  const StreamConfigEx* ConfigManager::FindStreamConfig(const std::string& id) const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = std::find_if(config_.streams.begin(), config_.streams.end(),
      [&id](const StreamConfigEx& stream) { return stream.id == id; });
    return it != config_.streams.end() ? &(*it) : nullptr;
  }

  bool ConfigManager::UpdateEncoderConfig(const std::string& id, const EncoderConfigEx& newConfig)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = std::find_if(config_.encoders.begin(), config_.encoders.end(),
      [&id](const EncoderConfigEx& enc) { return enc.id == id; });

    if (it != config_.encoders.end()) {
      *it = newConfig;
      NotifyConfigChange("encoders", id);
      return true;
    }
    return false;
  }

  bool ConfigManager::UpdateStreamConfig(const std::string& id, const StreamConfigEx& newConfig)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = std::find_if(config_.streams.begin(), config_.streams.end(),
      [&id](const StreamConfigEx& stream) { return stream.id == id; });

    if (it != config_.streams.end()) {
      *it = newConfig;
      NotifyConfigChange("streams", id);
      return true;
    }
    return false;
  }

  void ConfigManager::SetDefaultConfig()
  {
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = CompleteConfig{};
    config_.global.logLevel = "info";
    config_.global.logFile = "logs/stream_capture.log";
    config_.global.enableConsoleLog = true;

    config_.capture.enabled = true;
    config_.capture.targetProcess = "game.exe";
    config_.capture.captureMethod = "hook";
    config_.capture.bufferSize = 10;
  }

  void ConfigManager::SetConfigChangeCallback(ConfigChangeCallback callback)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    changeCallback_ = callback;
  }

  void ConfigManager::NotifyConfigChange(const std::string& section, const std::string& key)
  {
    if (changeCallback_) changeCallback_(section, key);
  }

} // namespace StreamCapture