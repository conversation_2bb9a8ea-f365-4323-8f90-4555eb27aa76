#include "ConfigManager.h"
#include <fstream>
#include <sstream>
#include <algorithm>

// 简单的 JSON 解析器（生产环境建议使用 nlohmann/json 或其他成熟库）
#include <regex>

namespace StreamCapture {

ConfigManager::ConfigManager() {
    SetDefaultConfig();
}

ConfigManager::~ConfigManager() = default;

bool ConfigManager::LoadFromFile(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        return false;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    file.close();
    
    return LoadFromString(buffer.str());
}

bool ConfigManager::SaveToFile(const std::string& filePath) const {
    std::ofstream file(filePath);
    if (!file.is_open()) {
        return false;
    }
    
    std::string jsonString = SaveToString();
    file << jsonString;
    file.close();
    
    return true;
}

bool ConfigManager::LoadFromString(const std::string& jsonString) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 简单的 JSON 解析（生产环境应使用专业的 JSON 库）
    try {
        // 这里应该使用真正的 JSON 解析器
        // 为了演示，我们使用简化的解析逻辑
        
        // 解析全局配置
        if (jsonString.find("\"global\"") != std::string::npos) {
            // 解析全局配置部分
            ParseGlobalConfigFromString(jsonString);
        }
        
        // 解析编码器配置
        if (jsonString.find("\"encoders\"") != std::string::npos) {
            ParseEncoderConfigsFromString(jsonString);
        }
        
        // 解析流配置
        if (jsonString.find("\"streams\"") != std::string::npos) {
            ParseStreamConfigsFromString(jsonString);
        }
        
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

std::string ConfigManager::SaveToString() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::stringstream json;
    json << "{\n";
    
    // 全局配置
    json << "  \"global\": {\n";
    json << "    \"logLevel\": \"" << config_.global.logLevel << "\",\n";
    json << "    \"logFile\": \"" << config_.global.logFile << "\",\n";
    json << "    \"maxLogSize\": " << config_.global.maxLogSize << ",\n";
    json << "    \"enableConsoleLog\": " << (config_.global.enableConsoleLog ? "true" : "false") << ",\n";
    json << "    \"threadPoolSize\": " << config_.global.threadPoolSize << "\n";
    json << "  },\n";
    
    // 采集配置
    json << "  \"capture\": {\n";
    json << "    \"enabled\": " << (config_.capture.enabled ? "true" : "false") << ",\n";
    json << "    \"targetProcess\": \"" << config_.capture.targetProcess << "\",\n";
    json << "    \"captureMethod\": \"" << config_.capture.captureMethod << "\",\n";
    json << "    \"frameRate\": " << config_.capture.frameRate << ",\n";
    json << "    \"bufferSize\": " << config_.capture.bufferSize << ",\n";
    json << "    \"enableGPUCapture\": " << (config_.capture.enableGPUCapture ? "true" : "false") << "\n";
    json << "  },\n";
    
    // 编码器配置
    json << "  \"encoders\": [\n";
    for (size_t i = 0; i < config_.encoders.size(); ++i) {
        const auto& encoder = config_.encoders[i];
        json << "    {\n";
        json << "      \"id\": \"" << encoder.id << "\",\n";
        json << "      \"enabled\": " << (encoder.enabled ? "true" : "false") << ",\n";
        json << "      \"type\": \"" << encoder.type << "\",\n";
        json << "      \"codec\": \"" << encoder.codec << "\",\n";
        json << "      \"width\": " << encoder.width << ",\n";
        json << "      \"height\": " << encoder.height << ",\n";
        json << "      \"fps\": " << encoder.fps << ",\n";
        json << "      \"bitrate\": " << encoder.bitrate << ",\n";
        json << "      \"preset\": \"" << encoder.preset << "\",\n";
        json << "      \"profile\": \"" << encoder.profile << "\",\n";
        json << "      \"keyframeInterval\": " << encoder.keyframeInterval << ",\n";
        json << "      \"enableBFrames\": " << (encoder.enableBFrames ? "true" : "false") << ",\n";
        json << "      \"bufferSize\": " << encoder.bufferSize << ",\n";
        json << "      \"hardwareType\": \"" << encoder.hardwareType << "\"\n";
        json << "    }";
        if (i < config_.encoders.size() - 1) json << ",";
        json << "\n";
    }
    json << "  ],\n";
    
    // 流配置
    json << "  \"streams\": [\n";
    for (size_t i = 0; i < config_.streams.size(); ++i) {
        const auto& stream = config_.streams[i];
        json << "    {\n";
        json << "      \"id\": \"" << stream.id << "\",\n";
        json << "      \"enabled\": " << (stream.enabled ? "true" : "false") << ",\n";
        json << "      \"encoderId\": \"" << stream.encoderId << "\",\n";
        json << "      \"type\": \"" << stream.type << "\",\n";
        json << "      \"url\": \"" << stream.url << "\",\n";
        json << "      \"reconnectInterval\": " << stream.reconnectInterval << ",\n";
        json << "      \"maxReconnectAttempts\": " << stream.maxReconnectAttempts << ",\n";
        json << "      \"enableAutoReconnect\": " << (stream.enableAutoReconnect ? "true" : "false") << ",\n";
        json << "      \"connectionTimeout\": " << stream.connectionTimeout << ",\n";
        json << "      \"sendTimeout\": " << stream.sendTimeout << "\n";
        json << "    }";
        if (i < config_.streams.size() - 1) json << ",";
        json << "\n";
    }
    json << "  ],\n";
    
    // 性能配置
    json << "  \"performance\": {\n";
    json << "    \"enableGPUScheduling\": " << (config_.performance.enableGPUScheduling ? "true" : "false") << ",\n";
    json << "    \"cpuThreads\": " << config_.performance.cpuThreads << ",\n";
    json << "    \"enableLowLatency\": " << (config_.performance.enableLowLatency ? "true" : "false") << ",\n";
    json << "    \"enableZeroCopy\": " << (config_.performance.enableZeroCopy ? "true" : "false") << ",\n";
    json << "    \"memoryPoolSize\": " << config_.performance.memoryPoolSize << ",\n";
    json << "    \"enableHardwareDecoding\": " << (config_.performance.enableHardwareDecoding ? "true" : "false") << "\n";
    json << "  },\n";
    
    // 监控配置
    json << "  \"monitoring\": {\n";
    json << "    \"enableStats\": " << (config_.monitoring.enableStats ? "true" : "false") << ",\n";
    json << "    \"statsInterval\": " << config_.monitoring.statsInterval << ",\n";
    json << "    \"enableHealthCheck\": " << (config_.monitoring.enableHealthCheck ? "true" : "false") << ",\n";
    json << "    \"healthCheckInterval\": " << config_.monitoring.healthCheckInterval << ",\n";
    json << "    \"enablePerformanceMetrics\": " << (config_.monitoring.enablePerformanceMetrics ? "true" : "false") << ",\n";
    json << "    \"metricsInterval\": " << config_.monitoring.metricsInterval << "\n";
    json << "  },\n";
    
    // 调试配置
    json << "  \"debug\": {\n";
    json << "    \"enableFrameDump\": " << (config_.debug.enableFrameDump ? "true" : "false") << ",\n";
    json << "    \"frameDumpPath\": \"" << config_.debug.frameDumpPath << "\",\n";
    json << "    \"maxDumpFrames\": " << config_.debug.maxDumpFrames << ",\n";
    json << "    \"enablePacketDump\": " << (config_.debug.enablePacketDump ? "true" : "false") << ",\n";
    json << "    \"packetDumpPath\": \"" << config_.debug.packetDumpPath << "\",\n";
    json << "    \"enableTimingLog\": " << (config_.debug.enableTimingLog ? "true" : "false") << "\n";
    json << "  }\n";
    
    json << "}";
    
    return json.str();
}

const EncoderConfigEx* ConfigManager::FindEncoderConfig(const std::string& id) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = std::find_if(config_.encoders.begin(), config_.encoders.end(),
                          [&id](const EncoderConfigEx& config) {
                              return config.id == id;
                          });
    
    return (it != config_.encoders.end()) ? &(*it) : nullptr;
}

const StreamConfigEx* ConfigManager::FindStreamConfig(const std::string& id) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = std::find_if(config_.streams.begin(), config_.streams.end(),
                          [&id](const StreamConfigEx& config) {
                              return config.id == id;
                          });
    
    return (it != config_.streams.end()) ? &(*it) : nullptr;
}

bool ConfigManager::ValidateConfig() const {
    std::vector<std::string> errors = GetValidationErrors();
    return errors.empty();
}

std::vector<std::string> ConfigManager::GetValidationErrors() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::string> errors;
    
    // 验证编码器配置
    for (const auto& encoder : config_.encoders) {
        ValidateEncoderConfig(encoder, errors);
    }
    
    // 验证流配置
    for (const auto& stream : config_.streams) {
        ValidateStreamConfig(stream, errors);
    }
    
    // 验证编码器和流的关联
    for (const auto& stream : config_.streams) {
        if (FindEncoderConfig(stream.encoderId) == nullptr) {
            errors.push_back("Stream '" + stream.id + "' references non-existent encoder '" + stream.encoderId + "'");
        }
    }
    
    return errors;
}

bool ConfigManager::UpdateEncoderConfig(const std::string& id, const EncoderConfigEx& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = std::find_if(config_.encoders.begin(), config_.encoders.end(),
                          [&id](const EncoderConfigEx& config) {
                              return config.id == id;
                          });
    
    if (it != config_.encoders.end()) {
        *it = config;
        NotifyConfigChange("encoders", id);
        return true;
    }
    
    return false;
}

bool ConfigManager::UpdateStreamConfig(const std::string& id, const StreamConfigEx& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = std::find_if(config_.streams.begin(), config_.streams.end(),
                          [&id](const StreamConfigEx& config) {
                              return config.id == id;
                          });
    
    if (it != config_.streams.end()) {
        *it = config;
        NotifyConfigChange("streams", id);
        return true;
    }
    
    return false;
}

void ConfigManager::SetDefaultConfig() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 设置默认全局配置
    config_.global = GlobalConfig{};
    
    // 设置默认采集配置
    config_.capture = CaptureConfigEx{};
    
    // 设置默认编码器配置
    config_.encoders.clear();
    EncoderConfigEx defaultEncoder;
    defaultEncoder.id = "default";
    defaultEncoder.enabled = true;
    config_.encoders.push_back(defaultEncoder);
    
    // 设置默认流配置
    config_.streams.clear();
    
    // 设置默认性能配置
    config_.performance = PerformanceConfig{};
    
    // 设置默认监控配置
    config_.monitoring = MonitoringConfig{};
    
    // 设置默认调试配置
    config_.debug = DebugConfig{};
}

void ConfigManager::SetConfigChangeCallback(ConfigChangeCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    changeCallback_ = callback;
}

// 简化的 JSON 解析辅助函数
void ConfigManager::ParseGlobalConfigFromString(const std::string& jsonString) {
    // 这里应该使用真正的 JSON 解析器
    // 为了演示，使用简单的字符串匹配
    
    std::regex logLevelRegex(R"("logLevel"\s*:\s*"([^"]*)")");
    std::smatch match;
    if (std::regex_search(jsonString, match, logLevelRegex)) {
        config_.global.logLevel = match[1].str();
    }
    
    std::regex threadPoolRegex(R"("threadPoolSize"\s*:\s*(\d+))");
    if (std::regex_search(jsonString, match, threadPoolRegex)) {
        config_.global.threadPoolSize = std::stoi(match[1].str());
    }
}

void ConfigManager::ParseEncoderConfigsFromString(const std::string& jsonString) {
    // 简化的编码器配置解析
    // 实际实现应该使用专业的 JSON 解析器
    config_.encoders.clear();
    
    // 添加默认编码器配置
    EncoderConfigEx encoder;
    encoder.id = "main_stream";
    encoder.enabled = true;
    encoder.type = "hardware";
    encoder.codec = "h264";
    encoder.width = 1920;
    encoder.height = 1080;
    encoder.fps = 30;
    encoder.bitrate = 2000;
    encoder.preset = "quality";
    encoder.profile = "main";
    encoder.keyframeInterval = 60;
    encoder.enableBFrames = true;
    encoder.hardwareType = "nvenc";
    
    config_.encoders.push_back(encoder);
}

void ConfigManager::ParseStreamConfigsFromString(const std::string& jsonString) {
    // 简化的流配置解析
    config_.streams.clear();
    
    // 添加默认流配置
    StreamConfigEx stream;
    stream.id = "rtmp_main";
    stream.enabled = true;
    stream.encoderId = "main_stream";
    stream.type = "rtmp";
    stream.url = "rtmp://localhost:1935/live/stream1";
    stream.reconnectInterval = 5000;
    stream.maxReconnectAttempts = 10;
    stream.enableAutoReconnect = true;
    stream.connectionTimeout = 10000;
    stream.sendTimeout = 5000;
    
    config_.streams.push_back(stream);
}

bool ConfigManager::ValidateEncoderConfig(const EncoderConfigEx& config, std::vector<std::string>& errors) const {
    bool valid = true;
    
    if (config.id.empty()) {
        errors.push_back("Encoder ID cannot be empty");
        valid = false;
    }
    
    if (config.width <= 0 || config.height <= 0) {
        errors.push_back("Encoder '" + config.id + "': Invalid resolution");
        valid = false;
    }
    
    if (config.fps <= 0 || config.fps > 120) {
        errors.push_back("Encoder '" + config.id + "': Invalid frame rate");
        valid = false;
    }
    
    if (config.bitrate <= 0) {
        errors.push_back("Encoder '" + config.id + "': Invalid bitrate");
        valid = false;
    }
    
    return valid;
}

bool ConfigManager::ValidateStreamConfig(const StreamConfigEx& config, std::vector<std::string>& errors) const {
    bool valid = true;
    
    if (config.id.empty()) {
        errors.push_back("Stream ID cannot be empty");
        valid = false;
    }
    
    if (config.encoderId.empty()) {
        errors.push_back("Stream '" + config.id + "': Encoder ID cannot be empty");
        valid = false;
    }
    
    if (config.url.empty()) {
        errors.push_back("Stream '" + config.id + "': URL cannot be empty");
        valid = false;
    }
    
    return valid;
}

void ConfigManager::NotifyConfigChange(const std::string& section, const std::string& key) {
    if (changeCallback_) {
        changeCallback_(section, key);
    }
}

} // namespace StreamCapture
