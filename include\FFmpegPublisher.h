#pragma once

#include "StreamPublisher.h"
#include "ConfigManager.h"
#include <memory>
#include <thread>
#include <atomic>
#include <queue>
#include <condition_variable>

// FFmpeg 前向声明
struct AVFormatContext;
struct AVStream;
struct AVCodecContext;
struct AVPacket;

namespace StreamCapture
{

    // FFmpeg RTMP 推流器
    class FFmpegRTMPPublisher : public RTMPPublisher
    {
    public:
        FFmpegRTMPPublisher();
        ~FFmpegRTMPPublisher() override;

        // StreamPublisher 接口实现
        bool Initialize(const Config &config) override;
        void Shutdown() override;
        bool Connect() override;
        void Disconnect() override;
        bool IsConnected() const override;
        bool SendPacket(const EncodedPacket &packet) override;
        bool SendMetadata(const std::string &metadata) override;
        Stats GetStats() const override;

        // 扩展配置支持
        bool InitializeEx(const StreamConfigEx &config);

        // RTMP 特定功能
        bool SetStreamMetadata(const std::map<std::string, std::string> &metadata);
        bool SendVideoHeader(const uint8_t *sps, size_t spsSize, const uint8_t *pps, size_t ppsSize);
        bool SendAudioHeader(const uint8_t *header, size_t headerSize);

        // 连接管理
        bool TestConnection();
        bool Reconnect();
        void SetReconnectCallback(std::function<void(bool)> callback);

    private:
        // FFmpeg 上下文
        AVFormatContext *formatCtx_;
        AVStream *videoStream_;
        AVCodecContext *codecCtx_;

        // 配置
        StreamConfigEx config_;
        bool initialized_;
        std::atomic<bool> connected_;

        // 统计
        mutable std::mutex statsMutex_;
        Stats stats_;

        // 发送队列和线程
        std::thread sendingThread_;
        std::atomic<bool> running_;
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::mutex queueMutex_;
        std::condition_variable queueCondition_;
        size_t maxQueueSize_;

        // 重连管理
        std::thread reconnectThread_;
        std::atomic<bool> shouldReconnect_;
        std::function<void(bool)> reconnectCallback_;
        int reconnectAttempts_;

        // 时间戳管理
        int64_t startTime_;
        int64_t lastDts_;
        int64_t lastPts_;

        // 初始化辅助函数
        bool SetupOutputFormat();
        bool SetupVideoStream();
        bool SetupRTMPOptions();
        bool OpenOutput();

        // 发送辅助函数
        bool SendAVPacket(const EncodedPacket &packet);
        AVPacket *CreateAVPacket(const EncodedPacket &packet);
        bool WritePacketToStream(AVPacket *avPacket);

        // 线程函数
        void SendingThreadFunc();
        void ReconnectThreadFunc();

        // 时间戳处理
        int64_t ConvertTimestamp(uint64_t timestamp);
        void UpdateTimestamps(AVPacket *packet, const EncodedPacket &srcPacket);

        // 错误处理
        void HandleSendError(int errorCode);
        bool IsRecoverableError(int errorCode);

        // 清理函数
        void CleanupFFmpeg();
        void CleanupQueues();

        // 统计更新
        void UpdateStats(const EncodedPacket &packet, bool success);
    };

    // FFmpeg SRT 推流器
    class FFmpegSRTPublisher : public SRTPublisher
    {
    public:
        FFmpegSRTPublisher();
        ~FFmpegSRTPublisher() override;

        // StreamPublisher 接口实现
        bool Initialize(const Config &config) override;
        void Shutdown() override;
        bool Connect() override;
        void Disconnect() override;
        bool IsConnected() const override;
        bool SendPacket(const EncodedPacket &packet) override;
        bool SendMetadata(const std::string &metadata) override;
        Stats GetStats() const override;

        // 扩展配置支持
        bool InitializeEx(const StreamConfigEx &config);

        // SRT 特定功能
        bool SetLatency(int latency);
        bool SetMaxBandwidth(int bandwidth);
        bool GetConnectionStats(void *stats); // SRT_TRACEBSTATS*

    private:
        // SRT 上下文 (使用 void* 避免直接依赖 SRT 头文件)
        void *srtSocket_;

        // 配置
        StreamConfigEx config_;
        bool initialized_;
        std::atomic<bool> connected_;

        // 统计
        mutable std::mutex statsMutex_;
        Stats stats_;

        // 发送线程
        std::thread sendingThread_;
        std::atomic<bool> running_;
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::mutex queueMutex_;
        std::condition_variable queueCondition_;

        // SRT 辅助函数
        bool SetupSRTSocket();
        bool ConfigureSRTOptions();
        bool ConnectSRT();
        void DisconnectSRT();

        // 发送辅助函数
        bool SendSRTPacket(const EncodedPacket &packet);

        // 线程函数
        void SendingThreadFunc();

        // 清理函数
        void CleanupSRT();
    };

    // FFmpeg 文件推流器
    class FFmpegFilePublisher : public FilePublisher
    {
    public:
        FFmpegFilePublisher();
        ~FFmpegFilePublisher() override;

        // StreamPublisher 接口实现
        bool Initialize(const Config &config) override;
        void Shutdown() override;
        bool Connect() override;
        void Disconnect() override;
        bool IsConnected() const override;
        bool SendPacket(const EncodedPacket &packet) override;
        bool SendMetadata(const std::string &metadata) override;
        Stats GetStats() const override;

        // 扩展配置支持
        bool InitializeEx(const StreamConfigEx &config);

        // 文件特定功能
        bool StartNewSegment();
        bool SetSegmentDuration(int duration);
        std::string GetCurrentFileName() const;
        std::vector<std::string> GetCompletedFiles() const;

    private:
        // FFmpeg 上下文
        AVFormatContext *formatCtx_;
        AVStream *videoStream_;

        // 配置
        StreamConfigEx config_;
        bool initialized_;
        std::atomic<bool> recording_;

        // 文件管理
        std::string currentFileName_;
        std::vector<std::string> completedFiles_;
        int64_t segmentStartTime_;
        int64_t currentFileSize_;
        int segmentIndex_;

        // 统计
        mutable std::mutex statsMutex_;
        Stats stats_;

        // 写入线程
        std::thread writingThread_;
        std::atomic<bool> running_;
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::mutex queueMutex_;
        std::condition_variable queueCondition_;

        // 时间戳管理
        int64_t startTime_;
        int64_t lastTimestamp_;

        // 文件辅助函数
        std::string GenerateFileName();
        bool OpenNewFile();
        bool CloseCurrentFile();
        bool ShouldStartNewSegment(const EncodedPacket &packet);

        // 写入辅助函数
        bool WriteAVPacket(const EncodedPacket &packet);
        AVPacket *CreateAVPacket(const EncodedPacket &packet);

        // 线程函数
        void WritingThreadFunc();

        // 时间戳处理
        int64_t ConvertTimestamp(uint64_t timestamp);

        // 清理函数
        void CleanupFFmpeg();
        void CleanupFiles();

        // 统计更新
        void UpdateStats(const EncodedPacket &packet);
    };

    // FFmpeg UDP 推流器
    class FFmpegUDPPublisher : public StreamPublisher
    {
    public:
        FFmpegUDPPublisher();
        ~FFmpegUDPPublisher() override;

        // StreamPublisher 接口实现
        bool Initialize(const Config &config) override;
        void Shutdown() override;
        bool Connect() override;
        void Disconnect() override;
        bool IsConnected() const override;
        bool SendPacket(const EncodedPacket &packet) override;
        bool SendMetadata(const std::string &metadata) override;
        StreamState GetState() const override { return state_; }
        std::string GetPublisherName() const override { return "FFmpeg UDP"; }
        Stats GetStats() const override;

        // 扩展配置支持
        bool InitializeEx(const StreamConfigEx &config);

        // UDP 特定功能
        bool SetMTU(int mtu);
        bool SetBufferSize(int bufferSize);

    private:
        // FFmpeg 上下文
        AVFormatContext *formatCtx_;
        AVStream *videoStream_;

        // 配置
        StreamConfigEx config_;
        StreamState state_;
        bool initialized_;

        // 统计
        mutable std::mutex statsMutex_;
        Stats stats_;

        // 发送线程
        std::thread sendingThread_;
        std::atomic<bool> running_;
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::mutex queueMutex_;
        std::condition_variable queueCondition_;

        // UDP 辅助函数
        bool SetupUDPOutput();
        bool ConfigureUDPOptions();

        // 发送辅助函数
        bool SendUDPPacket(const EncodedPacket &packet);

        // 线程函数
        void SendingThreadFunc();

        // 清理函数
        void CleanupFFmpeg();
    };

    // FFmpeg 推流器工厂
    class FFmpegPublisherFactory
    {
    public:
        static std::unique_ptr<StreamPublisher> CreatePublisher(const StreamConfigEx &config);
        static std::unique_ptr<StreamPublisher> CreateRTMPPublisher();
        static std::unique_ptr<StreamPublisher> CreateSRTPublisher();
        static std::unique_ptr<StreamPublisher> CreateFilePublisher();
        static std::unique_ptr<StreamPublisher> CreateUDPPublisher();

        // 能力检测
        static bool IsRTMPSupported();
        static bool IsSRTSupported();
        static bool IsFileFormatSupported(const std::string &format);
        static std::vector<std::string> GetSupportedFormats();

    private:
        static bool CheckFFmpegSupport();
        static bool CheckSRTSupport();
    };

} // namespace StreamCapture
