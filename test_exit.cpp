#include <iostream>
#include <chrono>
#include <thread>
#include <csignal>
#include <atomic>

std::atomic<bool> running(true);

void signalHandler(int signal) {
    std::cout << "Received signal " << signal << ", exiting..." << std::endl;
    running = false;
}

int main() {
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);
    
    std::cout << "Test program running. Press Ctrl+C to exit." << std::endl;
    
    int count = 0;
    while (running && count < 30) {
        std::cout << "Running... " << count++ << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    std::cout << "Test program exiting normally." << std::endl;
    return 0;
}
