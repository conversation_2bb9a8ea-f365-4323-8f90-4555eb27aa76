#include "HookCore.h"
#include <d3d11.h>
#include <dxgi.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <chrono>
#include <sstream>
#include <iomanip>

#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "dxgi.lib")

namespace StreamCapture {

// D3D11Hook静态成员
D3D11Hook* D3D11Hook::instance_ = nullptr;

D3D11Hook::D3D11Hook() 
    : state_(HookState::Inactive)
    , isHooked_(false)
    , captureEnabled_(false)
    , originalPresent_(nullptr)
    , originalResizeBuffers_(nullptr)
    , d3dDevice_(nullptr)
    , d3dContext_(nullptr)
    , renderTargetView_(nullptr)
    , stagingTexture_(nullptr)
    , frameWidth_(0)
    , frameHeight_(0)
    , frameFormat_(DXGI_FORMAT_UNKNOWN)
    , frameIndex_(0)
    , compatibility_(Win7Compatibility::Instance())
{
    instance_ = this;
    
    // 初始化性能计数器
    QueryPerformanceFrequency(&perfFrequency_);
    QueryPerformanceCounter(&lastFrameTime_);
    
    memset(&stats_, 0, sizeof(stats_));
}

D3D11Hook::~D3D11Hook() {
    Shutdown();
    instance_ = nullptr;
}

bool D3D11Hook::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (state_ != HookState::Inactive) {
        return false;
    }
    
    // 初始化MinHook
    MH_STATUS status = MH_Initialize();
    if (status != MH_OK && status != MH_ERROR_ALREADY_INITIALIZED) {
        HandleError(ErrorCode::SystemError, "Failed to initialize MinHook");
        return false;
    }
    
    state_ = HookState::Inactive;
    return true;
}

void D3D11Hook::Shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    UninstallHooks();
    CleanupD3DResources();
    
    // 清理MinHook
    MH_Uninitialize();
    
    state_ = HookState::Inactive;
}

bool D3D11Hook::InstallHooks() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (isHooked_) {
        return true;
    }
    
    state_ = HookState::Injecting;
    
    try {
        // 创建临时D3D11设备获取函数地址
        ID3D11Device* tempDevice = nullptr;
        ID3D11DeviceContext* tempContext = nullptr;
        IDXGISwapChain* tempSwapChain = nullptr;
        
        // 创建临时窗口
        HWND tempWindow = CreateWindowExA(0, "STATIC", "TempWindow", 0, 
                                        0, 0, 1, 1, NULL, NULL, NULL, NULL);
        if (!tempWindow) {
            HandleError(ErrorCode::SystemError, "Failed to create temporary window");
            return false;
        }
        
        // 创建交换链描述
        DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
        swapChainDesc.BufferCount = 1;
        swapChainDesc.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
        swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
        swapChainDesc.OutputWindow = tempWindow;
        swapChainDesc.SampleDesc.Count = 1;
        swapChainDesc.Windowed = TRUE;
        
        // 创建设备和交换链
        HRESULT hr = D3D11CreateDeviceAndSwapChain(
            nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
            nullptr, 0, D3D11_SDK_VERSION,
            &swapChainDesc, &tempSwapChain, &tempDevice, nullptr, &tempContext);
        
        if (FAILED(hr)) {
            DestroyWindow(tempWindow);
            HandleError(ErrorCode::SystemError, "Failed to create temporary D3D11 device");
            return false;
        }
        
        // 获取虚函数表
        void** swapChainVTable = *reinterpret_cast<void***>(tempSwapChain);
        
        // Hook Present函数 (索引8)
        MH_STATUS status = MH_CreateHook(swapChainVTable[8], 
                                        reinterpret_cast<LPVOID>(HookedPresent),
                                        reinterpret_cast<LPVOID*>(&originalPresent_));
        if (status != MH_OK) {
            tempSwapChain->Release();
            tempContext->Release();
            tempDevice->Release();
            DestroyWindow(tempWindow);
            HandleError(ErrorCode::HookFailed, "Failed to create Present hook");
            return false;
        }
        
        // Hook ResizeBuffers函数 (索引13)
        status = MH_CreateHook(swapChainVTable[13],
                              reinterpret_cast<LPVOID>(HookedResizeBuffers),
                              reinterpret_cast<LPVOID*>(&originalResizeBuffers_));
        if (status != MH_OK) {
            MH_RemoveHook(swapChainVTable[8]);
            tempSwapChain->Release();
            tempContext->Release();
            tempDevice->Release();
            DestroyWindow(tempWindow);
            HandleError(ErrorCode::HookFailed, "Failed to create ResizeBuffers hook");
            return false;
        }
        
        // 启用Hook
        status = MH_EnableHook(MH_ALL_HOOKS);
        if (status != MH_OK) {
            MH_RemoveHook(swapChainVTable[8]);
            MH_RemoveHook(swapChainVTable[13]);
            tempSwapChain->Release();
            tempContext->Release();
            tempDevice->Release();
            DestroyWindow(tempWindow);
            HandleError(ErrorCode::HookFailed, "Failed to enable hooks");
            return false;
        }
        
        // 清理临时资源
        tempSwapChain->Release();
        tempContext->Release();
        tempDevice->Release();
        DestroyWindow(tempWindow);
        
        isHooked_ = true;
        state_ = HookState::Active;
        
        return true;
        
    } catch (const std::exception& e) {
        HandleError(ErrorCode::SystemError, std::string("Exception in InstallHooks: ") + e.what());
        return false;
    } catch (...) {
        HandleError(ErrorCode::SystemError, "Unknown exception in InstallHooks");
        return false;
    }
}

bool D3D11Hook::UninstallHooks() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!isHooked_) {
        return true;
    }
    
    // 禁用Hook
    MH_DisableHook(MH_ALL_HOOKS);
    
    // 移除Hook
    if (originalPresent_) {
        MH_RemoveHook(reinterpret_cast<LPVOID>(originalPresent_));
        originalPresent_ = nullptr;
    }
    
    if (originalResizeBuffers_) {
        MH_RemoveHook(reinterpret_cast<LPVOID>(originalResizeBuffers_));
        originalResizeBuffers_ = nullptr;
    }
    
    isHooked_ = false;
    state_ = HookState::Inactive;
    
    return true;
}

HRESULT STDMETHODCALLTYPE D3D11Hook::HookedPresent(IDXGISwapChain* pSwapChain, 
                                                   UINT SyncInterval, UINT Flags) {
    if (instance_) {
        return instance_->HandlePresent(pSwapChain, SyncInterval, Flags);
    }
    return E_FAIL;
}

HRESULT STDMETHODCALLTYPE D3D11Hook::HookedResizeBuffers(IDXGISwapChain* pSwapChain,
                                                         UINT BufferCount, UINT Width, 
                                                         UINT Height, DXGI_FORMAT NewFormat, 
                                                         UINT SwapChainFlags) {
    if (instance_) {
        return instance_->HandleResizeBuffers(pSwapChain, BufferCount, Width, Height, 
                                              NewFormat, SwapChainFlags);
    }
    return E_FAIL;
}

HRESULT D3D11Hook::HandlePresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags) {
    // 性能计时开始
    LARGE_INTEGER startTime;
    QueryPerformanceCounter(&startTime);
    
    // 调用原始函数
    HRESULT result = originalPresent_(pSwapChain, SyncInterval, Flags);
    
    // 如果启用捕获且Hook正常工作
    if (captureEnabled_ && state_ == HookState::Active) {
        try {
            if (CaptureFrame(pSwapChain)) {
                stats_.captureFrames++;
            } else {
                stats_.droppedFrames++;
            }
        } catch (...) {
            stats_.droppedFrames++;
            // 不影响原始Present调用
        }
    }
    
    // 更新统计信息
    frameIndex_++;
    UpdateStats();
    
    // 计算捕获时间
    LARGE_INTEGER endTime;
    QueryPerformanceCounter(&endTime);
    float captureTime = static_cast<float>(endTime.QuadPart - startTime.QuadPart) / perfFrequency_.QuadPart * 1000.0f;
    stats_.avgCaptureTime = (stats_.avgCaptureTime * 0.9f) + (captureTime * 0.1f);
    
    return result;
}

HRESULT D3D11Hook::HandleResizeBuffers(IDXGISwapChain* pSwapChain, UINT BufferCount, 
                                       UINT Width, UINT Height, DXGI_FORMAT NewFormat, 
                                       UINT SwapChainFlags) {
    // 清理旧资源
    CleanupD3DResources();
    
    // 调用原始函数
    HRESULT result = originalResizeBuffers_(pSwapChain, BufferCount, Width, Height, 
                                           NewFormat, SwapChainFlags);
    
    // 重新创建资源
    if (SUCCEEDED(result)) {
        frameWidth_ = Width;
        frameHeight_ = Height;
        frameFormat_ = NewFormat;
        CreateRenderTargetView(pSwapChain);
    }
    
    return result;
}

bool D3D11Hook::CaptureFrame(IDXGISwapChain* pSwapChain) {
    if (!frameCallback_) {
        return false;
    }
    
    try {
        // 获取后台缓冲区
        ID3D11Texture2D* backBuffer = nullptr;
        HRESULT hr = pSwapChain->GetBuffer(0, IID_PPV_ARGS(&backBuffer));
        if (FAILED(hr)) {
            ReportError("GetBuffer", hr);
            return false;
        }
        
        // 获取描述
        D3D11_TEXTURE2D_DESC backBufferDesc;
        backBuffer->GetDesc(&backBufferDesc);
        
        // 更新帧信息
        frameWidth_ = backBufferDesc.Width;
        frameHeight_ = backBufferDesc.Height;
        frameFormat_ = backBufferDesc.Format;
        
        // 创建或更新staging纹理
        if (!stagingTexture_ || 
            backBufferDesc.Width != frameWidth_ || 
            backBufferDesc.Height != frameHeight_) {
            
            if (stagingTexture_) {
                stagingTexture_->Release();
                stagingTexture_ = nullptr;
            }
            
            D3D11_TEXTURE2D_DESC stagingDesc = backBufferDesc;
            stagingDesc.Usage = D3D11_USAGE_STAGING;
            stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
            stagingDesc.BindFlags = 0;
            stagingDesc.MiscFlags = 0;
            
            hr = d3dDevice_->CreateTexture2D(&stagingDesc, nullptr, &stagingTexture_);
            if (FAILED(hr)) {
                backBuffer->Release();
                ReportError("CreateTexture2D", hr);
                return false;
            }
        }
        
        // 复制到staging纹理
        d3dContext_->CopyResource(stagingTexture_, backBuffer);
        backBuffer->Release();
        
        // 映射staging纹理
        D3D11_MAPPED_SUBRESOURCE mappedResource;
        hr = d3dContext_->Map(stagingTexture_, 0, D3D11_MAP_READ, D3D11_MAP_FLAG_DO_NOT_WAIT, &mappedResource);
        if (hr == DXGI_ERROR_WAS_STILL_DRAWING) {
            // GPU还在绘制，跳过这一帧
            return false;
        }
        if (FAILED(hr)) {
            ReportError("Map", hr);
            return false;
        }
        
        // 创建帧数据
        auto frameData = std::make_unique<FrameData>();
        frameData->width = frameWidth_;
        frameData->height = frameHeight_;
        frameData->format = frameFormat_;
        frameData->stride = mappedResource.RowPitch;
        frameData->timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        frameData->frameIndex = frameIndex_;
        
        // 计算数据大小
        size_t dataSize = mappedResource.RowPitch * frameHeight_;
        frameData->size = dataSize;
        frameData->data = new uint8_t[dataSize];
        
        // 复制数据
        memcpy(frameData->data, mappedResource.pData, dataSize);
        
        // 取消映射
        d3dContext_->Unmap(stagingTexture_, 0);
        
        // 调用回调
        frameCallback_(std::move(frameData));
        
        return true;
        
    } catch (const std::exception& e) {
        HandleError(ErrorCode::CaptureError, std::string("Exception in CaptureFrame: ") + e.what());
        return false;
    } catch (...) {
        HandleError(ErrorCode::CaptureError, "Unknown exception in CaptureFrame");
        return false;
    }
}

bool D3D11Hook::CreateRenderTargetView(IDXGISwapChain* pSwapChain) {
    // 获取设备
    HRESULT hr = pSwapChain->GetDevice(IID_PPV_ARGS(&d3dDevice_));
    if (FAILED(hr)) return false;
    
    d3dDevice_->GetImmediateContext(&d3dContext_);
    
    // 获取后台缓冲区
    ID3D11Texture2D* backBuffer = nullptr;
    hr = pSwapChain->GetBuffer(0, IID_PPV_ARGS(&backBuffer));
    if (FAILED(hr)) return false;
    
    // 创建渲染目标视图
    hr = d3dDevice_->CreateRenderTargetView(backBuffer, nullptr, &renderTargetView_);
    backBuffer->Release();
    
    return SUCCEEDED(hr);
}

void D3D11Hook::CleanupD3DResources() {
    if (renderTargetView_) {
        renderTargetView_->Release();
        renderTargetView_ = nullptr;
    }
    
    if (stagingTexture_) {
        stagingTexture_->Release();
        stagingTexture_ = nullptr;
    }
    
    if (d3dContext_) {
        d3dContext_->Release();
        d3dContext_ = nullptr;
    }
    
    if (d3dDevice_) {
        d3dDevice_->Release();
        d3dDevice_ = nullptr;
    }
}

void D3D11Hook::HandleError(ErrorCode code, const std::string& message) {
    if (errorCallback_) {
        ErrorInfo error;
        error.code = code;
        error.message = message;
        error.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        errorCallback_(error);
    }
}

void D3D11Hook::ReportError(const std::string& function, HRESULT hr) {
    std::ostringstream oss;
    oss << function << " failed with HRESULT 0x" << std::hex << hr;
    HandleError(ErrorCode::SystemError, oss.str());
}

void D3D11Hook::UpdateStats() {
    // 更新内存使用
    stats_.memoryUsageMB = compatibility_.GetAvailableMemory();
    
    // 简单的CPU使用率估算（实际实现中应该使用更精确的方法）
    static FILETIME lastCPU, lastSysCPU, lastUserCPU;
    static bool firstTime = true;
    
    if (firstTime) {
        SYSTEM_INFO sysInfo;
        FILETIME ftime, fsys, fuser;
        
        GetSystemInfo(&sysInfo);
        GetSystemTimeAsFileTime(&ftime);
        memcpy(&lastCPU, &ftime, sizeof(FILETIME));
        
        GetProcessTimes(GetCurrentProcess(), &ftime, &ftime, &fsys, &fuser);
        memcpy(&lastSysCPU, &fsys, sizeof(FILETIME));
        memcpy(&lastUserCPU, &fuser, sizeof(FILETIME));
        firstTime = false;
    }
}

// ProcessInjector实现
ProcessInjector::ProcessInjector() 
    : compatibility_(Win7Compatibility::Instance()) {
}

ProcessInjector::~ProcessInjector() {
}

bool ProcessInjector::InjectIntoProcess(const std::string& processName, const std::string& dllPath) {
    auto pids = FindProcessesByName(processName);
    if (pids.empty()) {
        HandleError(ErrorCode::ProcessNotFound, "Process not found: " + processName);
        return false;
    }
    
    // 选择第一个找到的进程
    return InjectIntoPID(pids[0], dllPath);
}

bool ProcessInjector::InjectIntoPID(DWORD processId, const std::string& dllPath) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
    if (!hProcess) {
        HandleError(ErrorCode::InjectionFailed, "Failed to open process", GetLastError());
        return false;
    }
    
    bool result = PerformInjection(hProcess, dllPath);
    CloseHandle(hProcess);
    
    return result;
}

bool ProcessInjector::PerformInjection(HANDLE hProcess, const std::string& dllPath) {
    // 等待进程稳定
    if (!WaitForProcessStability(hProcess)) {
        HandleError(ErrorCode::InjectionFailed, "Process not stable for injection");
        return false;
    }
    
    // 分配远程内存
    SIZE_T pathSize = dllPath.length() + 1;
    LPVOID remotePath = compatibility_.SafeVirtualAllocEx(hProcess, nullptr, pathSize, 
                                                          MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!remotePath) {
        HandleError(ErrorCode::InjectionFailed, "Failed to allocate remote memory", GetLastError());
        return false;
    }
    
    // 写入DLL路径
    SIZE_T bytesWritten = 0;
    if (!compatibility_.SafeWriteProcessMemory(hProcess, remotePath, dllPath.c_str(), 
                                               pathSize, &bytesWritten)) {
        VirtualFreeEx(hProcess, remotePath, 0, MEM_RELEASE);
        HandleError(ErrorCode::InjectionFailed, "Failed to write DLL path", GetLastError());
        return false;
    }
    
    // 获取LoadLibraryA地址
    HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
    LPVOID loadLibraryAddr = GetProcAddress(kernel32, "LoadLibraryA");
    if (!loadLibraryAddr) {
        VirtualFreeEx(hProcess, remotePath, 0, MEM_RELEASE);
        HandleError(ErrorCode::InjectionFailed, "Failed to get LoadLibraryA address");
        return false;
    }
    
    // 创建远程线程
    DWORD threadId = 0;
    HANDLE hThread = compatibility_.SafeCreateRemoteThread(hProcess, nullptr, 0,
                                                           (LPTHREAD_START_ROUTINE)loadLibraryAddr,
                                                           remotePath, 0, &threadId);
    if (!hThread) {
        VirtualFreeEx(hProcess, remotePath, 0, MEM_RELEASE);
        HandleError(ErrorCode::InjectionFailed, "Failed to create remote thread", GetLastError());
        return false;
    }
    
    // 等待线程完成
    DWORD waitResult = WaitForSingleObject(hThread, 5000); // 5秒超时
    if (waitResult != WAIT_OBJECT_0) {
        TerminateThread(hThread, 1);
        CloseHandle(hThread);
        VirtualFreeEx(hProcess, remotePath, 0, MEM_RELEASE);
        HandleError(ErrorCode::InjectionFailed, "Remote thread timeout or failed");
        return false;
    }
    
    // 检查线程退出码
    DWORD exitCode = 0;
    GetExitCodeThread(hThread, &exitCode);
    CloseHandle(hThread);
    VirtualFreeEx(hProcess, remotePath, 0, MEM_RELEASE);
    
    if (exitCode == 0) {
        HandleError(ErrorCode::InjectionFailed, "LoadLibrary failed in target process");
        return false;
    }
    
    return true;
}

std::vector<DWORD> ProcessInjector::FindProcessesByName(const std::string& processName) {
    std::vector<DWORD> pids;
    
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return pids;
    }
    
    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);
    
    if (Process32First(hSnapshot, &pe32)) {
        do {
            // Convert TCHAR* to std::string properly
            std::string currentProcessName;
            #ifdef UNICODE
            std::wstring wProcessName(pe32.szExeFile);
            currentProcessName.assign(wProcessName.begin(), wProcessName.end());
            #else
            currentProcessName = pe32.szExeFile;
            #endif
            
            if (_stricmp(currentProcessName.c_str(), processName.c_str()) == 0) {
                pids.push_back(pe32.th32ProcessID);
            }
        } while (Process32Next(hSnapshot, &pe32));
    }
    
    CloseHandle(hSnapshot);
    return pids;
}

bool ProcessInjector::WaitForProcessStability(HANDLE hProcess) {
    // 简单的稳定性检查 - 等待一段时间确保进程完全加载
    for (int i = 0; i < 10; ++i) {
        DWORD exitCode;
        if (!GetExitCodeProcess(hProcess, &exitCode) || exitCode != STILL_ACTIVE) {
            return false;
        }
        Sleep(100);
    }
    return true;
}

void ProcessInjector::HandleError(ErrorCode code, const std::string& message, DWORD lastError) {
    if (errorCallback_) {
        ErrorInfo error;
        error.code = code;
        error.message = message;
        if (lastError != 0) {
            error.details = compatibility_.GetLastErrorString();
        }
        error.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        errorCallback_(error);
    }
}

// HookCore实现
HookCore& HookCore::Instance() {
    static HookCore instance;
    return instance;
}

bool HookCore::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 初始化兼容性模块
    if (!Win7Compatibility::Instance().Initialize()) {
        return false;
    }
    
    // 创建Hook和注入器
    d3dHook_ = std::make_unique<D3D11Hook>();
    injector_ = std::make_unique<ProcessInjector>();
    
    if (!d3dHook_->Initialize()) {
        return false;
    }
    
    // 设置错误回调
    d3dHook_->SetErrorCallback([this](const ErrorInfo& error) {
        if (errorCallback_) {
            errorCallback_(error);
        }
    });
    
    injector_->SetErrorCallback([this](const ErrorInfo& error) {
        if (errorCallback_) {
            errorCallback_(error);
        }
    });
    
    state_ = HookState::Inactive;
    return true;
}

void HookCore::Shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    StopHooking();
    
    if (d3dHook_) {
        d3dHook_->Shutdown();
        d3dHook_.reset();
    }
    
    injector_.reset();
    
    state_ = HookState::Inactive;
}

bool HookCore::StartHooking(const TargetConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (state_ != HookState::Inactive) {
        return false;
    }
    
    currentTarget_ = config;
    UpdateState(HookState::Injecting);
    
    // 设置帧回调
    d3dHook_->SetFrameCallback(frameCallback_);
    
    // 安装Hook
    if (!d3dHook_->InstallHooks()) {
        UpdateState(HookState::Error);
        return false;
    }
    
    UpdateState(HookState::Active);
    return true;
}

void HookCore::StopHooking() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (d3dHook_) {
        d3dHook_->UninstallHooks();
    }
    
    UpdateState(HookState::Inactive);
}

void HookCore::UpdateState(HookState newState) {
    state_ = newState;
    if (stateCallback_) {
        stateCallback_(newState, "");
    }
}

bool HookCore::IsHooking() const {
    return state_ == HookState::Active;
}

HookState HookCore::GetState() const {
    return state_;
}

PerformanceStats HookCore::GetStats() const {
    if (d3dHook_) {
        return d3dHook_->GetStats();
    }
    return {};
}

void HookCore::SetFrameCallback(FrameCallback callback) {
    frameCallback_ = callback;
    if (d3dHook_) {
        d3dHook_->SetFrameCallback(callback);
    }
}

void HookCore::SetErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

void HookCore::SetStateCallback(StateCallback callback) {
    stateCallback_ = callback;
}

void HookCore::EnableCapture(bool enable) {
    if (d3dHook_) {
        d3dHook_->EnableCapture(enable);
    }
}

bool HookCore::IsCaptureEnabled() const {
    if (d3dHook_) {
        return d3dHook_->IsCaptureEnabled();
    }
    return false;
}

} // namespace StreamCapture