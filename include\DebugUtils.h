#pragma once

#include "Common.h"
#include <string>
#include <memory>
#include <atomic>
#include <mutex>

namespace StreamCapture
{
    // 前向声明
    struct GlobalConfig;
    // 调试工具类
    class DebugUtils
    {
    public:
        static DebugUtils &Instance();

        // 初始化调试功能
        bool Initialize(const GlobalConfig &config);
        void Shutdown();

        // 帧保存功能
        bool SaveFrame(const FrameData &frame, const std::string &prefix = "frame");
        bool SaveFrameAsPNG(const uint8_t *data, int width, int height, int channels, const std::string &filename);

        // 调试日志
        void DebugLog(const std::string &message);
        void DebugLogF(const char *format, ...);

        // 编码调试
        void LogEncodingStart(const std::string &encoderId, int width, int height);
        void LogEncodingResult(const std::string &encoderId, bool success, size_t packetSize = 0);

        // 推流调试
        void LogStreamingStart(const std::string &streamId, const std::string &url);
        void LogStreamingResult(const std::string &streamId, bool success, const std::string &error = "");
        void LogPacketSent(const std::string &streamId, size_t packetSize, uint64_t timestamp);

        // 获取调试状态
        bool IsDebugEnabled() const { return debugEnabled_; }
        bool IsSaveFramesEnabled() const { return saveFrames_; }

    private:
        DebugUtils() = default;
        ~DebugUtils() = default;

        // 禁止拷贝
        DebugUtils(const DebugUtils &) = delete;
        DebugUtils &operator=(const DebugUtils &) = delete;

        // 内部方法
        std::string GenerateFrameFilename(const std::string &prefix);
        bool CreateDirectoryIfNotExists(const std::string &path);

        // 配置
        bool debugEnabled_ = false;
        bool saveFrames_ = false;
        std::string debugPath_;
        int maxFrames_ = 100;

        // 状态
        std::atomic<int> frameCounter_{0};
        std::mutex mutex_;
        bool initialized_ = false;
    };

// 便利宏
#define DEBUG_LOG(msg)                               \
    do                                               \
    {                                                \
        if (DebugUtils::Instance().IsDebugEnabled()) \
        {                                            \
            DebugUtils::Instance().DebugLog(msg);    \
        }                                            \
    } while (0)

#define DEBUG_LOG_F(fmt, ...)                                   \
    do                                                          \
    {                                                           \
        if (DebugUtils::Instance().IsDebugEnabled())            \
        {                                                       \
            DebugUtils::Instance().DebugLogF(fmt, __VA_ARGS__); \
        }                                                       \
    } while (0)

#define DEBUG_SAVE_FRAME(frame, prefix)                      \
    do                                                       \
    {                                                        \
        if (DebugUtils::Instance().IsSaveFramesEnabled())    \
        {                                                    \
            DebugUtils::Instance().SaveFrame(frame, prefix); \
        }                                                    \
    } while (0)

} // namespace StreamCapture
