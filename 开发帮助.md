# StreamCapture 多线程推流开发帮助

## 项目概述

StreamCapture 是一个基于 C++ 和 FFmpeg 的高性能多线程视频采集和推流系统，支持：

- **多通道采集**: 同时采集多个视频源
- **多编码器**: 硬件加速编码 (NVENC, QSV, AMF) + 软件编码备份
- **多协议推流**: RTMP、SRT、文件录制、UDP 推流
- **实时处理**: 低延迟编码和推流
- **自动重连**: 网络断线自动重连机制
- **配置驱动**: JSON 配置文件管理所有参数

## 核心特性

### 1. 多线程架构
- **采集线程**: DirectX Hook 采集游戏画面
- **编码线程**: 多路并行视频编码
- **推流线程**: 独立的网络推流处理
- **监控线程**: 实时统计和健康检查

### 2. 硬件加速支持
- **NVIDIA NVENC**: GeForce/Quadro 显卡硬件编码
- **Intel Quick Sync**: Intel 集成显卡硬件编码  
- **AMD AMF**: AMD 显卡硬件编码
- **软件编码**: x264/x265 软件编码备份

### 3. 多协议推流
- **RTMP**: 直播平台推流 (Twitch, YouTube, 斗鱼等)
- **SRT**: 低延迟专业推流协议
- **文件录制**: MP4/FLV 本地录制
- **UDP**: 局域网组播推流

## 快速开始

### 1. 环境准备

**必需依赖:**
- Visual Studio 2019/2022
- CMake 3.20+
- FFmpeg 开发库
- Windows 10/11

**可选依赖:**
- NVIDIA GPU (支持 NVENC)
- Intel GPU (支持 Quick Sync)
- AMD GPU (支持 AMF)

### 2. 编译项目

```bash
# 克隆项目
git clone <repository-url>
cd StreamCapture

# 创建构建目录
mkdir build_desk
cd build_desk

# 配置 CMake
cmake .. -G "Visual Studio 16 2019" -A x64

# 编译项目
cmake --build . --config Release

# 编译测试程序
cmake --build . --target StreamCaptureTest --config Release
```

### 3. 配置文件

编辑 `config/stream_config.json`:

```json
{
  "encoders": [
    {
      "id": "main_stream",
      "enabled": true,
      "type": "hardware",
      "codec": "h264",
      "width": 1920,
      "height": 1080,
      "fps": 30,
      "bitrate": 2000,
      "hardwareType": "nvenc"
    }
  ],
  "streams": [
    {
      "id": "rtmp_main", 
      "enabled": true,
      "encoderId": "main_stream",
      "type": "rtmp",
      "url": "rtmp://localhost:1935/live/stream1"
    }
  ]
}
```

### 4. 运行测试

```bash
# 启动 RTMP 服务器 (可选)
scripts\start_rtmp_server.bat

# 运行完整测试
scripts\run_stream_test.bat

# 或直接运行测试程序
redist_desk\StreamCaptureTest.exe
```

## API 使用示例

### 1. 基本编码器使用

```cpp
#include "FFmpegEncoder.h"

// 创建编码器
auto encoder = std::make_unique<FFmpegEncoder>();

// 配置编码器
EncoderConfigEx config;
config.id = "test_encoder";
config.type = "hardware";
config.codec = "h264";
config.width = 1920;
config.height = 1080;
config.fps = 30;
config.bitrate = 2000;
config.hardwareType = "nvenc";

// 初始化
if (!encoder->InitializeEx(config)) {
    // 处理初始化失败
    return false;
}

// 编码帧
FrameData frame;
// ... 填充帧数据
encoder->EncodeFrame(frame);

// 获取编码结果
auto packet = encoder->GetEncodedPacket(100); // 100ms 超时
if (packet) {
    // 处理编码后的数据包
    ProcessEncodedPacket(*packet);
}

// 清理
encoder->Shutdown();
```

### 2. RTMP 推流器使用

```cpp
#include "FFmpegPublisher.h"

// 创建推流器
auto publisher = std::make_unique<FFmpegRTMPPublisher>();

// 配置推流器
StreamConfigEx config;
config.id = "rtmp_stream";
config.type = "rtmp";
config.url = "rtmp://live.twitch.tv/live/YOUR_STREAM_KEY";
config.reconnectInterval = 5000;
config.maxReconnectAttempts = 10;
config.enableAutoReconnect = true;

// 初始化并连接
if (publisher->InitializeEx(config) && publisher->Connect()) {
    // 发送数据包
    EncodedPacket packet;
    // ... 填充数据包
    publisher->SendPacket(packet);
    
    // 检查连接状态
    if (!publisher->IsConnected()) {
        // 处理连接断开
    }
}

// 断开连接
publisher->Disconnect();
publisher->Shutdown();
```

### 3. 配置管理器使用

```cpp
#include "ConfigManager.h"

// 创建配置管理器
auto configManager = std::make_unique<ConfigManager>();

// 加载配置文件
if (!configManager->LoadFromFile("config/stream_config.json")) {
    // 使用默认配置
    configManager->SetDefaultConfig();
}

// 验证配置
if (!configManager->ValidateConfig()) {
    auto errors = configManager->GetValidationErrors();
    for (const auto& error : errors) {
        std::cerr << "Config error: " << error << std::endl;
    }
}

// 获取编码器配置
const auto& encoderConfigs = configManager->GetEncoderConfigs();
for (const auto& config : encoderConfigs) {
    if (config.enabled) {
        // 创建编码器
        CreateEncoder(config);
    }
}

// 动态更新配置
EncoderConfigEx newConfig = *configManager->FindEncoderConfig("main_stream");
newConfig.bitrate = 3000; // 提高比特率
configManager->UpdateEncoderConfig("main_stream", newConfig);
```

## 高级功能

### 1. 多编码器管理

```cpp
#include "FFmpegEncoder.h"

// 创建多编码器管理器
auto multiEncoder = std::make_unique<FFmpegMultiEncoder>();

// 配置多个编码流
std::vector<EncoderConfigEx> configs = {
    {.id = "high_quality", .bitrate = 3000, .width = 1920, .height = 1080},
    {.id = "low_quality", .bitrate = 1000, .width = 1280, .height = 720},
    {.id = "mobile", .bitrate = 500, .width = 854, .height = 480}
};

// 初始化
if (multiEncoder->InitializeEx(configs)) {
    // 编码到所有流
    FrameData frame;
    multiEncoder->EncodeFrame(frame);
    
    // 获取特定流的数据包
    auto packet = multiEncoder->GetEncodedPacket("high_quality", 100);
    
    // 获取所有流的数据包
    auto allPackets = multiEncoder->GetAllEncodedPackets(100);
    for (auto& [streamId, packet] : allPackets) {
        // 处理每个流的数据包
        ProcessStreamPacket(streamId, *packet);
    }
}
```

### 2. 自定义帧处理

```cpp
#include "FrameProcessor.h"

// 创建帧处理器
auto processor = std::make_unique<FrameProcessor>();

FrameProcessor::Config config;
config.bufferSize = 20;
config.enableResize = true;
config.targetWidth = 1920;
config.targetHeight = 1080;

processor->Initialize(config);

// 处理帧
FrameData inputFrame;
// ... 从 DirectX Hook 获取帧数据

auto processedFrame = processor->ProcessFrame(inputFrame);
if (processedFrame) {
    // 发送到编码器
    encoder->EncodeFrame(*processedFrame);
}
```

### 3. 错误处理和监控

```cpp
// 设置错误回调
publisher->SetReconnectCallback([](bool success) {
    if (success) {
        std::cout << "Reconnected successfully" << std::endl;
    } else {
        std::cout << "Reconnection failed" << std::endl;
    }
});

// 监控统计信息
auto stats = publisher->GetStats();
std::cout << "Total packets: " << stats.totalPackets << std::endl;
std::cout << "Dropped packets: " << stats.droppedPackets << std::endl;
std::cout << "Average bitrate: " << stats.avgBitrate << " kbps" << std::endl;

// 健康检查
if (stats.droppedPackets > stats.totalPackets * 0.1) {
    // 丢包率超过 10%，可能需要降低比特率
    encoder->SetBitrate(encoder->GetConfig().bitrate * 0.8);
}
```

## 性能优化建议

### 1. 硬件编码优化
- 优先使用 NVENC 硬件编码
- 设置合适的预设 (speed/balanced/quality)
- 启用零拷贝模式减少内存拷贝

### 2. 网络优化
- 使用适当的缓冲区大小
- 启用自动重连机制
- 监控网络延迟和丢包

### 3. 内存优化
- 使用对象池减少内存分配
- 及时释放不需要的帧数据
- 控制队列大小防止内存泄漏

### 4. CPU 优化
- 合理分配编码线程数
- 使用硬件编码减少 CPU 负载
- 避免在关键路径上进行重量级操作

## 故障排除

### 常见问题

1. **编码器初始化失败**
   - 检查 FFmpeg 库是否正确安装
   - 验证硬件编码器驱动是否最新
   - 尝试使用软件编码作为备份

2. **推流连接失败**
   - 检查网络连接和防火墙设置
   - 验证推流 URL 和密钥是否正确
   - 检查服务器是否支持相应协议

3. **帧率不稳定**
   - 检查系统 CPU/GPU 负载
   - 调整编码参数和缓冲区大小
   - 优化帧采集频率

4. **内存泄漏**
   - 确保正确释放帧数据和数据包
   - 检查队列大小限制
   - 使用内存分析工具定位问题

### 调试技巧

1. **启用详细日志**
   ```cpp
   configManager->GetGlobalConfig().logLevel = "debug";
   ```

2. **监控关键指标**
   ```cpp
   // 定期检查统计信息
   auto stats = encoder->GetStats();
   if (stats.droppedFrames > 0) {
       // 分析丢帧原因
   }
   ```

3. **使用性能分析工具**
   - Visual Studio 诊断工具
   - Intel VTune Profiler
   - NVIDIA Nsight Graphics

## 扩展开发

### 添加新的编码器

1. 继承 `VideoEncoder` 基类
2. 实现所有虚函数
3. 在 `EncoderManager` 中注册新编码器
4. 更新配置文件格式

### 添加新的推流协议

1. 继承 `StreamPublisher` 基类
2. 实现协议特定的连接和发送逻辑
3. 在 `PublisherFactory` 中注册新协议
4. 添加相应的配置选项

### 性能监控扩展

1. 实现自定义统计收集器
2. 添加新的性能指标
3. 集成外部监控系统 (Prometheus, Grafana)
4. 实现告警机制

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 技术支持

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至技术支持
- 查看项目 Wiki 文档
