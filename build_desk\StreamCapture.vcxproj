﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A9AD216C-087E-35C1-A054-AC519EBC9F8B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>StreamCapture</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">StreamCapture.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">StreamCapture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">StreamCapture.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">StreamCapture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">StreamCapture.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">StreamCapture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">StreamCapture.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">StreamCapture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/Debug/StreamCapture.exe -installedDir C:/dev/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avutil.lib;psapi.lib;uuid.lib;oleaut32.lib;shlwapi.lib;gdi32.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;user32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\dl.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\minhook.x64d.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\avutil.lib;uuid.lib;oleaut32.lib;shlwapi.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\dl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/dev/vcpkg/installed/x64-windows/lib;C:/dev/vcpkg/installed/x64-windows/lib/$(Configuration);C:/dev/vcpkg/installed/x64-windows/debug/lib;C:/dev/vcpkg/installed/x64-windows/debug/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/Debug/StreamCapture.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/Debug/StreamCapture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/Release/StreamCapture.exe -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;psapi.lib;uuid.lib;oleaut32.lib;shlwapi.lib;gdi32.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;user32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\lib\dl.lib;C:\dev\vcpkg\installed\x64-windows\lib\minhook.x64.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;uuid.lib;oleaut32.lib;shlwapi.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\lib\dl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/dev/vcpkg/installed/x64-windows/lib;C:/dev/vcpkg/installed/x64-windows/lib/$(Configuration);C:/dev/vcpkg/installed/x64-windows/debug/lib;C:/dev/vcpkg/installed/x64-windows/debug/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/Release/StreamCapture.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/Release/StreamCapture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/MinSizeRel/StreamCapture.exe -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;psapi.lib;uuid.lib;oleaut32.lib;shlwapi.lib;gdi32.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;user32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\lib\dl.lib;C:\dev\vcpkg\installed\x64-windows\lib\minhook.x64.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;uuid.lib;oleaut32.lib;shlwapi.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\lib\dl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/dev/vcpkg/installed/x64-windows/lib;C:/dev/vcpkg/installed/x64-windows/lib/$(Configuration);C:/dev/vcpkg/installed/x64-windows/debug/lib;C:/dev/vcpkg/installed/x64-windows/debug/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/MinSizeRel/StreamCapture.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/MinSizeRel/StreamCapture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WINVER=0x0601;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/RelWithDebInfo/StreamCapture.exe -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;psapi.lib;uuid.lib;oleaut32.lib;shlwapi.lib;gdi32.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;user32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\lib\dl.lib;C:\dev\vcpkg\installed\x64-windows\lib\minhook.x64.lib;gdi32.lib;d3d11.lib;dxgi.lib;psapi.lib;C:\dev\vcpkg\installed\x64-windows\lib\avdevice.lib;C:\dev\vcpkg\installed\x64-windows\lib\avfilter.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;uuid.lib;oleaut32.lib;shlwapi.lib;vfw32.lib;secur32.lib;ws2_32.lib;mfuuid.lib;strmiids.lib;ole32.lib;bcrypt.lib;C:\dev\vcpkg\installed\x64-windows\lib\dl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/dev/vcpkg/installed/x64-windows/lib;C:/dev/vcpkg/installed/x64-windows/lib/$(Configuration);C:/dev/vcpkg/installed/x64-windows/debug/lib;C:/dev/vcpkg/installed/x64-windows/debug/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/RelWithDebInfo/StreamCapture.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/RelWithDebInfo/StreamCapture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/dev/Unity3D_project/SM-Record-main/StreamCapture/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/dev/Unity3D_project/SM-Record-main/StreamCapture -BF:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk --check-stamp-file F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\FindFFMPEG.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\config\capture.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/dev/Unity3D_project/SM-Record-main/StreamCapture/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/dev/Unity3D_project/SM-Record-main/StreamCapture -BF:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk --check-stamp-file F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\FindFFMPEG.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\config\capture.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/dev/Unity3D_project/SM-Record-main/StreamCapture/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/dev/Unity3D_project/SM-Record-main/StreamCapture -BF:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk --check-stamp-file F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\FindFFMPEG.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\config\capture.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/dev/Unity3D_project/SM-Record-main/StreamCapture/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/dev/Unity3D_project/SM-Record-main/StreamCapture -BF:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk --check-stamp-file F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\FindFFMPEG.cmake;C:\dev\vcpkg\installed\x64-windows\share\ffmpeg\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\minhook\minhook-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\config\capture.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\StreamCaptureController.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\HookCore.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\ConfigManager.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\utils\Win7Compatibility.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\capture\FrameProcessor.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\VideoEncoder.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\StreamPublisher.cpp" />
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\FFmpegPublisher.cpp" />
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\Common.h" />
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureController.h" />
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\HookCore.h" />
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\Win7Compatibility.h" />
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\FrameProcessor.h" />
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\VideoEncoder.h" />
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamPublisher.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\ZERO_CHECK.vcxproj">
      <Project>{9ABCB420-4C2C-3334-A9D6-C75BB2D814A3}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>