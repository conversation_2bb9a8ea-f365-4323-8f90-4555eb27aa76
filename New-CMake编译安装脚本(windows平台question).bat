::下面3个连续的命令是window平台有效的构建、编译、安装三件套，可以用来学习掌握
:: cmake -S . -B build_desk --fresh -G "Visual Studio 17 2022" -A x64 -DCMAKE_CONFIGURATION_TYPES:STRING="Debug;Release;MinSizeRel;RelWithDebInfo" -DCMAKE_TOOLCHAIN_FILE="C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake"
:: cmake --build .\build_desk --config Release -j8
:: cmake --install .\build_desk --prefix .\redist_desk


:: 这是windows平台批处理快速编译、安装github CMake项目的脚本
:: 已经可以正常执行
:: 20250126增加 "是否全部重新构建"的用户选项

@echo off
@chcp 65001
:: 更新源代码
git pull

:: 使用for循环获取当前所在文件夹的名称
for %%I in ("%CD%") do (
    set "folder_name=%%~nxI"
)

:: 设置编译路径
set configuration=Release
set build_dir="C:/VisualStudioRollback/CMakeBuild_Temp/build-%folder_name%-Desktop_Qt_6_7_0_MSVC2022_64bit-%configuration%"
echo %build_dir%

:: 询问用户是否全部重新构建
set /p rebuild="Do you want to rebuild from scratch? (yes/no): "
if /i "%rebuild%"=="yes" (
    echo Starting fresh build...
    set fresh_option=--fresh
) else (
if /i "%rebuild%"=="y" (
       echo Starting fresh build...
       set fresh_option=--fresh
   ) else (
       echo Reusing existing cache...
       set fresh_option=
   )
)

:: 执行 CMake 构建
echo starting...
cmake -S . -B %build_dir% %fresh_option% -G "Visual Studio 17 2022" -A x64 -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake -DCMAKE_CONFIGURATION_TYPES:STRING="Debug;Release;MinSizeRel;RelWithDebInfo" & cmake --build %build_dir% --config %configuration% & cmake --install %build_dir% --prefix "C:/local/3rdparty/%folder_name%-sdk"
echo end

@chcp 936
@echo on
