#include "DebugUtils.h"
#include "ConfigManager.h"
#include "StreamCaptureController.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <cstdarg>
#include <filesystem>

// 简单的PNG保存实现（使用Windows API）
#include <windows.h>
#include <gdiplus.h>
#pragma comment(lib, "gdiplus.lib")

namespace StreamCapture
{
    DebugUtils &DebugUtils::Instance()
    {
        static DebugUtils instance;
        return instance;
    }

    bool DebugUtils::Initialize(const GlobalConfig &config)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        debugEnabled_ = config.enableDebugMode;
        saveFrames_ = config.saveDebugFrames;
        debugPath_ = config.debugFramePath;
        maxFrames_ = config.maxDebugFrames;

        if (debugEnabled_)
        {
            std::cout << "[DEBUG] Debug mode enabled\n";
            std::cout << "[DEBUG] Save frames: " << (saveFrames_ ? "YES" : "NO") << "\n";
            std::cout << "[DEBUG] Debug path: " << debugPath_ << "\n";
            std::cout << "[DEBUG] Max frames: " << maxFrames_ << "\n";
        }

        if (saveFrames_)
        {
            if (!CreateDirectoryIfNotExists(debugPath_))
            {
                std::cerr << "[DEBUG] Failed to create debug directory: " << debugPath_ << "\n";
                return false;
            }
        }

        // 初始化GDI+
        Gdiplus::GdiplusStartupInput gdiplusStartupInput;
        ULONG_PTR gdiplusToken;
        Gdiplus::GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, NULL);

        initialized_ = true;
        return true;
    }

    void DebugUtils::Shutdown()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (debugEnabled_)
        {
            std::cout << "[DEBUG] Debug utils shutting down\n";
            std::cout << "[DEBUG] Total frames saved: " << frameCounter_.load() << "\n";
        }

        initialized_ = false;
    }

    bool DebugUtils::SaveFrame(const FrameData &frame, const std::string &prefix)
    {
        if (!saveFrames_ || !initialized_ || frameCounter_ >= maxFrames_)
        {
            return false;
        }

        std::string filename = GenerateFrameFilename(prefix);

        // 假设帧数据是RGBA格式
        bool success = SaveFrameAsPNG(frame.data, frame.width, frame.height, 4, filename);

        if (success)
        {
            frameCounter_++;
            DebugLogF("Saved frame %d: %s (%dx%d, %zu bytes)",
                      frameCounter_.load(), filename.c_str(),
                      frame.width, frame.height, frame.size);
        }
        else
        {
            DebugLogF("Failed to save frame: %s", filename.c_str());
        }

        return success;
    }

    bool DebugUtils::SaveFrameAsPNG(const uint8_t *data, int width, int height, int channels, const std::string &filename)
    {
        try
        {
            // 创建位图
            Gdiplus::Bitmap bitmap(width, height, PixelFormat32bppARGB);

            // 锁定位图数据
            Gdiplus::BitmapData bitmapData;
            Gdiplus::Rect rect(0, 0, width, height);
            bitmap.LockBits(&rect, Gdiplus::ImageLockModeWrite, PixelFormat32bppARGB, &bitmapData);

            // 复制数据
            uint8_t *destPtr = static_cast<uint8_t *>(bitmapData.Scan0);
            const uint8_t *srcPtr = data;

            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    // 转换RGBA到BGRA
                    destPtr[0] = srcPtr[2]; // B
                    destPtr[1] = srcPtr[1]; // G
                    destPtr[2] = srcPtr[0]; // R
                    destPtr[3] = srcPtr[3]; // A

                    destPtr += 4;
                    srcPtr += channels;
                }
            }

            bitmap.UnlockBits(&bitmapData);

            // 保存为PNG
            CLSID pngClsid;
            CLSIDFromString(L"{557CF406-1A04-11D3-9A73-0000F81EF32E}", &pngClsid);

            std::wstring wfilename(filename.begin(), filename.end());
            Gdiplus::Status status = bitmap.Save(wfilename.c_str(), &pngClsid, NULL);

            return status == Gdiplus::Ok;
        }
        catch (...)
        {
            return false;
        }
    }

    void DebugUtils::DebugLog(const std::string &message)
    {
        if (!debugEnabled_)
            return;

        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                      now.time_since_epoch()) %
                  1000;

        std::cout << "[DEBUG " << std::put_time(std::localtime(&time_t), "%H:%M:%S")
                  << "." << std::setfill('0') << std::setw(3) << ms.count()
                  << "] " << message << std::endl;
    }

    void DebugUtils::DebugLogF(const char *format, ...)
    {
        if (!debugEnabled_)
            return;

        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);

        DebugLog(std::string(buffer));
    }

    void DebugUtils::LogEncodingStart(const std::string &encoderId, int width, int height)
    {
        DebugLogF("ENCODING START: %s (%dx%d)", encoderId.c_str(), width, height);
    }

    void DebugUtils::LogEncodingResult(const std::string &encoderId, bool success, size_t packetSize)
    {
        if (success)
        {
            DebugLogF("ENCODING SUCCESS: %s (packet size: %zu bytes)", encoderId.c_str(), packetSize);
        }
        else
        {
            DebugLogF("ENCODING FAILED: %s", encoderId.c_str());
        }
    }

    void DebugUtils::LogStreamingStart(const std::string &streamId, const std::string &url)
    {
        DebugLogF("STREAMING START: %s -> %s", streamId.c_str(), url.c_str());
    }

    void DebugUtils::LogStreamingResult(const std::string &streamId, bool success, const std::string &error)
    {
        if (success)
        {
            DebugLogF("STREAMING SUCCESS: %s", streamId.c_str());
        }
        else
        {
            DebugLogF("STREAMING FAILED: %s - %s", streamId.c_str(), error.c_str());
        }
    }

    void DebugUtils::LogPacketSent(const std::string &streamId, size_t packetSize, uint64_t timestamp)
    {
        DebugLogF("PACKET SENT: %s (size: %zu, ts: %llu)", streamId.c_str(), packetSize, timestamp);
    }

    std::string DebugUtils::GenerateFrameFilename(const std::string &prefix)
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                      now.time_since_epoch()) %
                  1000;

        std::ostringstream oss;
        oss << debugPath_ << "/" << prefix << "_"
            << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
            << "_" << std::setfill('0') << std::setw(3) << ms.count()
            << "_" << std::setfill('0') << std::setw(4) << frameCounter_.load() + 1
            << ".png";

        return oss.str();
    }

    bool DebugUtils::CreateDirectoryIfNotExists(const std::string &path)
    {
        try
        {
            std::filesystem::create_directories(path);
            return true;
        }
        catch (...)
        {
            return false;
        }
    }

} // namespace StreamCapture
