{"Version": 1, "WorkspaceRootPath": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\build_desk\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\encoder\\VideoEncoder.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\include\\VideoEncoder.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\StreamCaptureController.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\HookCore.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 292, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "CMakeLists.txt", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\CMakeLists.txt", "RelativeDocumentMoniker": "..\\CMakeLists.txt", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\CMakeLists.txt", "RelativeToolTip": "..\\CMakeLists.txt", "ViewState": "AgIAAHMAAAAAAAAAAAAgwIkAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-08-02T15:36:56.702Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "VideoEncoder.cpp", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\encoder\\VideoEncoder.cpp", "RelativeDocumentMoniker": "..\\src\\encoder\\VideoEncoder.cpp", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\encoder\\VideoEncoder.cpp", "RelativeToolTip": "..\\src\\encoder\\VideoEncoder.cpp", "ViewState": "AgIAACEAAAAAAAAAAAAAADwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-02T15:35:44.475Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "StreamCaptureController.cpp", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\StreamCaptureController.cpp", "RelativeDocumentMoniker": "..\\src\\core\\StreamCaptureController.cpp", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\StreamCaptureController.cpp", "RelativeToolTip": "..\\src\\core\\StreamCaptureController.cpp", "ViewState": "AgIAAL4BAAAAAAAAAIAywMsBAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-02T15:32:13.86Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "VideoEncoder.h", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\include\\VideoEncoder.h", "RelativeDocumentMoniker": "..\\include\\VideoEncoder.h", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\include\\VideoEncoder.h", "RelativeToolTip": "..\\include\\VideoEncoder.h", "ViewState": "AgIAAEUBAAAAAAAAAAA1wCoBAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-02T15:32:08.068Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "HookCore.cpp", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\HookCore.cpp", "RelativeDocumentMoniker": "..\\src\\core\\HookCore.cpp", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\HookCore.cpp", "RelativeToolTip": "..\\src\\core\\HookCore.cpp", "ViewState": "AgIAAKYBAAAAAAAAAAAAALMBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-02T15:28:33.596Z", "EditorCaption": ""}]}]}]}