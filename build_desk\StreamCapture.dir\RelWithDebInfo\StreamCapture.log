﻿StreamCaptureController.obj : error LNK2019: 无法解析的外部符号 "public: __cdecl StreamCapture::MultiEncoder::MultiEncoder(void)" (??0MultiEncoder@StreamCapture@@QEAA@XZ)，函数 "class std::unique_ptr<class StreamCapture::MultiEncoder,struct std::default_delete<class StreamCapture::MultiEncoder> > __cdecl std::make_unique<class StreamCapture::MultiEncoder,0>(void)" (??$make_unique@VMultiEncoder@StreamCapture@@$$V$0A@@std@@YA?AV?$unique_ptr@VMultiEncoder@StreamCapture@@U?$default_delete@VMultiEncoder@StreamCapture@@@std@@@0@XZ) 中引用了该符号
StreamCaptureController.obj : error LNK2019: 无法解析的外部符号 "public: __cdecl StreamCapture::MultiEncoder::~MultiEncoder(void)" (??1MultiEncoder@StreamCapture@@QEAA@XZ)，函数 "public: __cdecl std::unique_ptr<struct StreamCapture::StreamCaptureController::TargetInstance,struct std::default_delete<struct StreamCapture::StreamCaptureController::TargetInstance> >::~unique_ptr<struct StreamCapture::StreamCaptureController::TargetInstance,struct std::default_delete<struct StreamCapture::StreamCaptureController::TargetInstance> >(void)" (??1?$unique_ptr@UTargetInstance@StreamCaptureController@StreamCapture@@U?$default_delete@UTargetInstance@StreamCaptureController@StreamCapture@@@std@@@std@@QEAA@XZ) 中引用了该符号
StreamCaptureController.obj : error LNK2019: 无法解析的外部符号 "public: __cdecl StreamCapture::MultiStreamController::MultiStreamController(void)" (??0MultiStreamController@StreamCapture@@QEAA@XZ)，函数 "class std::unique_ptr<class StreamCapture::MultiStreamController,struct std::default_delete<class StreamCapture::MultiStreamController> > __cdecl std::make_unique<class StreamCapture::MultiStreamController,0>(void)" (??$make_unique@VMultiStreamController@StreamCapture@@$$V$0A@@std@@YA?AV?$unique_ptr@VMultiStreamController@StreamCapture@@U?$default_delete@VMultiStreamController@StreamCapture@@@std@@@0@XZ) 中引用了该符号
StreamCaptureController.obj : error LNK2019: 无法解析的外部符号 "public: __cdecl StreamCapture::MultiStreamController::~MultiStreamController(void)" (??1MultiStreamController@StreamCapture@@QEAA@XZ)，函数 "public: __cdecl std::unique_ptr<struct StreamCapture::StreamCaptureController::TargetInstance,struct std::default_delete<struct StreamCapture::StreamCaptureController::TargetInstance> >::~unique_ptr<struct StreamCapture::StreamCaptureController::TargetInstance,struct std::default_delete<struct StreamCapture::StreamCaptureController::TargetInstance> >(void)" (??1?$unique_ptr@UTargetInstance@StreamCaptureController@StreamCapture@@U?$default_delete@UTargetInstance@StreamCaptureController@StreamCapture@@@std@@@std@@QEAA@XZ) 中引用了该符号
StreamCaptureController.obj : error LNK2019: 无法解析的外部符号 "public: bool __cdecl StreamCapture::MultiStreamController::HandleEncodedPacket(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &,class std::unique_ptr<struct StreamCapture::EncodedPacket,struct std::default_delete<struct StreamCapture::EncodedPacket> >)" (?HandleEncodedPacket@MultiStreamController@StreamCapture@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$unique_ptr@UEncodedPacket@StreamCapture@@U?$default_delete@UEncodedPacket@StreamCapture@@@std@@@4@@Z)，函数 "private: void __cdecl StreamCapture::StreamCaptureController::HandleEncodedPacket(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &,class std::unique_ptr<struct StreamCapture::EncodedPacket,struct std::default_delete<struct StreamCapture::EncodedPacket> >)" (?HandleEncodedPacket@StreamCaptureController@StreamCapture@@AEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0V?$unique_ptr@UEncodedPacket@StreamCapture@@U?$default_delete@UEncodedPacket@StreamCapture@@@std@@@4@@Z) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_dict_set，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_dict_free，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_frame_alloc，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_frame_free，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_frame_get_buffer，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_find_encoder_by_name，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_packet_alloc，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_packet_free，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 av_packet_unref，函数 "public: virtual bool __cdecl StreamCapture::FFmpegEncoder::EncodeFrame(struct StreamCapture::FrameData const &)" (?EncodeFrame@FFmpegEncoder@StreamCapture@@UEAA_NAEBUFrameData@2@@Z) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_alloc_context3，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_free_context，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_open2，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_send_frame，函数 "public: virtual bool __cdecl StreamCapture::FFmpegEncoder::EncodeFrame(struct StreamCapture::FrameData const &)" (?EncodeFrame@FFmpegEncoder@StreamCapture@@UEAA_NAEBUFrameData@2@@Z) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_receive_packet，函数 "public: virtual bool __cdecl StreamCapture::FFmpegEncoder::EncodeFrame(struct StreamCapture::FrameData const &)" (?EncodeFrame@FFmpegEncoder@StreamCapture@@UEAA_NAEBUFrameData@2@@Z) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 sws_freeContext，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 sws_getContext，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupFFmpeg(void)" (?SetupFFmpeg@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
VideoEncoder.obj : error LNK2019: 无法解析的外部符号 sws_scale，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::ConvertFrameFormat(struct StreamCapture::FrameData const &,struct AVFrame *)" (?ConvertFrameFormat@FFmpegEncoder@StreamCapture@@AEAA_NAEBUFrameData@2@PEAUAVFrame@@@Z) 中引用了该符号
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\RelWithDebInfo\StreamCapture.exe : fatal error LNK1120: 22 个无法解析的外部命令
