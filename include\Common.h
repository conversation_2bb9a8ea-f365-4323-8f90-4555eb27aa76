#pragma once

#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <mutex>
#include <atomic>

// Windows 7兼容性宏
#ifndef WINVER
#define WINVER 0x0601 // Windows 7
#endif

#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0601 // Windows 7
#endif

// 前向声明
class FrameBuffer;
class Logger;

namespace StreamCapture
{

    // GPU信息结构
    struct GPUInfo
    {
        std::string name;
        std::string vendor;
        uint32_t vendorId;
        uint32_t deviceId;
        size_t dedicatedMemoryMB;
        bool supportsNVENC;
        bool supportsQSV;
        bool supportsAMF;
        bool isPrimary;
    };

    // 系统兼容性信息
    struct SystemInfo
    {
        bool isWindows7;
        bool hasNVENC;
        bool hasQSV;
        bool hasAMF;
        std::string gpuName;       // 保留用于向后兼容
        std::vector<GPUInfo> gpus; // 所有GPU信息
        size_t totalMemoryMB;
        size_t availableMemoryMB;
    };

    // 捕获配置
    struct CaptureConfig
    {
        std::string processName;
        std::string windowTitle;
        std::string windowClass;
        uint32_t targetFPS = 30;
        bool skipFrames = false;
        std::string quality = "high"; // low, medium, high
    };

    // 编码配置
    struct EncoderConfig
    {
        std::string type = "hardware";  // hardware, software
        std::string codec = "h264";     // h264, h265
        uint32_t bitrate = 2000;        // kbps
        std::string preset = "quality"; // speed, balanced, quality
        std::string profile = "main";   // baseline, main, high
        uint32_t keyframeInterval = 60;
    };

    // 输出配置
    struct OutputConfig
    {
        std::string type; // rtmp, srt, file
        std::string url;  // URL或文件路径
        bool enabled = true;
    };

    // 目标配置
    struct TargetConfig
    {
        std::string name;
        bool enabled = true;
        CaptureConfig capture;
        EncoderConfig encoder;
        std::vector<OutputConfig> outputs;
    };

    // 帧数据
    struct FrameData
    {
        uint8_t *data = nullptr;
        size_t size = 0;
        uint32_t width = 0;
        uint32_t height = 0;
        uint32_t stride = 0;
        DXGI_FORMAT format = DXGI_FORMAT_R8G8B8A8_UNORM;
        uint64_t timestamp = 0;
        uint32_t frameIndex = 0;

        ~FrameData()
        {
            if (data)
            {
                delete[] data;
                data = nullptr;
            }
        }

        // 禁止拷贝，只允许移动
        FrameData(const FrameData &) = delete;
        FrameData &operator=(const FrameData &) = delete;

        FrameData(FrameData &&other) noexcept
            : data(other.data), size(other.size), width(other.width), height(other.height), stride(other.stride), format(other.format), timestamp(other.timestamp), frameIndex(other.frameIndex)
        {
            other.data = nullptr;
            other.size = 0;
        }

        FrameData &operator=(FrameData &&other) noexcept
        {
            if (this != &other)
            {
                if (data)
                    delete[] data;

                data = other.data;
                size = other.size;
                width = other.width;
                height = other.height;
                stride = other.stride;
                format = other.format;
                timestamp = other.timestamp;
                frameIndex = other.frameIndex;

                other.data = nullptr;
                other.size = 0;
            }
            return *this;
        }

        FrameData() = default;
    };

    // Hook状态
    enum class HookState
    {
        Inactive,
        Injecting,
        Active,
        Error,
        Recovering
    };

    // 编码状态
    enum class EncoderState
    {
        Idle,
        Initializing,
        Encoding,
        Error
    };

    // 推流状态
    enum class StreamState
    {
        Disconnected,
        Connecting,
        Connected,
        Error
    };

    // 性能统计
    struct PerformanceStats
    {
        uint32_t captureFrames = 0;
        uint32_t droppedFrames = 0;
        uint32_t encodedFrames = 0;
        uint32_t streamedFrames = 0;
        float avgCaptureTime = 0.0f;
        float avgEncodeTime = 0.0f;
        float avgStreamTime = 0.0f;
        size_t memoryUsageMB = 0;
        float cpuUsage = 0.0f;
        float gpuUsage = 0.0f;
    };

    // 错误代码
    enum class ErrorCode
    {
        Success = 0,
        ProcessNotFound,
        InjectionFailed,
        HookFailed,
        CaptureError,
        EncoderError,
        StreamError,
        OutOfMemory,
        InvalidConfig,
        SystemError
    };

    // 错误信息
    struct ErrorInfo
    {
        ErrorCode code;
        std::string message;
        std::string details;
        uint64_t timestamp;
    };

    // 流配置扩展
    struct StreamConfigEx
    {
        std::string id;
        bool enabled = true;
        std::string encoderId;
        std::string type; // "rtmp", "rtsp", "hls", "file"
        std::string url;

        // 连接设置
        uint32_t reconnectInterval = 5000; // ms
        uint32_t maxReconnectAttempts = 10;
        bool enableAutoReconnect = true;
        uint32_t connectionTimeout = 10000; // ms
        uint32_t sendTimeout = 5000;        // ms

        // 质量设置
        uint32_t bitrate = 2000;    // kbps
        uint32_t maxBitrate = 4000; // kbps
        uint32_t bufferSize = 1000; // ms

        // 元数据
        std::string title;
        std::string description;
        std::string author;
    };

    // 回调函数类型
    using FrameCallback = std::function<void(std::unique_ptr<FrameData>)>;
    using ErrorCallback = std::function<void(const ErrorInfo &)>;
    using StateCallback = std::function<void(HookState, const std::string &)>;

} // namespace StreamCapture