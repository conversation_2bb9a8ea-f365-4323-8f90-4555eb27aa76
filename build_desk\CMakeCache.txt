# This is the CMakeCache file.
# For build in directory: f:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build examples
BUILD_EXAMPLES:BOOL=OFF

//Build test suite
BUILD_TESTS:BOOL=OFF

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe

//No help, variable specified on the command line.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /GR /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files/StreamCapture

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=StreamCapture

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/Program Files (x86)/Embarcadero/Studio/21.0/bin/rc.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Enable profiling
ENABLE_PROFILING:BOOL=OFF

//Path to a library.
FFMPEG_DEPENDENCY_dl_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/dl.lib

//Path to a library.
FFMPEG_DEPENDENCY_dl_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/dl.lib

FFMPEG_FOUND:BOOL=TRUE

FFMPEG_INCLUDE_DIRS:STRING=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_LIBRARIES:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/avdevice.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avdevice.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avfilter.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avfilter.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avformat.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avformat.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avcodec.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avcodec.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/swresample.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/swresample.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/swscale.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/swscale.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avutil.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avutil.lib;optimized;psapi;optimized;uuid;optimized;oleaut32;optimized;shlwapi;optimized;gdi32;optimized;vfw32;optimized;secur32;optimized;ws2_32;optimized;mfuuid;optimized;strmiids;optimized;ole32;optimized;user32;optimized;bcrypt;optimized;C:/dev/vcpkg/installed/x64-windows/lib/dl.lib;debug;psapi;debug;uuid;debug;oleaut32;debug;shlwapi;debug;gdi32;debug;vfw32;debug;secur32;debug;ws2_32;debug;mfuuid;debug;strmiids;debug;ole32;debug;user32;debug;bcrypt;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/dl.lib

FFMPEG_LIBRARY_DIRS:STRING=C:/dev/vcpkg/installed/x64-windows/lib;C:/dev/vcpkg/installed/x64-windows/debug/lib

//Path to a file.
FFMPEG_libavcodec_INCLUDE_DIRS:PATH=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_libavcodec_LIBRARY:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/avcodec.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avcodec.lib

//Path to a library.
FFMPEG_libavcodec_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avcodec.lib

//Path to a library.
FFMPEG_libavcodec_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/avcodec.lib

FFMPEG_libavcodec_VERSION:STRING=61.19.101

//Path to a file.
FFMPEG_libavdevice_INCLUDE_DIRS:PATH=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_libavdevice_LIBRARY:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/avdevice.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avdevice.lib

//Path to a library.
FFMPEG_libavdevice_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avdevice.lib

//Path to a library.
FFMPEG_libavdevice_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/avdevice.lib

FFMPEG_libavdevice_VERSION:STRING=61.3.100

//Path to a file.
FFMPEG_libavfilter_INCLUDE_DIRS:PATH=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_libavfilter_LIBRARY:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/avfilter.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avfilter.lib

//Path to a library.
FFMPEG_libavfilter_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avfilter.lib

//Path to a library.
FFMPEG_libavfilter_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/avfilter.lib

FFMPEG_libavfilter_VERSION:STRING=10.4.100

//Path to a file.
FFMPEG_libavformat_INCLUDE_DIRS:PATH=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_libavformat_LIBRARY:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/avformat.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avformat.lib

//Path to a library.
FFMPEG_libavformat_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avformat.lib

//Path to a library.
FFMPEG_libavformat_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/avformat.lib

FFMPEG_libavformat_VERSION:STRING=61.7.100

//Path to a file.
FFMPEG_libavutil_INCLUDE_DIRS:PATH=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_libavutil_LIBRARY:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/avutil.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avutil.lib

//Path to a library.
FFMPEG_libavutil_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avutil.lib

//Path to a library.
FFMPEG_libavutil_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/avutil.lib

FFMPEG_libavutil_VERSION:STRING=59.39.100

//Path to a file.
FFMPEG_libswresample_INCLUDE_DIRS:PATH=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_libswresample_LIBRARY:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/swresample.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/swresample.lib

//Path to a library.
FFMPEG_libswresample_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/swresample.lib

//Path to a library.
FFMPEG_libswresample_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/swresample.lib

FFMPEG_libswresample_VERSION:STRING=5.3.100

//Path to a file.
FFMPEG_libswscale_INCLUDE_DIRS:PATH=C:/dev/vcpkg/installed/x64-windows/include

FFMPEG_libswscale_LIBRARY:STRING=optimized;C:/dev/vcpkg/installed/x64-windows/lib/swscale.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/swscale.lib

//Path to a library.
FFMPEG_libswscale_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/swscale.lib

//Path to a library.
FFMPEG_libswscale_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/swscale.lib

FFMPEG_libswscale_VERSION:STRING=8.3.100

PKG_CONFIG_EXECUTABLE:STRING=C:/dev/vcpkg/installed/x64-windows/share/ffmpeg/../../../x64-windows/tools/pkgconf/pkgconf.exe

//Value Computed by CMake
StreamCapture_BINARY_DIR:STATIC=F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk

//Value Computed by CMake
StreamCapture_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
StreamCapture_SOURCE_DIR:STATIC=F:/dev/Unity3D_project/SM-Record-main/StreamCapture

//Automatically copy dependencies into the output directory for
// executables.
VCPKG_APPLOCAL_DEPS:BOOL=ON

//The directory which contains the installed libraries for each
// triplet
VCPKG_INSTALLED_DIR:PATH=C:/dev/vcpkg/installed

//The path to the vcpkg manifest directory.
VCPKG_MANIFEST_DIR:PATH=

//Use manifest mode, as opposed to classic mode.
VCPKG_MANIFEST_MODE:BOOL=OFF

//Appends the vcpkg paths to CMAKE_PREFIX_PATH, CMAKE_LIBRARY_PATH
// and CMAKE_FIND_ROOT_PATH so that vcpkg libraries/packages are
// found after toolchain/system libraries/packages.
VCPKG_PREFER_SYSTEM_LIBS:BOOL=OFF

//Enable the setup of CMAKE_PROGRAM_PATH to vcpkg paths
VCPKG_SETUP_CMAKE_PROGRAM_PATH:BOOL=ON

//Vcpkg target triplet (ex. x86-windows)
VCPKG_TARGET_TRIPLET:STRING=x64-windows

//Trace calls to find_package()
VCPKG_TRACE_FIND_PACKAGE:BOOL=OFF

//Enables messages from the VCPKG toolchain for debugging purposes.
VCPKG_VERBOSE:BOOL=OFF

//(experimental) Automatically copy dependencies into the install
// target directory for executables. Requires CMake 3.14.
X_VCPKG_APPLOCAL_DEPS_INSTALL:BOOL=OFF

//(experimental) Add USES_TERMINAL to VCPKG_APPLOCAL_DEPS to force
// serialization.
X_VCPKG_APPLOCAL_DEPS_SERIALIZED:BOOL=OFF

//Path to a program.
Z_VCPKG_PWSH_PATH:FILEPATH=C:/Program Files/PowerShell/7/pwsh.exe

//The directory which contains the installed libraries for each
// triplet
_VCPKG_INSTALLED_DIR:PATH=C:/dev/vcpkg/installed

//The directory containing a CMake configuration file for minhook.
minhook_DIR:PATH=C:/dev/vcpkg/installed/x64-windows/share/minhook


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=f:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_desk
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=4
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Enterprise
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=x64
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=F:/dev/Unity3D_project/SM-Record-main/StreamCapture
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.26
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TOOLCHAIN_FILE
CMAKE_TOOLCHAIN_FILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavcodec_LIBRARY_DEBUG
FFMPEG_libavcodec_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavcodec_LIBRARY_RELEASE
FFMPEG_libavcodec_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavdevice_LIBRARY_DEBUG
FFMPEG_libavdevice_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavdevice_LIBRARY_RELEASE
FFMPEG_libavdevice_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavfilter_LIBRARY_DEBUG
FFMPEG_libavfilter_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavfilter_LIBRARY_RELEASE
FFMPEG_libavfilter_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavformat_LIBRARY_DEBUG
FFMPEG_libavformat_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavformat_LIBRARY_RELEASE
FFMPEG_libavformat_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavutil_LIBRARY_DEBUG
FFMPEG_libavutil_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libavutil_LIBRARY_RELEASE
FFMPEG_libavutil_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libswresample_LIBRARY_DEBUG
FFMPEG_libswresample_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libswresample_LIBRARY_RELEASE
FFMPEG_libswresample_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libswscale_LIBRARY_DEBUG
FFMPEG_libswscale_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFMPEG_libswscale_LIBRARY_RELEASE
FFMPEG_libswscale_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Details about finding FFMPEG
FIND_PACKAGE_MESSAGE_DETAILS_FFMPEG:INTERNAL=[optimized;C:/dev/vcpkg/installed/x64-windows/lib/avdevice.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avdevice.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avfilter.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avfilter.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avformat.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avformat.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avcodec.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avcodec.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/swresample.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/swresample.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/swscale.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/swscale.lib;optimized;C:/dev/vcpkg/installed/x64-windows/lib/avutil.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/avutil.lib;optimized;psapi;optimized;uuid;optimized;oleaut32;optimized;shlwapi;optimized;gdi32;optimized;vfw32;optimized;secur32;optimized;ws2_32;optimized;mfuuid;optimized;strmiids;optimized;ole32;optimized;user32;optimized;bcrypt;optimized;C:/dev/vcpkg/installed/x64-windows/lib/dl.lib;debug;psapi;debug;uuid;debug;oleaut32;debug;shlwapi;debug;gdi32;debug;vfw32;debug;secur32;debug;ws2_32;debug;mfuuid;debug;strmiids;debug;ole32;debug;user32;debug;bcrypt;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/dl.lib][C:/dev/vcpkg/installed/x64-windows/lib;C:/dev/vcpkg/installed/x64-windows/debug/lib][C:/dev/vcpkg/installed/x64-windows/include][v()]
//Install the dependencies listed in your manifest:
//\n    If this is off, you will have to manually install your dependencies.
//\n    See https://github.com/microsoft/vcpkg/tree/master/docs/specifications/manifests.md
// for more info.
//\n
VCPKG_MANIFEST_INSTALL:INTERNAL=OFF
//ADVANCED property for variable: VCPKG_VERBOSE
VCPKG_VERBOSE-ADVANCED:INTERNAL=1
//Making sure VCPKG_MANIFEST_MODE doesn't change
Z_VCPKG_CHECK_MANIFEST_MODE:INTERNAL=OFF
//The path to the PowerShell implementation to use.
Z_VCPKG_POWERSHELL_PATH:INTERNAL=C:/Program Files/PowerShell/7/pwsh.exe
//Vcpkg root directory
Z_VCPKG_ROOT_DIR:INTERNAL=C:/dev/vcpkg

