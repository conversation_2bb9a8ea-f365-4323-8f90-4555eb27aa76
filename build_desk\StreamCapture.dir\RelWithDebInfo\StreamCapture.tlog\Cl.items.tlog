F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\main.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\StreamCaptureController.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\StreamCaptureController.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\HookCore.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\HookCore.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\ConfigManager.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\ConfigManager.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\MediaServerManager.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\DebugUtils.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\DebugUtils.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\utils\Win7Compatibility.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\Win7Compatibility.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\capture\FrameProcessor.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\FrameProcessor.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\VideoEncoder.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\VideoEncoder.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\FFmpegEncoder.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\StreamPublisher.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\StreamPublisher.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\FFmpegPublisher.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\FFmpegPublisher.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\MultiStreamController.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\RelWithDebInfo\MultiStreamController.obj
