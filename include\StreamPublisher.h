#pragma once

#include "Common.h"
#include "VideoEncoder.h"
#include <thread>
#include <atomic>
#include <chrono>

// FFmpeg推流相关
extern "C"
{
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/time.h>
}

namespace StreamCapture
{

    // 推流器基类
    class StreamPublisher
    {
    public:
        struct Config
        {
            std::string url;
            std::string protocol = "rtmp"; // rtmp, srt, file
            uint32_t bufferSize = 3000;    // ms
            uint32_t reconnectAttempts = 5;
            uint32_t reconnectDelayMs = 2000;
            bool enableAuthentication = false;
            std::string username;
            std::string password;

            // 文件输出特定配置
            std::string fileFormat = "mp4"; // mp4, flv, mkv
            bool overwriteFile = true;
        };

        StreamPublisher() = default;
        virtual ~StreamPublisher() = default;

        // 生命周期
        virtual bool Initialize(const Config &config) = 0;
        virtual void Shutdown() = 0;

        // 推流控制
        virtual bool Connect() = 0;
        virtual void Disconnect() = 0;
        virtual bool IsConnected() const = 0;

        // 数据发送
        virtual bool SendPacket(const EncodedPacket &packet) = 0;
        virtual bool SendMetadata(const std::string &metadata) = 0;

        // 状态查询
        virtual StreamState GetState() const = 0;
        virtual std::string GetPublisherName() const = 0;

        // 统计
        struct Stats
        {
            uint64_t totalPackets = 0;
            uint64_t totalBytes = 0;
            uint64_t droppedPackets = 0;
            uint32_t reconnectCount = 0;
            float avgBitrate = 0.0f;
            uint32_t bufferUtilization = 0; // 百分比
            uint64_t lastPacketTime = 0;
        };

        virtual Stats GetStats() const = 0;

        // 回调
        using StateChangeCallback = std::function<void(StreamState oldState, StreamState newState)>;
        using ErrorCallback = std::function<void(const ErrorInfo &)>;

        virtual void SetStateChangeCallback(StateChangeCallback callback) { stateCallback_ = callback; }
        virtual void SetErrorCallback(ErrorCallback callback) { errorCallback_ = callback; }

    protected:
        void NotifyStateChange(StreamState oldState, StreamState newState)
        {
            if (stateCallback_)
            {
                stateCallback_(oldState, newState);
            }
        }

        void NotifyError(ErrorCode code, const std::string &message)
        {
            if (errorCallback_)
            {
                ErrorInfo error;
                error.code = code;
                error.message = message;
                error.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                                      std::chrono::system_clock::now().time_since_epoch())
                                      .count();
                errorCallback_(error);
            }
        }

        Config config_;
        Stats stats_;
        std::atomic<StreamState> state_;

        StateChangeCallback stateCallback_;
        ErrorCallback errorCallback_;
    };

    // RTMP推流器
    class RTMPPublisher : public StreamPublisher
    {
    public:
        RTMPPublisher();
        ~RTMPPublisher() override;

        bool Initialize(const Config &config) override;
        void Shutdown() override;

        bool Connect() override;
        void Disconnect() override;
        bool IsConnected() const override;

        bool SendPacket(const EncodedPacket &packet) override;
        bool SendMetadata(const std::string &metadata) override;

        StreamState GetState() const override { return state_; }
        std::string GetPublisherName() const override { return "RTMP"; }

        Stats GetStats() const override;

    private:
        bool SetupFFmpegContext();
        void CleanupFFmpegContext();
        bool WriteHeader();
        bool WriteTrailer();

        AVFormatContext *formatContext_;
        AVStream *videoStream_;
        AVCodecContext *codecContext_;

        bool headerWritten_;
        int64_t startTime_;
        int64_t frameIndex_;

        mutable std::mutex mutex_;
    };

    // 文件输出器
    class FilePublisher : public StreamPublisher
    {
    public:
        FilePublisher();
        ~FilePublisher() override;

        bool Initialize(const Config &config) override;
        void Shutdown() override;

        bool Connect() override;
        void Disconnect() override;
        bool IsConnected() const override;

        bool SendPacket(const EncodedPacket &packet) override;
        bool SendMetadata(const std::string &metadata) override;

        StreamState GetState() const override { return state_; }
        std::string GetPublisherName() const override { return "File"; }

        Stats GetStats() const override;

    private:
        bool SetupFileOutput();
        void CleanupFileOutput();
        std::string GenerateFileName() const;

        AVFormatContext *formatContext_;
        AVStream *videoStream_;

        std::string currentFileName_;
        bool headerWritten_;
        int64_t startTime_;
        int64_t frameIndex_;

        mutable std::mutex mutex_;
    };

    // SRT推流器（简化实现）
    class SRTPublisher : public StreamPublisher
    {
    public:
        SRTPublisher();
        ~SRTPublisher() override;

        bool Initialize(const Config &config) override;
        void Shutdown() override;

        bool Connect() override;
        void Disconnect() override;
        bool IsConnected() const override;

        bool SendPacket(const EncodedPacket &packet) override;
        bool SendMetadata(const std::string &metadata) override;

        StreamState GetState() const override { return state_; }
        std::string GetPublisherName() const override { return "SRT"; }

        Stats GetStats() const override;

    private:
        // SRT实现（需要SRT库）
        void *srtSocket_; // 实际应该是SRTSOCKET

        mutable std::mutex mutex_;
    };

    // 推流管理器
    class StreamManager
    {
    public:
        struct StreamConfig
        {
            std::string streamId;
            StreamPublisher::Config publisherConfig;
            bool enabled = true;
            bool autoReconnect = true;
        };

        StreamManager();
        ~StreamManager();

        // 生命周期
        bool Initialize();
        void Shutdown();

        // 流管理
        bool AddStream(const StreamConfig &config);
        bool RemoveStream(const std::string &streamId);
        bool EnableStream(const std::string &streamId, bool enable);

        // 推流控制
        bool SendPacket(const std::string &streamId, const EncodedPacket &packet);
        bool SendPacketToAllStreams(const EncodedPacket &packet);
        bool BroadcastPacket(const EncodedPacket &packet);

        // 批量流控制
        bool StartAllStreams();
        void StopAllStreams();
        bool IsAnyStreamActive() const;

        // 连接管理
        bool ConnectStream(const std::string &streamId);
        void DisconnectStream(const std::string &streamId);
        bool IsStreamConnected(const std::string &streamId) const;

        // 状态查询
        std::vector<std::string> GetStreamIds() const;
        StreamState GetStreamState(const std::string &streamId) const;
        StreamPublisher::Stats GetStreamStats(const std::string &streamId) const;

        // 全局统计
        struct GlobalStats
        {
            uint32_t totalStreams = 0;
            uint32_t connectedStreams = 0;
            uint64_t totalPackets = 0;
            uint64_t totalBytes = 0;
            uint64_t totalDropped = 0;
        };

        GlobalStats GetGlobalStats() const;

        // 回调
        using StreamStateCallback = std::function<void(const std::string &streamId, StreamState oldState, StreamState newState)>;
        using StreamErrorCallback = std::function<void(const std::string &streamId, const ErrorInfo &)>;

        void SetStreamStateCallback(StreamStateCallback callback) { streamStateCallback_ = callback; }
        void SetStreamErrorCallback(StreamErrorCallback callback) { streamErrorCallback_ = callback; }

    private:
        struct StreamInfo
        {
            std::string id;
            StreamConfig config;
            std::unique_ptr<StreamPublisher> publisher;
            std::thread reconnectThread;
            std::atomic<bool> shouldReconnect;
            std::atomic<bool> running;
        };

        std::unique_ptr<StreamPublisher> CreatePublisher(const StreamPublisher::Config &config);
        StreamInfo *FindStream(const std::string &streamId);
        const StreamInfo *FindStream(const std::string &streamId) const;
        void ReconnectThread(StreamInfo *streamInfo);
        void HandleStreamStateChange(const std::string &streamId, StreamState oldState, StreamState newState);
        void HandleStreamError(const std::string &streamId, const ErrorInfo &error);

        std::vector<std::unique_ptr<StreamInfo>> streams_;

        StreamStateCallback streamStateCallback_;
        StreamErrorCallback streamErrorCallback_;

        mutable std::mutex mutex_;
        std::atomic<bool> running_;
    };

    // 多路推流控制器
    class MultiStreamController
    {
    public:
        struct Config
        {
            std::vector<StreamManager::StreamConfig> streams;
            bool enableBroadcast = true; // 广播到所有流
            bool enableFailover = true;  // 故障转移
            uint32_t packetBufferSize = 100;
        };

        MultiStreamController();
        ~MultiStreamController();

        // 生命周期
        bool Initialize(const Config &config);
        void Shutdown();

        // 编码数据包处理
        bool HandleEncodedPacket(const std::string &encoderId, std::unique_ptr<EncodedPacket> packet);

        // 流控制
        bool StartAllStreams();
        void StopAllStreams();
        bool IsAnyStreamActive() const;

        // 配置更新
        bool UpdateConfig(const Config &config);
        Config GetConfig() const;

        // 统计
        struct Stats
        {
            StreamManager::GlobalStats streamStats;
            uint64_t totalInputPackets = 0;
            uint64_t totalOutputPackets = 0;
            uint64_t totalDroppedPackets = 0;
            float avgLatency = 0.0f;
        };

        Stats GetStats() const;

        // 回调
        using StatusCallback = std::function<void(const std::string &message)>;
        void SetStatusCallback(StatusCallback callback) { statusCallback_ = callback; }

    private:
        void ProcessingThread();
        void DistributePacket(const EncodedPacket &packet);

        Config config_;
        std::unique_ptr<StreamManager> streamManager_;

        // 数据包缓冲
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::mutex packetMutex_;
        std::condition_variable packetCondition_;

        // 处理线程
        std::thread processingThread_;
        std::atomic<bool> running_;

        // 统计
        Stats stats_;

        StatusCallback statusCallback_;

        mutable std::mutex mutex_;
    };

} // namespace StreamCapture