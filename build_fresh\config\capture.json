{"global": {"logLevel": "INFO", "logFile": "streamcapture.log", "maxMemoryMB": 512, "compatibility": "win7", "threadPoolSize": 4, "performanceMonitoring": true}, "injection": {"retryAttempts": 3, "retryDelayMS": 1000, "safeMode": true, "win7Compatible": true}, "capture": {"defaultFPS": 30, "maxBufferFrames": 10, "pixelFormat": "RGBA", "captureMethod": "d3d11"}, "encoding": {"hardwarePriority": ["nvenc", "qsv", "amf"], "softwareFallback": true, "defaultCodec": "h264", "defaultBitrate": 2000, "keyframeInterval": 60}, "streaming": {"reconnectAttempts": 5, "reconnectDelayMS": 2000, "bufferSizeMS": 3000, "protocolPriority": ["rtmp", "srt"]}, "targets": [{"name": "Unity3D Test Application", "enabled": true, "processName": "test1.exe", "windowTitle": "", "windowClass": "", "capture": {"fps": 30, "quality": "high", "skipFrames": false}, "encoder": {"type": "hardware", "codec": "h264", "bitrate": 2500, "preset": "balanced", "profile": "main"}, "outputs": [{"type": "rtmp", "url": "rtmp://localhost:1935/live/unity_stream", "enabled": true}, {"type": "file", "path": "output/unity_test_{timestamp}.mp4", "enabled": true}]}, {"name": "Unity Editor", "enabled": false, "processName": "Unity.exe", "windowTitle": "*Unity*", "capture": {"fps": 25, "quality": "medium"}, "encoder": {"type": "software", "codec": "h264", "bitrate": 1500}, "outputs": [{"type": "rtmp", "url": "rtmp://streaming-server.com/live/unity-dev", "enabled": false}]}]}