#include <iostream>
#include <thread>
#include <chrono>
#include <string>

int main()
{
    std::cout << "=== StreamCapture Simple Test ===" << std::endl;

    std::cout << "This is a basic test to verify the build system works." << std::endl;
    std::cout << "MediaMTX server integration will be added later." << std::endl;

    // 模拟一些工作
    std::cout << "Simulating stream capture..." << std::endl;
    for (int i = 1; i <= 5; ++i)
    {
        std::cout << "Frame " << i << " captured" << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    std::cout << "Test completed successfully!" << std::endl;
    return 0;
}
