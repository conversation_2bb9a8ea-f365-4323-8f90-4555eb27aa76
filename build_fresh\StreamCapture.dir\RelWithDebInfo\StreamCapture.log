﻿  Building Custom Rule F:/dev/Unity3D_project/SM-Record-main/StreamCapture/CMakeLists.txt
  main.cpp
  StreamCaptureController.cpp
  HookCore.cpp
  ConfigManager.cpp
  MediaServerManager.cpp
  Win7Compatibility.cpp
  FrameProcessor.cpp
  VideoEncoder.cpp
  FFmpegEncoder.cpp
  StreamPublisher.cpp
  FFmpegPublisher.cpp
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp(230,20): error C2653: “Win7Compatibility”: 不是类或命名空间名称
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp(230,39): error C3861: “Instance”: 找不到标识符
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp(239,29): error C3536: “sysInfo”: 初始化之前无法使用
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(59,14): error C2660: “StreamCapture::MediaServerManager::LaunchServerProcess”: 函数不接受 1 个参数
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(79,14):
      参见“StreamCapture::MediaServerManager::LaunchServerProcess”的声明
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(59,14):
      尝试匹配参数列表“(const std::string)”时
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(266,30): error C2511: “bool StreamCapture::MediaServerManager::LaunchServerProcess(const std::string &)”:“StreamCapture::MediaServerManager”中没有找到重载的成员函数
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(13,11):
      参见“StreamCapture::MediaServerManager”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(268,13): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverRunning_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(70,27):
      参见“StreamCapture::MediaServerManager::serverRunning_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(268,13): error C2451: 类型为“unknown”的条件表达式无效
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(268,13):
      对非静态成员的格式错误的引用
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(274,9): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverExecutablePath_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(66,21):
      参见“StreamCapture::MediaServerManager::serverExecutablePath_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(277,28): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverExecutablePath_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(66,21):
      参见“StreamCapture::MediaServerManager::serverExecutablePath_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(280,73): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverExecutablePath_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(280,73):
      参见“StreamCapture::MediaServerManager::serverExecutablePath_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(280,73): error C3867: “StreamCapture::MediaServerManager::serverExecutablePath_”: 非标准语法；请使用 "&" 来创建指向成员的指针
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(280,73): error C2568: “+”: 无法解析函数重载
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(280,13): error C2352: “StreamCapture::MediaServerManager::LogServerEvent”: 调用非静态成员函数需要一个对象
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(99,14):
      参见“StreamCapture::MediaServerManager::LogServerEvent”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(285,42): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverExecutablePath_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(285,42):
      参见“StreamCapture::MediaServerManager::serverExecutablePath_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(285,42): error C3867: “StreamCapture::MediaServerManager::serverExecutablePath_”: 非标准语法；请使用 "&" 来创建指向成员的指针
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(285,42): error C2568: “+”: 无法解析函数重载
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(288,36): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverConfigPath_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(288,36):
      参见“StreamCapture::MediaServerManager::serverConfigPath_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(288,36): error C3867: “StreamCapture::MediaServerManager::serverConfigPath_”: 非标准语法；请使用 "&" 来创建指向成员的指针
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(288,36): error C2568: “+”: 无法解析函数重载
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(316,77): error C2352: “StreamCapture::MediaServerManager::GetLastSystemError”: 调用非静态成员函数需要一个对象
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(100,21):
      参见“StreamCapture::MediaServerManager::GetLastSystemError”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(316,13): error C2352: “StreamCapture::MediaServerManager::LogServerEvent”: 调用非静态成员函数需要一个对象
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(99,14):
      参见“StreamCapture::MediaServerManager::LogServerEvent”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(322,9): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverProcessId_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(71,31):
      参见“StreamCapture::MediaServerManager::serverProcessId_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(323,9): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverRunning_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(70,27):
      参见“StreamCapture::MediaServerManager::serverRunning_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(336,9): error C2597: 对非静态成员“StreamCapture::MediaServerManager::monitoring_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(74,27):
      参见“StreamCapture::MediaServerManager::monitoring_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(337,81): error C2671: “StreamCapture::MediaServerManager::LaunchServerProcess”: 静态成员函数没有“this”指针
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(337,9): error C2597: 对非静态成员“StreamCapture::MediaServerManager::monitorThread_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(73,21):
      参见“StreamCapture::MediaServerManager::monitorThread_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(339,86): error C2597: 对非静态成员“StreamCapture::MediaServerManager::serverProcessId_”的非法引用
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(71,31):
      参见“StreamCapture::MediaServerManager::serverProcessId_”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(339,9): error C2352: “StreamCapture::MediaServerManager::LogServerEvent”: 调用非静态成员函数需要一个对象
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h(99,14):
      参见“StreamCapture::MediaServerManager::LogServerEvent”的声明
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(578,32): warning C4996: 'inet_addr': Use inet_pton() or InetPton() instead or define _WINSOCK_DEPRECATED_NO_WARNINGS to disable deprecated API warnings
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp(582,34): warning C4996: 'gethostbyname': Use getaddrinfo() or GetAddrInfoW() instead or define _WINSOCK_DEPRECATED_NO_WARNINGS to disable deprecated API warnings
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\memory(3308,27): error C2027: 使用了未定义类型“StreamCapture::MultiStreamController”
  (编译源文件“../src/core/StreamCaptureController.cpp”)
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureController.h(21,9):
      参见“StreamCapture::MultiStreamController”的声明
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\memory(3308,27):
      模板实例化上下文(最早的实例化上下文)为
          F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureController.h(175,46):
          查看对正在编译的 类 模板 实例化“std::unique_ptr<StreamCapture::MultiStreamController,std::default_delete<StreamCapture::MultiStreamController>>”的引用
          C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\memory(3352,92):
          查看对正在编译的 类 模板 实例化“std::default_delete<StreamCapture::MultiStreamController>”的引用
          C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\memory(3307,23):
          在编译 类 模板 成员函数“void std::default_delete<StreamCapture::MultiStreamController>::operator ()(_Ty *) noexcept const”时
          with
          [
              _Ty=StreamCapture::MultiStreamController
          ]
              C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\memory(3427,33):
              请参阅 "std::unique_ptr<StreamCapture::MultiStreamController,std::default_delete<StreamCapture::MultiStreamController>>::~unique_ptr" 中对 "std::default_delete<StreamCapture::MultiStreamController>::operator ()" 的第一个引用
              F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureController.h(179,5):
              请参阅 "StreamCapture::StreamCaptureController::TargetInstance::TargetInstance" 中对 "std::unique_ptr<StreamCapture::MultiStreamController,std::default_delete<StreamCapture::MultiStreamController>>::~unique_ptr" 的第一个引用
  
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\memory(3308,25): error C2338: static_assert failed: 'can't delete an incomplete type'
  (编译源文件“../src/core/StreamCaptureController.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\memory(3309,9): warning C4150: 删除指向不完整“StreamCapture::MultiStreamController”类型的指针；没有调用析构函数
  (编译源文件“../src/core/StreamCaptureController.cpp”)
      F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureController.h(21,9):
      参见“StreamCapture::MultiStreamController”的声明
  
