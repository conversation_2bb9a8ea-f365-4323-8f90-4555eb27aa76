#pragma once

#include "Common.h"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <mutex>

namespace StreamCapture
{

    // 全局配置
    struct GlobalConfig
    {
        std::string logLevel = "info";
        std::string logFile = "logs/stream_capture.log";
        size_t maxLogSize = 10 * 1024 * 1024; // 10MB
        bool enableConsoleLog = true;
        int threadPoolSize = 4;
    };

    // 采集配置
    struct CaptureConfigEx
    {
        bool enabled = true;
        std::string targetProcess = "test1.exe";
        std::string captureMethod = "hook";
        int frameRate = 30;
        int bufferSize = 10;
        bool enableGPUCapture = true;
    };

    // 编码器配置扩展
    struct EncoderConfigEx
    {
        std::string id;
        bool enabled = true;
        std::string type = "hardware"; // hardware, software
        std::string codec = "h264";    // h264, h265
        int width = 1920;
        int height = 1080;
        int fps = 30;
        int bitrate = 2000;             // kbps
        std::string preset = "quality"; // speed, balanced, quality
        std::string profile = "main";   // baseline, main, high
        int keyframeInterval = 60;
        bool enableBFrames = true;
        int bufferSize = 0;                 // 0 = auto
        std::string hardwareType = "nvenc"; // nvenc, qsv, amf
    };

    // 流配置扩展
    struct StreamConfigEx
    {
        std::string id;
        bool enabled = true;
        std::string encoderId;
        std::string type; // rtmp, srt, file, udp
        std::string url;
        int reconnectInterval = 5000; // ms
        int maxReconnectAttempts = 10;
        bool enableAutoReconnect = true;
        int connectionTimeout = 10000; // ms
        int sendTimeout = 5000;        // ms

        // RTMP specific
        std::map<std::string, std::string> metadata;

        // SRT specific
        int latency = 200;          // ms
        int maxBandwidth = 5000000; // bps

        // File specific
        bool enableSegmentation = false;
        int segmentDuration = 3600;                    // seconds
        size_t maxFileSize = 2LL * 1024 * 1024 * 1024; // 2GB
        bool enableTimestamp = true;

        // UDP specific
        int mtu = 1316;
        int bufferSize = 65536;
    };

    // 性能配置
    struct PerformanceConfig
    {
        bool enableGPUScheduling = true;
        int cpuThreads = 0; // 0 = auto
        bool enableLowLatency = true;
        bool enableZeroCopy = true;
        size_t memoryPoolSize = 128 * 1024 * 1024; // 128MB
        bool enableHardwareDecoding = true;
    };

    // 监控配置
    struct MonitoringConfig
    {
        bool enableStats = true;
        int statsInterval = 5000; // ms
        bool enableHealthCheck = true;
        int healthCheckInterval = 10000; // ms
        bool enablePerformanceMetrics = true;
        int metricsInterval = 1000; // ms
    };

    // 调试配置
    struct DebugConfig
    {
        bool enableFrameDump = false;
        std::string frameDumpPath = "debug/frames";
        int maxDumpFrames = 100;
        bool enablePacketDump = false;
        std::string packetDumpPath = "debug/packets";
        bool enableTimingLog = false;
    };

    // 完整配置
    struct CompleteConfig
    {
        GlobalConfig global;
        CaptureConfigEx capture;
        std::vector<EncoderConfigEx> encoders;
        std::vector<StreamConfigEx> streams;
        PerformanceConfig performance;
        MonitoringConfig monitoring;
        DebugConfig debug;
    };

    // 配置管理器
    class ConfigManager
    {
    public:
        ConfigManager();
        ~ConfigManager();

        // 配置加载和保存
        bool LoadFromFile(const std::string &filePath);
        bool SaveToFile(const std::string &filePath) const;
        bool LoadFromString(const std::string &jsonString);
        std::string SaveToString() const;

        // 配置访问
        const CompleteConfig &GetConfig() const { return config_; }
        CompleteConfig &GetConfig() { return config_; }

        // 特定配置访问
        const GlobalConfig &GetGlobalConfig() const { return config_.global; }
        const CaptureConfigEx &GetCaptureConfig() const { return config_.capture; }
        const std::vector<EncoderConfigEx> &GetEncoderConfigs() const { return config_.encoders; }
        const std::vector<StreamConfigEx> &GetStreamConfigs() const { return config_.streams; }
        const PerformanceConfig &GetPerformanceConfig() const { return config_.performance; }
        const MonitoringConfig &GetMonitoringConfig() const { return config_.monitoring; }
        const DebugConfig &GetDebugConfig() const { return config_.debug; }

        // 查找配置
        const EncoderConfigEx *FindEncoderConfig(const std::string &id) const;
        const StreamConfigEx *FindStreamConfig(const std::string &id) const;

        // 配置验证
        bool ValidateConfig() const;
        std::vector<std::string> GetValidationErrors() const;

        // 配置更新
        bool UpdateEncoderConfig(const std::string &id, const EncoderConfigEx &config);
        bool UpdateStreamConfig(const std::string &id, const StreamConfigEx &config);

        // 默认配置
        void SetDefaultConfig();

        // 配置监听
        using ConfigChangeCallback = std::function<void(const std::string &section, const std::string &key)>;
        void SetConfigChangeCallback(ConfigChangeCallback callback);

    private:
        CompleteConfig config_;
        ConfigChangeCallback changeCallback_;
        mutable std::mutex mutex_;

        // JSON 解析辅助函数
        bool ParseGlobalConfig(const void *json);
        bool ParseCaptureConfig(const void *json);
        bool ParseEncoderConfigs(const void *json);
        bool ParseStreamConfigs(const void *json);
        bool ParsePerformanceConfig(const void *json);
        bool ParseMonitoringConfig(const void *json);
        bool ParseDebugConfig(const void *json);

        // JSON 序列化辅助函数
        void SerializeGlobalConfig(void *json) const;
        void SerializeCaptureConfig(void *json) const;
        void SerializeEncoderConfigs(void *json) const;
        void SerializeStreamConfigs(void *json) const;
        void SerializePerformanceConfig(void *json) const;
        void SerializeMonitoringConfig(void *json) const;
        void SerializeDebugConfig(void *json) const;

        // 验证辅助函数
        bool ValidateEncoderConfig(const EncoderConfigEx &config, std::vector<std::string> &errors) const;
        bool ValidateStreamConfig(const StreamConfigEx &config, std::vector<std::string> &errors) const;

        void NotifyConfigChange(const std::string &section, const std::string &key);
    };

} // namespace StreamCapture
