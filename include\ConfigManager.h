#pragma once

#include "Common.h"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <mutex>

namespace StreamCapture
{

  // 全局配置
  struct GlobalConfig
  {
    std::string logLevel = "info";
    std::string logFile = "logs/stream_capture.log";
    bool enableConsoleLog = true;
    bool enableDebugMode = false;
    bool saveDebugFrames = false;
    std::string debugFramePath = "capture";
    int maxDebugFrames = 100;
  };

  // 采集配置
  struct CaptureConfigEx
  {
    bool enabled = true;
    std::string targetProcess = "game.exe";
    std::string captureMethod = "hook";
    int bufferSize = 10;
  };

  // 编码器配置
  struct EncoderConfigEx
  {
    std::string id;
    bool enabled = true;
    std::string type = "hardware"; // hardware, software
    std::string codec = "h264";    // h264, h265
    int width = 1920;
    int height = 1080;
    int fps = 30;
    int bitrate = 4000; // 单位 kbps
    std::string preset = "fast";
    std::string profile = "main";
    int keyframeInterval = 60;
    bool enableBFrames = false;
    int bufferSize = 10;
    std::string hardwareType = "nvenc";
  };

  // 完整配置
  struct CompleteConfig
  {
    GlobalConfig global;
    CaptureConfigEx capture;
    std::vector<EncoderConfigEx> encoders;
    std::vector<StreamConfigEx> streams;
  };

  // 配置管理器
  class ConfigManager
  {
  public:
    ConfigManager();
    ~ConfigManager();

    // 配置加载和保存
    bool LoadFromFile(const std::string &filePath);
    bool SaveToFile(const std::string &filePath) const;
    bool LoadFromString(const std::string &jsonString);
    std::string SaveToString() const;

    // 配置访问
    const CompleteConfig &GetConfig() const
    {
      return config_;
    }
    CompleteConfig &GetConfig()
    {
      return config_;
    }

    // 特定配置访问
    const GlobalConfig &GetGlobalConfig() const
    {
      return config_.global;
    }
    const CaptureConfigEx &GetCaptureConfig() const
    {
      return config_.capture;
    }
    const std::vector<EncoderConfigEx> &GetEncoderConfigs() const
    {
      return config_.encoders;
    }
    const std::vector<StreamConfigEx> &GetStreamConfigs() const
    {
      return config_.streams;
    }

    // 查找配置
    const EncoderConfigEx *FindEncoderConfig(const std::string &id) const;
    const StreamConfigEx *FindStreamConfig(const std::string &id) const;

    // 配置更新
    bool UpdateEncoderConfig(const std::string &id, const EncoderConfigEx &config);
    bool UpdateStreamConfig(const std::string &id, const StreamConfigEx &config);

    // 默认配置
    void SetDefaultConfig();

    // 配置监听
    using ConfigChangeCallback = std::function<void(const std::string &section, const std::string &key)>;
    void SetConfigChangeCallback(ConfigChangeCallback callback);

  private:
    CompleteConfig config_;
    ConfigChangeCallback changeCallback_;
    mutable std::mutex mutex_;

    void NotifyConfigChange(const std::string &section, const std::string &key);
  };

} // namespace StreamCapture