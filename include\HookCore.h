#pragma once

#include "Common.h"
#include "Win7Compatibility.h"
#include <MinHook.h>

namespace StreamCapture {

class D3D11Hook {
public:
    D3D11Hook();
    ~D3D11Hook();
    
    // 初始化和清理
    bool Initialize();
    void Shutdown();
    
    // Hook管理
    bool InstallHooks();
    bool UninstallHooks();
    
    // 回调设置
    void SetFrameCallback(FrameCallback callback) { frameCallback_ = callback; }
    void SetErrorCallback(ErrorCallback callback) { errorCallback_ = callback; }
    
    // 状态查询
    bool IsHooked() const { return isHooked_; }
    HookState GetState() const { return state_; }
    PerformanceStats GetStats() const { return stats_; }
    
    // 控制
    void EnableCapture(bool enable) { captureEnabled_ = enable; }
    bool IsCaptureEnabled() const { return captureEnabled_; }
    
private:
    // Hook函数指针类型
    typedef HRESULT(STDMETHODCALLTYPE* PresentFunc)(IDXGISwapChain* pSwapChain, 
                                                    UINT SyncInterval, UINT Flags);
    typedef HRESULT(STDMETHODCALLTYPE* ResizeBuffersFunc)(IDXGISwapChain* pSwapChain,
                                                          UINT BufferCount, UINT Width, 
                                                          UINT Height, DXGI_FORMAT NewFormat, 
                                                          UINT SwapChainFlags);
    
    // 静态Hook函数
    static HRESULT STDMETHODCALLTYPE HookedPresent(IDXGISwapChain* pSwapChain, 
                                                   UINT SyncInterval, UINT Flags);
    static HRESULT STDMETHODCALLTYPE HookedResizeBuffers(IDXGISwapChain* pSwapChain,
                                                         UINT BufferCount, UINT Width, 
                                                         UINT Height, DXGI_FORMAT NewFormat, 
                                                         UINT SwapChainFlags);
    
    // 实例方法
    HRESULT HandlePresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags);
    HRESULT HandleResizeBuffers(IDXGISwapChain* pSwapChain, UINT BufferCount, 
                                UINT Width, UINT Height, DXGI_FORMAT NewFormat, 
                                UINT SwapChainFlags);
    
    // 帧捕获
    bool CaptureFrame(IDXGISwapChain* pSwapChain);
    bool CreateRenderTargetView(IDXGISwapChain* pSwapChain);
    void CleanupD3DResources();
    
    // 错误处理
    void HandleError(ErrorCode code, const std::string& message);
    void ReportError(const std::string& function, HRESULT hr);
    
    // 性能统计
    void UpdateStats();
    
    // 成员变量
    static D3D11Hook* instance_;  // 单例实例
    
    std::atomic<HookState> state_;
    std::atomic<bool> isHooked_;
    std::atomic<bool> captureEnabled_;
    
    // 原始函数指针
    PresentFunc originalPresent_;
    ResizeBuffersFunc originalResizeBuffers_;
    
    // D3D11资源
    ID3D11Device* d3dDevice_;
    ID3D11DeviceContext* d3dContext_;
    ID3D11RenderTargetView* renderTargetView_;
    ID3D11Texture2D* stagingTexture_;
    
    // 帧信息
    uint32_t frameWidth_;
    uint32_t frameHeight_;
    DXGI_FORMAT frameFormat_;
    uint32_t frameIndex_;
    
    // 回调函数
    FrameCallback frameCallback_;
    ErrorCallback errorCallback_;
    
    // 性能统计
    PerformanceStats stats_;
    LARGE_INTEGER perfFrequency_;
    LARGE_INTEGER lastFrameTime_;
    
    // 同步
    mutable std::mutex mutex_;
    
    // Windows 7兼容性
    Win7Compatibility& compatibility_;
};

// 进程注入器
class ProcessInjector {
public:
    ProcessInjector();
    ~ProcessInjector();
    
    // 注入管理
    bool InjectIntoProcess(const std::string& processName, const std::string& dllPath);
    bool InjectIntoPID(DWORD processId, const std::string& dllPath);
    bool UninjectFromProcess(const std::string& processName, const std::string& dllPath);
    
    // 进程查找
    std::vector<DWORD> FindProcessesByName(const std::string& processName);
    DWORD FindProcessByWindow(const std::string& windowTitle, const std::string& windowClass = "");
    
    // 状态查询
    bool IsInjected(DWORD processId, const std::string& dllPath);
    
    // 错误处理
    void SetErrorCallback(ErrorCallback callback) { errorCallback_ = callback; }
    
private:
    // 注入实现
    bool PerformInjection(HANDLE hProcess, const std::string& dllPath);
    bool PerformEjection(HANDLE hProcess, const std::string& dllPath);
    
    // 辅助函数
    HMODULE GetRemoteModuleHandle(HANDLE hProcess, const std::string& moduleName);
    bool WaitForProcessStability(HANDLE hProcess);
    
    // 错误处理
    void HandleError(ErrorCode code, const std::string& message, DWORD lastError = 0);
    
    ErrorCallback errorCallback_;
    Win7Compatibility& compatibility_;
    std::mutex mutex_;
};

// Hook核心管理器
class HookCore {
public:
    static HookCore& Instance();
    
    // 生命周期
    bool Initialize();
    void Shutdown();
    
    // Hook管理
    bool StartHooking(const TargetConfig& config);
    void StopHooking();
    bool IsHooking() const;
    
    // 回调设置
    void SetFrameCallback(FrameCallback callback);
    void SetErrorCallback(ErrorCallback callback);
    void SetStateCallback(StateCallback callback);
    
    // 状态查询
    HookState GetState() const;
    PerformanceStats GetStats() const;
    
    // 控制
    void EnableCapture(bool enable);
    bool IsCaptureEnabled() const;
    
private:
    HookCore() = default;
    
public:
    ~HookCore() = default;
    
private:
    
    // 禁止拷贝
    HookCore(const HookCore&) = delete;
    HookCore& operator=(const HookCore&) = delete;
    
    // 内部状态
    void UpdateState(HookState newState);
    
    std::unique_ptr<D3D11Hook> d3dHook_;
    std::unique_ptr<ProcessInjector> injector_;
    
    TargetConfig currentTarget_;
    std::atomic<HookState> state_;
    
    FrameCallback frameCallback_;
    ErrorCallback errorCallback_;
    StateCallback stateCallback_;
    
    mutable std::mutex mutex_;
};

} // namespace StreamCapture