#pragma once

#include <string>
#include <vector>
#include <thread>
#include <atomic>
#include <memory>
#include <functional>
#include <mutex>

#include "Common.h"
#include "ConfigManager.h"
#include "MediaServerManager.h"

// 前向声明
namespace StreamCapture
{
  class HookCore;
  class FrameProcessor;
  class MultiEncoder;
  class MultiStreamController;
}

namespace StreamCapture
{

  // 配置加载器
  class ConfigLoader
  {
  public:
    static bool LoadConfig(const std::string& configPath, std::vector<TargetConfig>& targets);
    static bool SaveConfig(const std::string& configPath, const std::vector<TargetConfig>& targets);

    // 配置验证
    static bool ValidateConfig(const TargetConfig& config, std::string& errorMessage);

    // 默认配置生成
    static TargetConfig CreateDefaultConfig();

  private:
    static bool ParseJSON(const std::string& jsonContent, std::vector<TargetConfig>& targets);
    static std::string ConfigToJSON(const std::vector<TargetConfig>& targets);
  };

  // 系统监控器
  class SystemMonitor
  {
  public:
    struct SystemStatus
    {
      float cpuUsage = 0.0f;
      float memoryUsage = 0.0f;
      size_t availableMemoryMB = 0;
      float gpuUsage = 0.0f;
      uint32_t processCount = 0;
      bool isLowMemory = false;
      bool isHighCpuLoad = false;
    };

    SystemMonitor();
    ~SystemMonitor();

    bool Initialize();
    void Shutdown();

    SystemStatus GetStatus() const;
    void UpdateStatus();

    // 阈值设置
    void SetMemoryThreshold(float threshold)
    {
      memoryThreshold_ = threshold;
    }
    void SetCpuThreshold(float threshold)
    {
      cpuThreshold_ = threshold;
    }

  private:
    void MonitoringThread();

    SystemStatus status_;
    float memoryThreshold_ = 80.0f;  // 80%
    float cpuThreshold_ = 90.0f;     // 90%

    std::thread monitorThread_;
    std::atomic<bool> running_;
    mutable std::mutex mutex_;
  };

  // 主控制器
  class StreamCaptureController
  {
  public:
    enum class State
    {
      Idle,
      Initializing,
      Running,
      Stopping,
      Error
    };

    StreamCaptureController();
    ~StreamCaptureController();

    // 生命周期
    bool Initialize(const std::string& configPath = "config/capture.json");
    void Shutdown();

    // 捕获控制
    bool StartCapture();
    bool StopCapture();
    bool PauseCapture(bool pause);

    // 目标管理
    bool StartTarget(const std::string& targetName);
    bool StopTarget(const std::string& targetName);
    bool IsTargetActive(const std::string& targetName) const;

    // 状态查询
    State GetState() const
    {
      return state_;
    }
    std::vector<std::string> GetTargetNames() const;
    std::vector<TargetConfig> GetTargets() const;

    // 统计信息
    struct GlobalStats
    {
      uint32_t activeTargets = 0;
      uint32_t totalFramesCaptured = 0;
      uint32_t totalFramesEncoded = 0;
      uint32_t totalFramesStreamed = 0;
      uint32_t droppedFrames = 0;
      float avgCaptureTime = 0.0f;
      float avgEncodeTime = 0.0f;
      float avgStreamTime = 0.0f;
    };

    GlobalStats GetGlobalStats() const;
    PerformanceStats GetTargetStats(const std::string& targetName) const;

    // 配置管理
    bool ReloadConfig();
    bool UpdateTargetConfig(const std::string& targetName, const TargetConfig& config);

    // 回调设置
    using StateChangeCallback = std::function<void(State oldState, State newState)>;
    using ErrorCallback = std::function<void(const ErrorInfo&)>;
    using StatusCallback = std::function<void(const std::string& message)>;

    void SetStateCallback(StateChangeCallback callback)
    {
      stateCallback_ = callback;
    }
    void SetErrorCallback(ErrorCallback callback)
    {
      errorCallback_ = callback;
    }
    void SetStatusCallback(StatusCallback callback)
    {
      statusCallback_ = callback;
    }

  private:
    // 目标管理
    struct TargetInstance
    {
      TargetConfig config;
      std::unique_ptr<HookCore> hookCore;
      std::unique_ptr<FrameProcessor> frameProcessor;
      std::unique_ptr<MultiEncoder> encoder;
      std::unique_ptr<MultiStreamController> streamController;
      std::atomic<bool> active;
      std::atomic<bool> paused;
      PerformanceStats stats;
    };

  private:
    // 状态和配置
    State state_;
    std::string configPath_;
    std::vector<TargetConfig> targetConfigs_;

    // 目标实例
    std::vector<std::unique_ptr<TargetInstance>> targets_;


    // 媒体服务器管理器
    std::unique_ptr<MediaServerManager> mediaServerManager_;

    // 线程控制
    std::atomic<bool> running_;
    std::atomic<bool> paused_;
    std::thread monitoringThread_;

    // 回调
    StateChangeCallback stateCallback_;
    ErrorCallback errorCallback_;
    StatusCallback statusCallback_;

    // 私有方法
    void HandleStateChange(State newState);
    void MonitoringThread();
    TargetInstance* FindTarget(const std::string& name);
    std::unique_ptr<TargetInstance> CreateTargetInstance(const TargetConfig& config);
    bool StartTargetInstance(TargetInstance* target);
    void StopTargetInstance(TargetInstance* target);
    void UpdateTargetStats(TargetInstance* target);
    
    // 全局统计
    GlobalStats globalStats_;
    mutable std::mutex mutex_;
  };

  // 日志系统
  class Logger
  {
  public:
    enum class Level
    {
      Debug = 0,
      Info = 1,
      Warning = 2,
      Error = 3,
      Fatal = 4
    };

    static Logger& Instance();

    bool Initialize(const std::string& logFile = "", Level minLevel = Level::Info);
    void Shutdown();

    void Log(Level level, const std::string& message);
    void LogFormat(Level level, const char* format, ...);

    // 便捷方法
    void Debug(const std::string& message)
    {
      Log(Level::Debug, message);
    }
    void Info(const std::string& message)
    {
      Log(Level::Info, message);
    }
    void Warning(const std::string& message)
    {
      Log(Level::Warning, message);
    }
    void Error(const std::string& message)
    {
      Log(Level::Error, message);
    }
    void Fatal(const std::string& message)
    {
      Log(Level::Fatal, message);
    }

    void SetLevel(Level level)
    {
      minLevel_ = level;
    }
    Level GetLevel() const
    {
      return minLevel_;
    }

  private:
    Logger() = default;
    ~Logger() = default;

    std::string LevelToString(Level level) const;
    std::string GetTimestamp() const;

    std::string logFile_;
    Level minLevel_ = Level::Info;
    FILE* fileHandle_ = nullptr;

    mutable std::mutex mutex_;
  };

  // 便捷宏
#define LOG_DEBUG(msg) StreamCapture::Logger::Instance().Debug(msg)
#define LOG_INFO(msg) StreamCapture::Logger::Instance().Info(msg)
#define LOG_WARNING(msg) StreamCapture::Logger::Instance().Warning(msg)
#define LOG_ERROR(msg) StreamCapture::Logger::Instance().Error(msg)
#define LOG_FATAL(msg) StreamCapture::Logger::Instance().Fatal(msg)

#define LOG_DEBUG_F(fmt, ...) StreamCapture::Logger::Instance().LogFormat(StreamCapture::Logger::Level::Debug, fmt, __VA_ARGS__)
#define LOG_INFO_F(fmt, ...) StreamCapture::Logger::Instance().LogFormat(StreamCapture::Logger::Level::Info, fmt, __VA_ARGS__)
#define LOG_WARNING_F(fmt, ...) StreamCapture::Logger::Instance().LogFormat(StreamCapture::Logger::Level::Warning, fmt, __VA_ARGS__)
#define LOG_ERROR_F(fmt, ...) StreamCapture::Logger::Instance().LogFormat(StreamCapture::Logger::Level::Error, fmt, __VA_ARGS__)
#define LOG_FATAL_F(fmt, ...) StreamCapture::Logger::Instance().LogFormat(StreamCapture::Logger::Level::Fatal, fmt, __VA_ARGS__)

} // namespace StreamCapture