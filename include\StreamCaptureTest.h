#pragma once

#include "StreamCaptureController.h"
#include "ConfigManager.h"
#include "FFmpegEncoder.h"
#include "FFmpegPublisher.h"
#include "MediaServerManager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <memory>

namespace StreamCapture {

class StreamCaptureTest {
public:
    StreamCaptureTest();
    ~StreamCaptureTest();
    
    // 测试方法
    bool Initialize();
    void Shutdown();
    bool RunTest();
    
    // 测试配置
    void SetTestDuration(int seconds) { testDurationSeconds_ = seconds; }
    void SetVerbose(bool verbose) { verbose_ = verbose; }
    
private:
    std::unique_ptr<MediaServerManager> mediaServer_;
    std::unique_ptr<StreamCaptureController> controller_;
    std::atomic<bool> running_;
    int testDurationSeconds_;
    bool verbose_;
    
    // 测试辅助方法
    bool TestFFmpegEncoder();
    bool TestMediaServer();
    bool TestConfiguration();
    void PrintTestResults();
};

} // namespace StreamCapture
