{"global": {"logLevel": "info", "logFile": "logs/stream_capture.log", "maxLogSize": 10485760, "enableConsoleLog": true, "threadPoolSize": 4}, "capture": {"enabled": true, "targetProcess": "test1.exe", "captureMethod": "hook", "frameRate": 30, "bufferSize": 10, "enableGPUCapture": true}, "encoders": [{"id": "main_stream", "enabled": true, "type": "hardware", "codec": "h264", "width": 1920, "height": 1080, "fps": 30, "bitrate": 2000, "preset": "quality", "profile": "main", "keyframeInterval": 60, "enableBFrames": true, "bufferSize": 0, "hardwareType": "nvenc"}, {"id": "low_quality", "enabled": true, "type": "hardware", "codec": "h264", "width": 1280, "height": 720, "fps": 30, "bitrate": 1000, "preset": "speed", "profile": "main", "keyframeInterval": 60, "enableBFrames": false, "bufferSize": 0, "hardwareType": "nvenc"}, {"id": "software_backup", "enabled": false, "type": "software", "codec": "h264", "width": 1920, "height": 1080, "fps": 30, "bitrate": 1500, "preset": "balanced", "profile": "main", "keyframeInterval": 60, "enableBFrames": true, "bufferSize": 0}], "streams": [{"id": "rtmp_main", "enabled": true, "encoderId": "main_stream", "type": "rtmp", "url": "rtmp://127.0.0.1:1935/live/stream1", "reconnectInterval": 5000, "maxReconnectAttempts": 10, "enableAutoReconnect": true, "connectionTimeout": 10000, "sendTimeout": 5000, "metadata": {"title": "Unity3D Test Stream", "description": "Direct3D11 Hook Capture Stream"}}, {"id": "rtmp_backup", "enabled": true, "encoderId": "low_quality", "type": "rtmp", "url": "rtmp://backup.server.com:1935/live/stream1", "reconnectInterval": 5000, "maxReconnectAttempts": 5, "enableAutoReconnect": true, "connectionTimeout": 10000, "sendTimeout": 5000}, {"id": "srt_stream", "enabled": false, "encoderId": "main_stream", "type": "srt", "url": "srt://localhost:9999?mode=caller", "reconnectInterval": 3000, "maxReconnectAttempts": 10, "enableAutoReconnect": true, "connectionTimeout": 5000, "latency": 200, "maxBandwidth": 5000000}, {"id": "file_record", "enabled": true, "encoderId": "main_stream", "type": "file", "url": "recordings/capture_%Y%m%d_%H%M%S.mp4", "enableSegmentation": true, "segmentDuration": 3600, "maxFileSize": **********, "enableTimestamp": true}, {"id": "udp_stream", "enabled": false, "encoderId": "low_quality", "type": "udp", "url": "udp://***************:1234", "mtu": 1316, "bufferSize": 65536}], "performance": {"enableGPUScheduling": true, "cpuThreads": 0, "enableLowLatency": true, "enableZeroCopy": true, "memoryPoolSize": 134217728, "enableHardwareDecoding": true}, "monitoring": {"enableStats": true, "statsInterval": 5000, "enableHealthCheck": true, "healthCheckInterval": 10000, "enablePerformanceMetrics": true, "metricsInterval": 1000}, "debug": {"enableFrameDump": false, "frameDumpPath": "debug/frames", "maxDumpFrames": 100, "enablePacketDump": false, "packetDumpPath": "debug/packets", "enableTimingLog": false}}