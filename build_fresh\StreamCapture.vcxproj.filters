﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\StreamCaptureController.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\HookCore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\ConfigManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\utils\Win7Compatibility.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\capture\FrameProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\VideoEncoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\StreamPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\FFmpegPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\Common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureController.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\HookCore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\Win7Compatibility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\FrameProcessor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\VideoEncoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{02A05F19-5FD4-36F2-8387-6C9C0A10BF95}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{779C389B-FC8D-31AE-BB53-50910BC2E7E7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
