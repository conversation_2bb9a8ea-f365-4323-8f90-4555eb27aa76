#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <wininet.h>
#include <tlhelp32.h>
#include <fstream>
#include <sstream>
#include <chrono>
#include <thread>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <cctype>
#include <cstring>
#include <cstdlib>

// 添加缺失的定义
#ifndef TH32CS_SNAPNOHEAPS
#define TH32CS_SNAPNOHEAPS 0x40000000
#endif

#include "MediaServerManager.h"
#include "StreamCaptureController.h"

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "wininet.lib")
// 添加额外的Windows库
#pragma comment(lib, "advapi32.lib")  // 用于服务控制
#pragma comment(lib, "user32.lib")   // 用于窗口操作
#pragma comment(lib, "shell32.lib")  // 用于进程操作

namespace StreamCapture
{

    MediaServerManager::MediaServerManager()
        : serverRunning_(false), serverProcessId_(0), monitoring_(false)
    {

        // 初始化 Winsock
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);
    }

    MediaServerManager::~MediaServerManager()
    {
        StopServer();
        WSACleanup();
    }

    bool MediaServerManager::StartServer(const std::string &executablePath)
    {
        if (serverRunning_)
        {
            return true;
        }

        // 启动服务器进程
        if (!LaunchServerProcess(executablePath))
        {
            LogServerEvent("Error", "Failed to launch MediaMTX server process");
            return false;
        }

        // 生成默认配置文件
        if (!GenerateDefaultConfig())
        {
            LogServerEvent("Error", "Failed to generate server configuration");
            return false;
        }

        // 检查端口是否可用
        if (!IsPortAvailable(ports_.rtmpPort))
        {
            LogServerEvent("Warning", "RTMP port " + std::to_string(ports_.rtmpPort) + " is already in use");
        }

        // 启动服务器进程
        if (!LaunchServerProcess())
        {
            LogServerEvent("Error", "Failed to launch MediaMTX server process");
            return false;
        }

        // 等待服务器启动
        if (!WaitForServerReady(30))
        {
            LogServerEvent("Error", "Server failed to start within timeout");
            TerminateServerProcess();
            return false;
        }

        // 启动监控线程
        monitoring_ = true;
        monitorThread_ = std::thread(&MediaServerManager::MonitorServerProcess, this);

        serverRunning_ = true;
        LogServerEvent("Info", "MediaMTX server started successfully");

        if (statusCallback_)
        {
            statusCallback_(true, "Server started successfully");
        }

        return true;
    }

    void MediaServerManager::StopServer()
    {
        if (!serverRunning_)
        {
            return;
        }

        // 停止监控
        monitoring_ = false;
        if (monitorThread_.joinable())
        {
            monitorThread_.join();
        }

        // 终止服务器进程
        if (serverProcessId_ != 0)
        {
            HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, serverProcessId_);
            if (hProcess != nullptr)
            {
                // 首先尝试正常终止进程
                if (TerminateProcess(hProcess, 0))
                {
                    // 进程成功终止
                    serverProcessId_ = 0;
                    CloseHandle(hProcess);
                }
                else
                {
                    // 如果终止失败，尝试查找并终止子进程
                    TerminateChildProcesses(serverProcessId_);
                    // 然后强制关闭进程
                    DWORD exitCode;
                    if (GetExitCodeProcess(hProcess, &exitCode))
                    {
                        if (exitCode == STILL_ACTIVE)
                        {
                            TerminateProcess(hProcess, 1);
                        }
                    }
                    serverProcessId_ = 0;
                    CloseHandle(hProcess);
                }
            }
        }

        serverRunning_ = false;
        serverProcessId_ = 0;

        LogServerEvent("Info", "MediaMTX server stopped");

        if (statusCallback_)
        {
            statusCallback_(false, "Server stopped");
        }
    }

    bool MediaServerManager::IsServerRunning() const
    {
        return serverRunning_ && IsProcessRunning(serverProcessId_);
    }

    std::string MediaServerManager::GetServerStatus() const
    {
        if (!serverRunning_)
        {
            return "Stopped";
        }

        if (!IsProcessRunning(serverProcessId_))
        {
            return "Process not found";
        }

        // 检查服务器健康状态
        if (const_cast<MediaServerManager *>(this)->CheckServerHealth())
        {
            return "Running";
        }
        else
        {
            return "Unhealthy";
        }
    }

    bool MediaServerManager::SetServerConfig(const std::string &configPath)
    {
        serverConfigPath_ = configPath;
        return true;
    }

    std::string MediaServerManager::GetRTMPUrl(const std::string &streamKey) const
    {
        return "rtmp://localhost:" + std::to_string(ports_.rtmpPort) + "/live/" + streamKey;
    }

    std::string MediaServerManager::GetRTSPUrl(const std::string &streamKey) const
    {
        return "rtsp://localhost:" + std::to_string(ports_.rtspPort) + "/" + streamKey;
    }

    std::string MediaServerManager::GetHLSUrl(const std::string &streamKey) const
    {
        return "http://localhost:" + std::to_string(ports_.hlsPort) + "/" + streamKey + "/index.m3u8";
    }

    std::string MediaServerManager::GetWebRTCUrl(const std::string &streamKey) const
    {
        return "http://localhost:" + std::to_string(ports_.webrtcPort) + "/" + streamKey + "/whep";
    }

    void MediaServerManager::SetStatusCallback(std::function<void(bool, const std::string &)> callback)
    {
        statusCallback_ = callback;
    }

    bool MediaServerManager::IsPortAvailable(int port) const
    {
        SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock == INVALID_SOCKET)
        {
            return false;
        }

        sockaddr_in addr;
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = INADDR_ANY;
        addr.sin_port = htons(static_cast<u_short>(port));

        bool available = (bind(sock, (sockaddr *)&addr, sizeof(addr)) == 0);
        closesocket(sock);

        return available;
    }

    bool MediaServerManager::WaitForServerReady(int timeoutSeconds)
    {
        auto startTime = std::chrono::steady_clock::now();
        auto timeout = std::chrono::seconds(timeoutSeconds);

        while (std::chrono::steady_clock::now() - startTime < timeout)
        {
            if (CheckServerHealth())
            {
                return true;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }

        return false;
    }

    uint32_t MediaServerManager::GetServerProcessId() const
    {
        return serverProcessId_;
    }

    bool MediaServerManager::LaunchServerProcess(const std::string &executablePath)
    {
        if (serverRunning_)
        {
            return true;
        }

        // 保存可执行文件路径
        serverExecutablePath_ = executablePath;

        // 检查可执行文件是否存在
        std::ifstream file(serverExecutablePath_);
        if (!file.good())
        {
            LogServerEvent("Error", "MediaMTX executable not found: " + serverExecutablePath_);
            return false;
        }

        // 构建命令行
        std::string commandLine = "\"" + serverExecutablePath_ + "\"";
        if (!serverConfigPath_.empty())
        {
            commandLine += " \"" + serverConfigPath_ + "\"";
        }

        // 启动进程
        STARTUPINFOA si = {0};
        si.cb = sizeof(si);
        si.dwFlags = STARTF_USESHOWWINDOW;
        si.wShowWindow = SW_HIDE; // 隐藏控制台窗口
        
        PROCESS_INFORMATION pi = {0};

        // 创建进程
        BOOL result = CreateProcessA(
            nullptr,                    // 应用程序名称
            const_cast<LPSTR>(commandLine.c_str()), // 命令行
            nullptr,                    // 进程安全属性
            nullptr,                    // 线程安全属性
            FALSE,                      // 是否继承句柄
            CREATE_NEW_PROCESS_GROUP | // 创建新的进程组
            CREATE_NO_WINDOW,           // 不创建控制台窗口
            nullptr,                    // 环境变量
            nullptr,                    // 当前目录
            &si,                        // 启动信息
            &pi);                       // 进程信息

        if (!result)
        {
            DWORD error = GetLastError();
            LogServerEvent("Error", "Failed to create MediaMTX process: " + GetLastSystemError() + 
                          " (Error code: " + std::to_string(error) + ")");
            return false;
        }

        // 保存进程ID
        serverProcessId_ = pi.dwProcessId;
        serverRunning_ = true;

        // 关闭句柄
        if (pi.hProcess)
        {
            CloseHandle(pi.hProcess);
        }
        if (pi.hThread)
        {
            CloseHandle(pi.hThread);
        }

        // 启动监控线程
        monitoring_ = true;
        monitorThread_ = std::thread(&MediaServerManager::MonitorServerProcess, this);

        LogServerEvent("Info", "MediaMTX server started with PID: " + std::to_string(serverProcessId_));
        return true;
    }

    void MediaServerManager::TerminateServerProcess()
    {
        if (serverProcessId_ == 0)
        {
            return;
        }

        HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, serverProcessId_);
        if (hProcess != nullptr)
        {
            TerminateProcess(hProcess, 0);
            CloseHandle(hProcess);
        }

        serverProcessId_ = 0;
    }

    bool MediaServerManager::CheckServerHealth()
    {
        // 检查 RTMP 端口
        return IsHostReachable("localhost", ports_.rtmpPort, 1000);
    }

    void MediaServerManager::MonitorServerProcess()
    {
        while (monitoring_)
        {
            std::this_thread::sleep_for(std::chrono::seconds(5));

            if (!IsProcessRunning(serverProcessId_))
            {
                LogServerEvent("Warning", "Server process terminated unexpectedly");
                serverRunning_ = false;

                if (statusCallback_)
                {
                    statusCallback_(false, "Server process terminated");
                }
                break;
            }
        }
    }

    bool MediaServerManager::GenerateDefaultConfig()
    {
        // 生成配置内容
        std::string configContent = GetDefaultConfigContent();

        // 写入文件
        serverConfigPath_ = "config/mediamtx.yml";
        std::ofstream configFile(serverConfigPath_);
        if (!configFile.is_open())
        {
            // 尝试创建config目录
            CreateDirectoryA("config", nullptr);
            configFile.open(serverConfigPath_);
        }

        if (configFile.is_open())
        {
            configFile << configContent;
            configFile.close();
            return true;
        }

        LogServerEvent("Error", "Failed to write MediaMTX config file");
        return false;
    }

    std::string MediaServerManager::GetDefaultConfigContent() const
    {
        MediaServerConfigGenerator::Config config;
        return MediaServerConfigGenerator::GenerateConfig(config);
    }

    bool MediaServerManager::TestServerConnection(const std::string &url, int timeoutMs)
    {
      (void)url; // 避免未引用参数警告
      (void)timeoutMs;
        // 简单的连接测试
        return true; // 占位符实现
    }

    bool MediaServerManager::IsProcessRunning(uint32_t processId) const
    {
        if (processId == 0)
        {
            return false;
        }

        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (hProcess == nullptr)
        {
            // 如果无法打开进程，尝试使用另一种方法
            hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
            if (hProcess == nullptr)
            {
                return false;
            }
        }

        DWORD exitCode;
        BOOL result = GetExitCodeProcess(hProcess, &exitCode);
        CloseHandle(hProcess);

        return result && exitCode == STILL_ACTIVE;
    }

    bool MediaServerManager::TerminateChildProcesses(uint32_t parentProcessId) const
    {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS | TH32CS_SNAPNOHEAPS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
        {
            return false;
        }

        PROCESSENTRY32 pe;
        pe.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe))
        {
            do
            {
                if (pe.th32ParentProcessID == parentProcessId)
                {
                    HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, pe.th32ProcessID);
                    if (hProcess != nullptr)
                    {
                        TerminateProcess(hProcess, 0);
                        CloseHandle(hProcess);
                    }
                }
            } while (Process32Next(hSnapshot, &pe));
        }

        CloseHandle(hSnapshot);
        return true;
    }

    bool MediaServerManager::KillProcess(uint32_t processId) const
    {
        HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, processId);
        if (hProcess == nullptr)
        {
            return false;
        }

        BOOL result = TerminateProcess(hProcess, 0);
        CloseHandle(hProcess);

        return result != FALSE;
    }

    uint32_t MediaServerManager::FindProcessByName(const std::string &processName) const
    {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
        {
            return 0;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32))
        {
            do
            {
                // 比较进程名称
                if (processName == pe32.szExeFile)
                {
                    CloseHandle(hSnapshot);
                    return pe32.th32ProcessID;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return 0;
    }

    void MediaServerManager::LogServerEvent(const std::string &event, const std::string &details)
    {
      (void)event;
      (void)details;

        // 这里可以添加日志记录
        // LOG_INFO_F("MediaServer {}: {}", event, details);
    }

    std::string MediaServerManager::GetLastSystemError() const
    {
        DWORD error = GetLastError();
        if (error == 0)
        {
            return "No error";
        }

        LPSTR messageBuffer = nullptr;
        size_t size = FormatMessageA(
            FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
            nullptr, error, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
            (LPSTR)&messageBuffer, 0, nullptr);

        std::string message(messageBuffer, size);
        LocalFree(messageBuffer);

        // 移除末尾的换行符
        message.erase(std::remove(message.begin(), message.end(), '\r'), message.end());
        message.erase(std::remove(message.begin(), message.end(), '\n'), message.end());

        return message;
    }

    bool MediaServerManager::IsHostReachable(const std::string &host, int port, int timeoutMs) const
    {
        // 创建套接字
        SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock == INVALID_SOCKET)
        {
            return false;
        }

        // 设置为非阻塞模式
        u_long mode = 1;
        if (ioctlsocket(sock, FIONBIO, &mode) != 0)
        {
            closesocket(sock);
            return false;
        }

        // 解析主机名
        sockaddr_in addr;
        addr.sin_family = AF_INET;
        addr.sin_port = htons(static_cast<u_short>(port));
        addr.sin_addr.s_addr = inet_addr(host.c_str());

        if (addr.sin_addr.s_addr == INADDR_NONE)
        {
            struct hostent *he = gethostbyname(host.c_str());
            if (he == nullptr)
            {
                closesocket(sock);
                return false;
            }
            addr.sin_addr = *reinterpret_cast<in_addr *>(he->h_addr);
        }

        // 尝试连接
        int connectResult = connect(sock, reinterpret_cast<sockaddr *>(&addr), sizeof(addr));
        
        // 检查连接是否立即成功或非阻塞情况下是否进行中
        if (connectResult == 0 || WSAGetLastError() == WSAEWOULDBLOCK)
        {
            fd_set writeSet;
            FD_ZERO(&writeSet);
            FD_SET(sock, &writeSet);

            timeval timeout;
            timeout.tv_sec = timeoutMs / 1000;
            timeout.tv_usec = (timeoutMs % 1000) * 1000;

            int selectResult = select(0, nullptr, &writeSet, nullptr, &timeout);
            
            if (selectResult > 0 && FD_ISSET(sock, &writeSet))
            {
                // 连接成功
                closesocket(sock);
                return true;
            }
            else if (selectResult == 0)
            {
                // 超时
                closesocket(sock);
                return false;
            }
            else
            {
                // select错误
                closesocket(sock);
                return false;
            }
        }
        else
        {
            // 立即连接失败
            closesocket(sock);
            return false;
        }

        closesocket(sock);
        return false; // 默认情况不应该到达这里
    }

    /*
    bool MediaServerManager::ConnectToServer(const std::string& host, int port, uint32_t timeoutMs)
    {
        // 避免未引用参数警告
        (void)host;
        (void)port;
        (void)timeoutMs;
        
        // 实现连接逻辑
        return false;
    }
    */

    /*
    bool MediaServerManager::StartStreaming(const std::string& url, uint32_t timeoutMs)
    {
        // 避免未引用参数警告
        (void)url;
        (void)timeoutMs;
        
        // 实现推流逻辑
        return false;
    }
    */

    /*
    void MediaServerManager::HandleServerEvent(ServerEventType event, const std::string& details)
    {
        // 避免未引用参数警告
        (void)event;
        (void)details;
        
        // 实现事件处理逻辑
    }
    */

    /*
    bool MediaServerManager::SaveRecording(const std::string& filePath)
    {
        // 避免未引用参数警告
        (void)filePath;
        
        // 实现保存录制逻辑
        return false;
    }
    */

    // MediaServerConfigGenerator 实现
    std::string MediaServerConfigGenerator::GenerateConfig(const Config &config)
    {
        std::stringstream ss;

        // 全局配置
        ss << "# MediaMTX configuration file\n";
        ss << "# Generated automatically by StreamCapture\n\n";

        // 日志配置
        ss << GenerateLogSection(config);
        ss << "\n";

        // API 配置
        if (config.enableAPI)
        {
            ss << GenerateAPISection(config);
            ss << "\n";
        }

        // 性能配置
        ss << "readTimeout: " << config.readTimeout << "s\n";
        ss << "writeTimeout: " << config.writeTimeout << "s\n";
        ss << "readBufferCount: " << config.readBufferCount << "\n\n";

        // 协议配置
        if (config.enableRTMP)
        {
            ss << GenerateRTMPSection(config);
            ss << "\n";
        }

        if (config.enableRTSP)
        {
            ss << GenerateRTSPSection(config);
            ss << "\n";
        }

        if (config.enableHLS)
        {
            ss << GenerateHLSSection(config);
            ss << "\n";
        }

        if (config.enableWebRTC)
        {
            ss << GenerateWebRTCSection(config);
            ss << "\n";
        }

        // 路径配置
        ss << GeneratePathsSection(config);

        return ss.str();
    }

    bool MediaServerConfigGenerator::SaveConfigToFile(const Config &config, const std::string &filePath)
    {
        std::ofstream file(filePath);
        if (!file.is_open())
        {
            return false;
        }

        file << GenerateConfig(config);
        file.close();

        return true;
    }

    MediaServerConfigGenerator::Config MediaServerConfigGenerator::LoadConfigFromFile(const std::string &filePath)
    {
      (void)filePath;
        Config config; // 返回默认配置
        // 这里可以实现 YAML 解析
        return config;
    }

    std::string MediaServerConfigGenerator::GenerateRTMPSection(const Config &config)
    {
        std::stringstream ss;
        ss << "# RTMP server\n";
        ss << "rtmp: " << (config.enableRTMP ? "yes" : "no") << "\n";
        ss << "rtmpAddress: :" << config.rtmpPort << "\n";
        ss << "rtmpEncryption: optional\n";
        ss << "rtmpServerKey: server.key\n";
        ss << "rtmpServerCert: server.crt\n";
        ss << "rtmpAlwaysRemux: no\n";
        ss << "rtmpVariant: gpu\n";
        ss << "\n";
        return ss.str();
    }

    std::string MediaServerConfigGenerator::GenerateRTSPSection(const Config &config)
    {
        std::stringstream ss;
        ss << "# RTSP server\n";
        ss << "rtsp: " << (config.enableRTSP ? "yes" : "no") << "\n";
        ss << "rtspAddress: :" << config.rtspPort << "\n";
        ss << "rtspEncryption: no\n";
        ss << "rtspServerKey: server.key\n";
        ss << "rtspServerCert: server.crt\n";
        ss << "rtspAlwaysRemux: no\n";
        ss << "rtspVariant: gpu\n";
        ss << "\n";
        return ss.str();
    }

    std::string MediaServerConfigGenerator::GenerateHLSSection(const Config &config)
    {
        std::stringstream ss;
        ss << "# HLS server\n";
        ss << "hls: " << (config.enableHLS ? "yes" : "no") << "\n";
        ss << "hlsAddress: :" << config.hlsPort << "\n";
        ss << "hlsEncryption: no\n";
        ss << "hlsServerKey: server.key\n";
        ss << "hlsServerCert: server.crt\n";
        ss << "hlsAlwaysRemux: no\n";
        ss << "hlsVariant: memory\n";
        ss << "hlsSegmentCount: " << config.hlsSegmentCount << "\n";
        ss << "hlsSegmentDuration: " << config.hlsSegmentDuration << "\n";
        ss << "hlsPartDuration: 200ms\n";
        ss << "hlsSegmentMaxSize: 50M\n";
        ss << "hlsAllowOrigin: '*'\n";
        ss << "hlsTrustedProxies: []\n";
        ss << "hlsDirectory: ''\n";
        ss << "\n";
        return ss.str();
    }

    std::string MediaServerConfigGenerator::GenerateWebRTCSection(const Config &config)
    {
        std::stringstream ss;
        ss << "# WebRTC server\n";
        ss << "webrtc: " << (config.enableWebRTC ? "yes" : "no") << "\n";
        ss << "webrtcAddress: :" << config.webrtcPort << "\n";
        ss << "webrtcEncryption: no\n";
        ss << "webrtcServerKey: server.key\n";
        ss << "webrtcServerCert: server.crt\n";
        ss << "webrtcAllowOrigin: '*'\n";
        ss << "webrtcTrustedProxies: []\n";
        ss << "webrtcICEServers: [stun:stun.l.google.com:19302]\n";
        ss << "webrtcICEHostNAT1To1IPs: []\n";
        ss << "webrtcICEUDPMuxAddress: ''\n";
        ss << "webrtcICETCPMuxAddress: ''\n";
        ss << "\n";
        return ss.str();
    }

    std::string MediaServerConfigGenerator::GenerateAPISection(const Config &config)
    {
        std::stringstream ss;
        ss << "# API server\n";
        ss << "api: " << (config.enableAPI ? "yes" : "no") << "\n";
        ss << "apiAddress: :" << config.apiPort << "\n";
        ss << "apiEncryption: no\n";
        ss << "apiServerKey: server.key\n";
        ss << "apiServerCert: server.crt\n";
        ss << "apiAllowOrigin: '*'\n";
        ss << "\n";
        return ss.str();
    }

    std::string MediaServerConfigGenerator::GenerateLogSection(const Config &config)
    {
        std::stringstream ss;
        ss << "# Logging\n";
        ss << "logLevel: " << config.logLevel << "\n";
        ss << "logDestinations: [" << config.logDestinations << "]\n";
        ss << "\n";
        return ss.str();
    }

    std::string MediaServerConfigGenerator::GeneratePathsSection(const Config &config)
    {
        std::stringstream ss;
        ss << "pathDefaults:\n";
        ss << "  # Source of the stream\n";
        ss << "  source: publisher\n";
        ss << "  sourceOnDemand: false\n";
        ss << "  disablePublisherOverride: false\n";
        ss << "  fallback: ''\n";
        ss << "\n";
        ss << "  # Record streams\n";
        ss << "  record: " << (config.enableRecording ? "yes" : "no") << "\n";
        ss << "  recordPath: " << config.recordingPath << "\n";
        ss << "  recordFormat: fmp4\n";
        ss << "  recordPartDuration: 1s\n";
        ss << "  recordSegmentDuration: 1m\n";
        ss << "  recordDeleteAfter: 1h\n";
        ss << "\n";
        ss << "  # Authentication\n";
        ss << "  publishUser: ''\n";
        ss << "  publishPass: ''\n";
        ss << "  publishIPs: []\n";
        ss << "  readUser: ''\n";
        ss << "  readPass: ''\n";
        ss << "  readIPs: []\n";
        ss << "\n";
        ss << "  # Custom commands\n";
        ss << "  runOnInit: ''\n";
        ss << "  runOnInitRestart: no\n";
        ss << "  runOnDemand: ''\n";
        ss << "  runOnDemandRestart: no\n";
        ss << "  runOnDemandStartTimeout: 10s\n";
        ss << "  runOnDemandCloseAfter: 10s\n";
        ss << "  runOnUnDemand: ''\n";
        ss << "  runOnReady: ''\n";
        ss << "  runOnNotReady: ''\n";
        ss << "  runOnRead: ''\n";
        ss << "  runOnUnread: ''\n";
        ss << "\n";
        
        if (config.enableRecording)
        {
            ss << "  # Recording specific settings\n";
            ss << "  recordThread: true\n";
            ss << "  recordFormat: fmp4\n";
            ss << "\n";
        }

        ss << "paths:\n";
        ss << "  # Default path for all streams\n";
        ss << "  all_others:\n";
        ss << "\n";

        return ss.str();
    }

} // namespace StreamCapture
