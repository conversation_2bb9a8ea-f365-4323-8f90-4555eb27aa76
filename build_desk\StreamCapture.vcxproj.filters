﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\StreamCaptureController.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\HookCore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\ConfigManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\utils\Win7Compatibility.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\capture\FrameProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\VideoEncoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\StreamPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\FFmpegPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\MultiStreamController.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\Common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureController.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\HookCore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\ConfigManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MediaServerManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\Win7Compatibility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\FrameProcessor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\VideoEncoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\FFmpegEncoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\FFmpegPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\MultiStreamController.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\include\StreamCaptureTest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{C0767735-EC5B-3776-BCF4-1B6E53EB5D13}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{F9422F7E-884D-3F98-88DF-33FEBF27D269}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
