﻿#include "StreamCaptureController.h"
#include "ConfigManager.h"
#include "FFmpegEncoder.h"
#include "FFmpegPublisher.h"
#include "StreamPublisher.h"
#include "MediaServerManager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <memory>

using namespace StreamCapture;

struct EncoderConfigEx
{
    std::string id;
    bool enabled = true;
    std::string type = "hardware"; // hardware, software
    std::string codec = "h264";    // h264, h265
    int width = 1920;
    int height = 1080;
    int fps = 30;
    int bitrate = 4000; // 单位 kbps
    std::string preset = "fast";
    std::string profile = "main";
    int keyframeInterval = 60;
    bool enableBFrames = false;
    int bufferSize = 10;
    std::string hardwareType = "nvenc";
};

class StreamCaptureTest
{
public:
    StreamCaptureTest() : running_(false) {}

    void Run()
    {
        if (!Initialize())
        {
            return;
        }

        running_ = true;

        // 启动统计线程
        std::thread statsThread(&StreamCaptureTest::StatsThread, this);

        // 模拟帧数据生成
        std::thread frameThread(&StreamCaptureTest::FrameGenerationThread, this);

        // 主循环
        std::cout << "StreamCapture test running... Press Enter to stop" << std::endl;
        std::cin.get();

        // 停止
        running_ = false;

        if (frameThread.joinable())
        {
            frameThread.join();
        }

        if (statsThread.joinable())
        {
            statsThread.join();
        }

        Shutdown();
    }

private:
    std::unique_ptr<MediaServerManager> mediaServer_;
    std::unique_ptr<ConfigManager> configManager_;
    std::vector<std::unique_ptr<FFmpegEncoder>> encoders_;
    std::vector<std::unique_ptr<StreamPublisher>> publishers_;
    std::atomic<bool> running_;

    bool Initialize()
    {
        // 启动媒体服务器
        std::cout << "Starting MediaMTX server..." << std::endl;
        mediaServer_ = std::make_unique<MediaServerManager>();

        // 设置状态回调
        mediaServer_->SetStatusCallback([](bool isRunning, const std::string &status)
                                        { std::cout << "MediaServer status: " << (isRunning ? "Running" : "Stopped")
                                                    << " - " << status << std::endl; });

        if (!mediaServer_->StartServer("mediamtx\\mediamtx.exe"))
        {
            std::cerr << "Failed to start MediaMTX server" << std::endl;
            std::cerr << "Please ensure mediamtx.exe is in the mediamtx folder" << std::endl;
            return false;
        }

        std::cout << "MediaMTX server started successfully!" << std::endl;
        std::cout << "RTMP URL: " << mediaServer_->GetRTMPUrl("stream1") << std::endl;
        std::cout << "RTSP URL: " << mediaServer_->GetRTSPUrl("stream1") << std::endl;
        std::cout << "HLS URL: " << mediaServer_->GetHLSUrl("stream1") << std::endl;
        std::cout << std::endl;

        // 初始化 FFmpeg
        if (!FFmpegUtils::InitializeFFmpeg())
        {
            std::cerr << "Failed to initialize FFmpeg" << std::endl;
            return false;
        }

        // 加载配置
        configManager_ = std::make_unique<ConfigManager>();
        if (!configManager_->LoadFromFile("config/stream_config.json"))
        {
            std::cout << "Warning: Could not load config file, using defaults" << std::endl;
            configManager_->SetDefaultConfig();
        }


        // 创建编码器
        if (!CreateEncoders())
        {
            std::cerr << "Failed to create encoders" << std::endl;
            return false;
        }

        // 创建推流器
        if (!CreatePublishers())
        {
            std::cerr << "Failed to create publishers" << std::endl;
            return false;
        }

        std::cout << "StreamCapture test initialized successfully" << std::endl;
        return true;
    }

    void Shutdown()
    {
        running_ = false;

        // 停止推流器
        for (auto &publisher : publishers_)
        {
            if (publisher)
            {
                publisher->Disconnect();
                publisher->Shutdown();
            }
        }
        publishers_.clear();

        // 停止编码器
        for (auto &encoder : encoders_)
        {
            if (encoder)
            {
                encoder->Shutdown();
            }
        }
        encoders_.clear();

        // 停止媒体服务器
        if (mediaServer_)
        {
            std::cout << "Stopping MediaMTX server..." << std::endl;
            mediaServer_->StopServer();
            mediaServer_.reset();
        }

        // 清理 FFmpeg
        FFmpegUtils::CleanupFFmpeg();

        std::cout << "StreamCapture test shutdown completed" << std::endl;
    }

    bool CreateEncoders()
    {
        const auto &encoderConfigs = configManager_->GetEncoderConfigs();

        for (const auto &config : encoderConfigs)
        {
            if (!config.enabled)
            {
                continue;
            }

            auto encoder = std::make_unique<FFmpegEncoder>();
            if (!encoder->InitializeEx(config))
            {
                std::cerr << "Failed to initialize encoder: " << config.id << std::endl;
                continue;
            }

            encoders_.push_back(std::move(encoder));
            std::cout << "Created encoder: " << config.id << " (" << config.type << ")" << std::endl;
        }

        return !encoders_.empty();
    }

    bool CreatePublishers()
    {
        const auto &streamConfigs = configManager_->GetStreamConfigs();

        for (const auto &config : streamConfigs)
        {
            if (!config.enabled)
            {
                continue;
            }

            std::unique_ptr<StreamPublisher> publisher;

            if (config.type == "rtmp")
            {
                auto rtmpPublisher = std::make_unique<FFmpegRTMPPublisher>();
                if (rtmpPublisher->InitializeEx(config))
                {
                    publisher = std::move(rtmpPublisher);
                }
            }
            // 可以添加其他类型的推流器

            if (publisher)
            {
                if (publisher->Connect())
                {
                    publishers_.push_back(std::move(publisher));
                    std::cout << "Created and connected publisher: " << config.id << " (" << config.type << ")" << std::endl;
                }
                else
                {
                    std::cerr << "Failed to connect publisher: " << config.id << std::endl;
                }
            }
            else
            {
                std::cerr << "Failed to create publisher: " << config.id << std::endl;
            }
        }

        return !publishers_.empty();
    }

    void FrameGenerationThread()
    {
        const int width = 1920;
        const int height = 1080;
        const int fps = 30;
        const auto frameDuration = std::chrono::milliseconds(1000 / fps);

        uint32_t frameIndex = 0;
        auto startTime = std::chrono::high_resolution_clock::now();

        std::cout << "Starting frame generation thread..." << std::endl;

        while (running_)
        {
            // 创建模拟帧数据
            auto frame = CreateTestFrame(width, height, frameIndex);

            // 编码帧
            for (auto &encoder : encoders_)
            {
                if (encoder)
                {
                    encoder->EncodeFrame(*frame);

                    // 获取编码后的数据包
                    auto packet = encoder->GetEncodedPacket(10); // 10ms 超时
                    if (packet)
                    {
                        // 发送到所有推流器
                        for (auto &publisher : publishers_)
                        {
                            if (publisher && publisher->IsConnected())
                            {
                                publisher->SendPacket(*packet);
                            }
                        }
                    }
                }
            }

            frameIndex++;

            // 控制帧率
            std::this_thread::sleep_for(frameDuration);
        }

        std::cout << "Frame generation thread stopped" << std::endl;
    }

    std::unique_ptr<FrameData> CreateTestFrame(int width, int height, uint32_t frameIndex)
    {
        auto frame = std::make_unique<FrameData>();

        frame->width = width;
        frame->height = height;
        frame->stride = width * 4; // RGBA
        frame->format = DXGI_FORMAT_R8G8B8A8_UNORM;
        frame->frameIndex = frameIndex;
        frame->timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
                               std::chrono::high_resolution_clock::now().time_since_epoch())
                               .count();

        // 分配帧数据
        frame->size = frame->stride * height;
        frame->data = new uint8_t[frame->size];

        // 生成测试图案（彩色条纹）
        uint8_t *pixels = frame->data;
        for (int y = 0; y < height; ++y)
        {
            for (int x = 0; x < width; ++x)
            {
                int pixelIndex = (y * width + x) * 4;

                // 生成彩色条纹图案
                uint8_t r = (uint8_t)((x * 255) / width);
                uint8_t g = (uint8_t)((y * 255) / height);
                uint8_t b = (uint8_t)((frameIndex * 5) % 255);
                uint8_t a = 255;

                pixels[pixelIndex + 0] = r; // R
                pixels[pixelIndex + 1] = g; // G
                pixels[pixelIndex + 2] = b; // B
                pixels[pixelIndex + 3] = a; // A
            }
        }

        return frame;
    }

    void StatsThread()
    {
        std::cout << "Starting statistics thread..." << std::endl;

        while (running_)
        {
            std::this_thread::sleep_for(std::chrono::seconds(5));

            if (!running_)
                break;

            std::cout << "\n=== Statistics ===" << std::endl;

            // 编码器统计
            for (size_t i = 0; i < encoders_.size(); ++i)
            {
                if (encoders_[i])
                {
                    auto stats = encoders_[i]->GetStats();
                    std::cout << "Encoder " << i << ":" << std::endl;
                    std::cout << "  Input frames: " << stats.inputFrames << std::endl;
                    std::cout << "  Output packets: " << stats.outputPackets << std::endl;
                    std::cout << "  Dropped frames: " << stats.droppedFrames << std::endl;
                    std::cout << "  Total bytes: " << stats.totalBytes << std::endl;
                    std::cout << "  Avg bitrate: " << stats.avgBitrate << " kbps" << std::endl;
                }
            }

            // 推流器统计
            for (size_t i = 0; i < publishers_.size(); ++i)
            {
                if (publishers_[i])
                {
                    auto stats = publishers_[i]->GetStats();
                    std::cout << "Publisher " << i << " (" << publishers_[i]->GetPublisherName() << "):" << std::endl;
                    std::cout << "  Connected: " << (publishers_[i]->IsConnected() ? "Yes" : "No") << std::endl;
                    std::cout << "  Total packets: " << stats.totalPackets << std::endl;
                    std::cout << "  Dropped packets: " << stats.droppedPackets << std::endl;
                    std::cout << "  Total bytes: " << stats.totalBytes << std::endl;
                    std::cout << "  Avg bitrate: " << stats.avgBitrate << " kbps" << std::endl;
                }
            }

            std::cout << "==================" << std::endl;
        }

        std::cout << "Statistics thread stopped" << std::endl;
    }
};

int main()
{
    std::cout << "StreamCapture Test Application" << std::endl;
    std::cout << "==============================" << std::endl;

    try
    {
        StreamCaptureTest test;
        test.Run();
    }
    catch (const std::exception &e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
