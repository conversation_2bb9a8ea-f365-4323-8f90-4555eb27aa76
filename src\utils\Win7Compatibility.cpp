#include "Win7Compatibility.h"
#include <windows.h>
#include <VersionHelpers.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <vector>
#include <dxgi.h>
#include <d3d11.h>
#include <memory>
#include <sstream>
#include <algorithm>
#include <cctype>
#include <iostream>
#include <fstream>
#include <iomanip>
#include <string>

#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "dxgi.lib")

namespace StreamCapture {

Win7Compatibility& Win7Compatibility::Instance() {
    static Win7Compatibility instance;
    return instance;
}

bool Win7Compatibility::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (initialized_) {
        return true;
    }

    // 使用VersionHelpers.h中的函数替代GetVersionEx
    systemInfo_.isWindows7 = IsWindows7SP1OrGreater() && !IsWindows8OrGreater();

    // 获取内存信息
    MEMORYSTATUSEX memStatus = {0};
    memStatus.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memStatus)) {
        systemInfo_.totalMemoryMB = static_cast<size_t>(memStatus.ullTotalPhys / (1024 * 1024));
        systemInfo_.availableMemoryMB = static_cast<size_t>(memStatus.ullAvailPhys / (1024 * 1024));
    } else {
        systemInfo_.totalMemoryMB = 0;
        systemInfo_.availableMemoryMB = 0;
    }

    // 检测GPU信息
    EnumerateGPUs();

    initialized_ = true;
    return true;
}

std::string Win7Compatibility::GetLastErrorString() const
{
    DWORD errorCode = GetLastError();
    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer, 0, NULL);

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);
    
    std::ostringstream oss;
    oss << "Error " << errorCode << ": " << message;
    return oss.str();
}

void Win7Compatibility::HandleCrash(EXCEPTION_POINTERS* pExceptionInfo) {
    // 简单的崩溃处理 - 记录信息
    DWORD exceptionCode = 0;
    PVOID exceptionAddress = nullptr;
    
    if (pExceptionInfo && pExceptionInfo->ExceptionRecord) {
        exceptionCode = pExceptionInfo->ExceptionRecord->ExceptionCode;
        exceptionAddress = pExceptionInfo->ExceptionRecord->ExceptionAddress;
    }
    
    // 这里可以添加更详细的崩溃日志记录
    char crashInfo[512];
    sprintf_s(crashInfo, sizeof(crashInfo), 
              "Crash: Code=0x%08X, Address=0x%p, Process=%lu",
              exceptionCode, exceptionAddress, GetCurrentProcessId());
    
    OutputDebugStringA(crashInfo);
    
    // 尝试写入日志文件
    FILE* crashLog = nullptr;
    if (fopen_s(&crashLog, "streamcapture_crash.log", "a") == 0 && crashLog) {
        fprintf(crashLog, "[%lu] %s\n", GetTickCount(), crashInfo);
        fclose(crashLog);
    }
}

bool Win7Compatibility::IsLowMemory() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return systemInfo_.availableMemoryMB < 512; // 低于512MB认为内存不足
}

size_t Win7Compatibility::GetAvailableMemory() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return systemInfo_.availableMemoryMB;
}

void Win7Compatibility::EnumerateGPUs() {
    // 清空现有GPU列表
    systemInfo_.gpus.clear();

    // 创建DXGI工厂
    IDXGIFactory* pFactory = nullptr;
    HRESULT hr = CreateDXGIFactory(__uuidof(IDXGIFactory), (void**)&pFactory);
    if (FAILED(hr)) {
        return;
    }

    // 枚举适配器
    UINT adapterIndex = 0;
    IDXGIAdapter* pAdapter = nullptr;
    
    while (pFactory->EnumAdapters(adapterIndex, &pAdapter) != DXGI_ERROR_NOT_FOUND) {
        DXGI_ADAPTER_DESC desc;
        hr = pAdapter->GetDesc(&desc);
        
        if (SUCCEEDED(hr)) {
            GPUInfo gpuInfo = CreateGPUInfo(pAdapter, desc);
            gpuInfo.isPrimary = (adapterIndex == 0); // 第一个适配器通常是主要的
            
            // 检测硬件编码支持
            if (IsNVIDIAGPU(gpuInfo.vendorId)) {
                gpuInfo.supportsNVENC = DetectNVENC(gpuInfo);
            } else if (IsIntelGPU(gpuInfo.vendorId)) {
                gpuInfo.supportsQSV = DetectQSV(gpuInfo);
            } else if (IsAMDGPU(gpuInfo.vendorId)) {
                gpuInfo.supportsAMF = DetectAMF(gpuInfo);
            }
            
            systemInfo_.gpus.push_back(gpuInfo);
        }
        
        pAdapter->Release();
        pAdapter = nullptr;
        adapterIndex++;
    }
    
    pFactory->Release();
}

GPUInfo Win7Compatibility::CreateGPUInfo(IDXGIAdapter* pAdapter, DXGI_ADAPTER_DESC& desc) 
{
  (void*)pAdapter;
    GPUInfo info;
    
    // 转换宽字符到多字节字符
    int bufferSize = WideCharToMultiByte(CP_UTF8, 0, desc.Description, -1, nullptr, 0, nullptr, nullptr);
    if (bufferSize > 0) {
        std::vector<char> buffer(bufferSize);
        WideCharToMultiByte(CP_UTF8, 0, desc.Description, -1, buffer.data(), bufferSize, nullptr, nullptr);
        info.name = std::string(buffer.data());
    }
    
    info.vendorId = desc.VendorId;
    info.deviceId = desc.DeviceId;
    info.dedicatedMemoryMB = static_cast<size_t>(desc.DedicatedVideoMemory / (1024 * 1024));
    
    // 确定供应商
    if (desc.VendorId == 0x10DE) {
        info.vendor = "NVIDIA";
        info.supportsNVENC = true;
    } else if (desc.VendorId == 0x8086) {
        info.vendor = "Intel";
        info.supportsQSV = true;
    } else if (desc.VendorId == 0x1002) {
        info.vendor = "AMD";
        info.supportsAMF = true;
    } else {
        info.vendor = "Unknown";
        info.supportsNVENC = false;
        info.supportsQSV = false;
        info.supportsAMF = false;
    }
    
    return info;
}

bool Win7Compatibility::IsNVIDIAGPU(uint32_t vendorId) {
    return vendorId == 0x10DE;
}

bool Win7Compatibility::IsIntelGPU(uint32_t vendorId) {
    return vendorId == 0x8086;
}

bool Win7Compatibility::IsAMDGPU(uint32_t vendorId) {
    return vendorId == 0x1002;
}

bool Win7Compatibility::DetectNVENC(const GPUInfo& gpu) {
    // 简化的NVENC检测
    // 实际应该检查NVIDIA驱动版本和NVENC库
    std::string gpuName = gpu.name;
    std::transform(gpuName.begin(), gpuName.end(), gpuName.begin(), ::tolower);
    
    // 检查是否包含NVIDIA关键词
    return gpuName.find("nvidia") != std::string::npos || 
           gpuName.find("geforce") != std::string::npos ||
           gpuName.find("quadro") != std::string::npos ||
           gpuName.find("tesla") != std::string::npos;
}

bool Win7Compatibility::DetectQSV(const GPUInfo& gpu) {
    // 简化的QSV检测
    // 实际应该检查Intel驱动版本和Media SDK
    std::string gpuName = gpu.name;
    std::transform(gpuName.begin(), gpuName.end(), gpuName.begin(), ::tolower);
    
    // 检查是否包含Intel关键词
    return gpuName.find("intel") != std::string::npos;
}

bool Win7Compatibility::DetectAMF(const GPUInfo& gpu) {
    // 简化的AMF检测
    // 实际应该检查AMD驱动版本和AMF库
    std::string gpuName = gpu.name;
    std::transform(gpuName.begin(), gpuName.end(), gpuName.begin(), ::tolower);
    
    // 检查是否包含AMD关键词
    return gpuName.find("amd") != std::string::npos ||
           gpuName.find("radeon") != std::string::npos ||
           gpuName.find("firepro") != std::string::npos;
}

GPUInfo* Win7Compatibility::GetPrimaryGPU() {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& gpu : systemInfo_.gpus) {
        if (gpu.isPrimary) {
            return &gpu;
        }
    }
    return !systemInfo_.gpus.empty() ? &systemInfo_.gpus[0] : nullptr;
}

GPUInfo* Win7Compatibility::GetNVIDIAGPU() {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& gpu : systemInfo_.gpus) {
        if (IsNVIDIAGPU(gpu.vendorId)) {
            return &gpu;
        }
    }
    return nullptr;
}

GPUInfo* Win7Compatibility::GetIntelGPU() {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& gpu : systemInfo_.gpus) {
        if (IsIntelGPU(gpu.vendorId)) {
            return &gpu;
        }
    }
    return nullptr;
}

GPUInfo* Win7Compatibility::GetAMDGPU() {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& gpu : systemInfo_.gpus) {
        if (IsAMDGPU(gpu.vendorId)) {
            return &gpu;
        }
    }
    return nullptr;
}

HANDLE Win7Compatibility::SafeCreateRemoteThread(HANDLE hProcess, LPSECURITY_ATTRIBUTES lpThreadAttributes,
                                  SIZE_T dwStackSize, LPTHREAD_START_ROUTINE lpStartAddress,
                                  LPVOID lpParameter, DWORD dwCreationFlags, LPDWORD lpThreadId)
{
    // 如果有动态加载的函数，使用它，否则回退到系统API
    if (createRemoteThreadProc_) {
        return createRemoteThreadProc_(hProcess, lpThreadAttributes, dwStackSize, lpStartAddress, lpParameter, dwCreationFlags, lpThreadId);
    }
    
    // 回退到系统API
    return CreateRemoteThread(hProcess, lpThreadAttributes, dwStackSize, lpStartAddress, lpParameter, dwCreationFlags, lpThreadId);
}

LPVOID Win7Compatibility::SafeVirtualAllocEx(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize,
                              DWORD flAllocationType, DWORD flProtect)
{
    // 使用系统API
    return VirtualAllocEx(hProcess, lpAddress, dwSize, flAllocationType, flProtect);
}

BOOL Win7Compatibility::SafeWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress,
                                LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten)
{
    // 使用系统API
    return WriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
}

} // namespace StreamCapture
