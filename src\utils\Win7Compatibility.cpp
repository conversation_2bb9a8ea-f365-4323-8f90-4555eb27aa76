#include "Win7Compatibility.h"
#include <psapi.h>
#include <tlhelp32.h>
#include <sstream>
#include <vector>

#pragma comment(lib, "psapi.lib")

namespace StreamCapture {

Win7Compatibility& Win7Compatibility::Instance() {
    static Win7Compatibility instance;
    return instance;
}

bool Win7Compatibility::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 检测系统版本
    OSVERSIONINFOEX osvi = {0};
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
    
    if (!GetVersionEx((OSVERSIONINFO*)&osvi)) {
        return false;
    }
    
    systemInfo_.isWindows7 = (osvi.dwMajorVersion == 6 && osvi.dwMinorVersion == 1);
    
    // 获取内存信息
    MEMORYSTATUSEX memStatus = {0};
    memStatus.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memStatus)) {
        systemInfo_.totalMemoryMB = static_cast<size_t>(memStatus.ullTotalPhys / (1024 * 1024));
        systemInfo_.availableMemoryMB = static_cast<size_t>(memStatus.ullAvailPhys / (1024 * 1024));
    }
    
    DetectSystemCapabilities();
    SetupExceptionHandler();
    
    return true;
}

void Win7Compatibility::DetectSystemCapabilities() {
    // 检测NVENC支持
    systemInfo_.hasNVENC = false;
    systemInfo_.hasQSV = false;
    systemInfo_.hasAMF = false;
    systemInfo_.gpuName = "Unknown";
    
    // 尝试检测GPU
    try {
        // 简单的GPU检测 - 在实际实现中应该使用更复杂的方法
        DISPLAY_DEVICE dd = {0};
        dd.cb = sizeof(DISPLAY_DEVICE);
        
        if (EnumDisplayDevices(NULL, 0, &dd, 0)) {
            std::string deviceName = dd.DeviceString;
            systemInfo_.gpuName = deviceName;
            
            // 简单的厂商检测
            if (deviceName.find("NVIDIA") != std::string::npos ||
                deviceName.find("GeForce") != std::string::npos ||
                deviceName.find("Quadro") != std::string::npos) {
                systemInfo_.hasNVENC = true;  // 假设支持，实际需要更详细检测
            }
            
            if (deviceName.find("Intel") != std::string::npos) {
                systemInfo_.hasQSV = true;    // 假设支持
            }
            
            if (deviceName.find("AMD") != std::string::npos ||
                deviceName.find("Radeon") != std::string::npos) {
                systemInfo_.hasAMF = true;    // 假设支持
            }
        }
    } catch (...) {
        // 忽略GPU检测错误
    }
    
    // 动态加载可能不存在的API
    HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
    if (kernel32) {
        createRemoteThreadProc_ = reinterpret_cast<CreateRemoteThreadProc>(
            GetProcAddress(kernel32, "CreateRemoteThread"));
    }
}

void Win7Compatibility::SetupExceptionHandler() {
    SetUnhandledExceptionFilter([](EXCEPTION_POINTERS* pExceptionInfo) -> LONG {
        Win7Compatibility::Instance().HandleCrash(pExceptionInfo);
        return EXCEPTION_EXECUTE_HANDLER;
    });
}

HANDLE Win7Compatibility::SafeCreateRemoteThread(HANDLE hProcess, 
                                                 LPSECURITY_ATTRIBUTES lpThreadAttributes,
                                                 SIZE_T dwStackSize, 
                                                 LPTHREAD_START_ROUTINE lpStartAddress,
                                                 LPVOID lpParameter, 
                                                 DWORD dwCreationFlags, 
                                                 LPDWORD lpThreadId) {
    // Windows 7兼容的远程线程创建
    if (createRemoteThreadProc_) {
        return createRemoteThreadProc_(hProcess, lpThreadAttributes, dwStackSize,
                                       lpStartAddress, lpParameter, dwCreationFlags, lpThreadId);
    }
    
    // 降级方案 - 使用标准API
    return CreateRemoteThread(hProcess, lpThreadAttributes, dwStackSize,
                              lpStartAddress, lpParameter, dwCreationFlags, lpThreadId);
}

LPVOID Win7Compatibility::SafeVirtualAllocEx(HANDLE hProcess, LPVOID lpAddress, 
                                             SIZE_T dwSize, DWORD flAllocationType, 
                                             DWORD flProtect) {
    // Windows 7特定的内存分配策略
    if (systemInfo_.isWindows7) {
        // 使用较小的块，避免大块内存分配问题
        const size_t MAX_ALLOC_SIZE = 64 * 1024 * 1024; // 64MB限制
        if (dwSize > MAX_ALLOC_SIZE) {
            SetLastError(ERROR_NOT_ENOUGH_MEMORY);
            return nullptr;
        }
    }
    
    return VirtualAllocEx(hProcess, lpAddress, dwSize, flAllocationType, flProtect);
}

BOOL Win7Compatibility::SafeWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress,
                                               LPCVOID lpBuffer, SIZE_T nSize, 
                                               SIZE_T* lpNumberOfBytesWritten) {
    // Windows 7兼容的内存写入
    if (systemInfo_.isWindows7 && nSize > 0) {
        // 分块写入大数据，避免一次性写入过多数据
        const size_t CHUNK_SIZE = 4096; // 4KB块
        if (nSize > CHUNK_SIZE) {
            SIZE_T totalWritten = 0;
            const uint8_t* buffer = static_cast<const uint8_t*>(lpBuffer);
            
            while (totalWritten < nSize) {
                SIZE_T chunkSize = min(CHUNK_SIZE, nSize - totalWritten);
                SIZE_T written = 0;
                
                if (!WriteProcessMemory(hProcess, 
                                        static_cast<uint8_t*>(lpBaseAddress) + totalWritten,
                                        buffer + totalWritten, chunkSize, &written)) {
                    if (lpNumberOfBytesWritten) {
                        *lpNumberOfBytesWritten = totalWritten;
                    }
                    return FALSE;
                }
                
                totalWritten += written;
                if (written != chunkSize) break;
            }
            
            if (lpNumberOfBytesWritten) {
                *lpNumberOfBytesWritten = totalWritten;
            }
            return totalWritten == nSize ? TRUE : FALSE;
        }
    }
    
    return WriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
}

void* Win7Compatibility::AllocateAligned(size_t size, size_t alignment) {
    if (systemInfo_.isWindows7) {
        // Windows 7使用更保守的内存分配
        return _aligned_malloc(size, alignment);
    } else {
        return _aligned_malloc(size, alignment);
    }
}

void Win7Compatibility::FreeAligned(void* ptr) {
    if (ptr) {
        _aligned_free(ptr);
    }
}

std::string Win7Compatibility::GetLastErrorString() const {
    DWORD errorCode = GetLastError();
    if (errorCode == 0) return "No error";
    
    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer, 0, NULL);
    
    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);
    
    std::ostringstream oss;
    oss << "Error " << errorCode << ": " << message;
    return oss.str();
}

void Win7Compatibility::HandleCrash(EXCEPTION_POINTERS* pExceptionInfo) {
    // 简单的崩溃处理 - 记录信息
    DWORD exceptionCode = pExceptionInfo->ExceptionRecord->ExceptionCode;
    PVOID exceptionAddress = pExceptionInfo->ExceptionRecord->ExceptionAddress;
    
    // 这里可以添加更详细的崩溃日志记录
    char crashInfo[512];
    sprintf_s(crashInfo, sizeof(crashInfo), 
              "Crash: Code=0x%08X, Address=0x%p, Process=%lu",
              exceptionCode, exceptionAddress, GetCurrentProcessId());
    
    OutputDebugStringA(crashInfo);
    
    // 尝试写入日志文件
    FILE* crashLog = nullptr;
    if (fopen_s(&crashLog, "streamcapture_crash.log", "a") == 0 && crashLog) {
        fprintf(crashLog, "[%lu] %s\n", GetTickCount(), crashInfo);
        fclose(crashLog);
    }
}

bool Win7Compatibility::IsLowMemory() const {
    MEMORYSTATUSEX memStatus = {0};
    memStatus.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memStatus)) {
        return memStatus.dwMemoryLoad > 80; // 内存使用率超过80%
    }
    return false;
}

size_t Win7Compatibility::GetAvailableMemory() const {
    MEMORYSTATUSEX memStatus = {0};
    memStatus.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memStatus)) {
        return static_cast<size_t>(memStatus.ullAvailPhys / (1024 * 1024));
    }
    return 0;
}

} // namespace StreamCapture