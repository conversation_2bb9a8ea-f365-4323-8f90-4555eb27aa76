#pragma once

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <functional>

namespace StreamCapture
{

    // 媒体服务器管理器
    class MediaServerManager
    {
    public:
        MediaServerManager();
        ~MediaServerManager();

        // 启动媒体服务器
        bool StartServer(const std::string &executablePath = "mediamtx\\mediamtx.exe");

        // 停止媒体服务器
        void StopServer();

        // 检查服务器是否运行
        bool IsServerRunning() const;

        // 获取服务器状态
        std::string GetServerStatus() const;

        // 设置服务器配置
        bool SetServerConfig(const std::string &configPath);

        // 获取推流 URL
        std::string GetRTMPUrl(const std::string &streamKey = "stream1") const;
        std::string GetRTSPUrl(const std::string &streamKey = "stream1") const;
        std::string GetHLSUrl(const std::string &streamKey = "stream1") const;
        std::string GetWebRTCUrl(const std::string &streamKey = "stream1") const;

        // 设置状态回调
        void SetStatusCallback(std::function<void(bool isRunning, const std::string &status)> callback);

        // 获取服务器端口配置
        struct ServerPorts
        {
            int rtmpPort = 1935;
            int rtspPort = 8554;
            int hlsPort = 8888;
            int webrtcPort = 8889;
            int apiPort = 9997;
        };

        const ServerPorts &GetServerPorts() const { return ports_; }
        void SetServerPorts(const ServerPorts &ports) { ports_ = ports; }

        // 检查端口是否可用
        bool IsPortAvailable(int port) const;

        // 等待服务器启动
        bool WaitForServerReady(int timeoutSeconds = 30);

        // 获取服务器进程 ID
        uint32_t GetServerProcessId() const;

    private:
        std::string serverExecutablePath_;
        std::string serverConfigPath_;
        ServerPorts ports_;

        std::atomic<bool> serverRunning_;
        std::atomic<uint32_t> serverProcessId_;
        std::atomic<bool> externalProcess_; // 标记是否为外部进程

        std::thread monitorThread_;
        std::atomic<bool> monitoring_;

        std::function<void(bool, const std::string &)> statusCallback_;

        // 内部方法
        bool LaunchServerProcess();
        void TerminateServerProcess();
        bool TerminateChildProcesses(uint32_t parentProcessId) const;
        bool CheckServerHealth();
        void MonitorServerProcess();

        // 配置文件生成
        bool GenerateDefaultConfig();
        std::string GetDefaultConfigContent() const;

        // 网络检查
        bool TestServerConnection(const std::string &url, int timeoutMs = 5000);
        bool IsHostReachable(const std::string &host, int port, int timeoutMs = 5000) const;

        // 进程管理
        bool IsProcessRunning(uint32_t processId) const;
        bool KillProcess(uint32_t processId) const;
        uint32_t FindProcessByName(const std::string &processName) const;
        bool IsMediaMTXProcessRunning() const;

        // 日志和错误处理
        void LogServerEvent(const std::string &event, const std::string &details = "");
        std::string GetLastSystemError() const;
    };

    // 媒体服务器配置生成器
    class MediaServerConfigGenerator
    {
    public:
        struct Config
        {
            // RTMP 配置
            bool enableRTMP = true;
            int rtmpPort = 1935;

            // RTSP 配置
            bool enableRTSP = true;
            int rtspPort = 8554;

            // HLS 配置
            bool enableHLS = true;
            int hlsPort = 8888;
            std::string hlsSegmentDuration = "1s";
            int hlsSegmentCount = 3;

            // WebRTC 配置
            bool enableWebRTC = true;
            int webrtcPort = 8889;

            // API 配置
            bool enableAPI = true;
            int apiPort = 9997;

            // 日志配置
            std::string logLevel = "info";
            std::string logDestinations = "stdout";

            // 性能配置
            int readTimeout = 10;
            int writeTimeout = 10;
            int readBufferCount = 512;

            // 录制配置
            bool enableRecording = false;
            std::string recordingPath = "recordings";

            // 认证配置
            bool enableAuth = false;
            std::string authMethod = "internal";
        };

        static std::string GenerateConfig(const Config &config);
        static bool SaveConfigToFile(const Config &config, const std::string &filePath);
        static Config LoadConfigFromFile(const std::string &filePath);

    private:
        static std::string GenerateRTMPSection(const Config &config);
        static std::string GenerateRTSPSection(const Config &config);
        static std::string GenerateHLSSection(const Config &config);
        static std::string GenerateWebRTCSection(const Config &config);
        static std::string GenerateAPISection(const Config &config);
        static std::string GenerateLogSection(const Config &config);
        static std::string GeneratePathsSection(const Config &config);
    };

    // 媒体服务器客户端（用于测试连接）
    class MediaServerClient
    {
    public:
        MediaServerClient();
        ~MediaServerClient();

        // 测试 RTMP 连接
        bool TestRTMPConnection(const std::string &url, int timeoutMs = 5000);

        // 测试 RTSP 连接
        bool TestRTSPConnection(const std::string &url, int timeoutMs = 5000);

        // 获取服务器信息
        bool GetServerInfo(const std::string &apiUrl, std::string &info);

        // 获取流列表
        bool GetStreamList(const std::string &apiUrl, std::vector<std::string> &streams);

        // 检查流是否存在
        bool IsStreamActive(const std::string &apiUrl, const std::string &streamName);

    private:
        // HTTP 客户端功能
        bool HttpGet(const std::string &url, std::string &response, int timeoutMs = 5000);

        // 网络工具
        bool IsHostReachable(const std::string &host, int port, int timeoutMs = 5000);
    };

} // namespace StreamCapture
