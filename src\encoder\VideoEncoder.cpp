#include "VideoEncoder.h"
#include "Win7Compatibility.h"
#include "StreamCaptureController.h"
#include "FFmpegEncoder.h"
#include <chrono>
#include <algorithm>
#include <vector>
#include <windows.h>

namespace StreamCapture
{

    // VideoEncoder静态方法实现
    bool VideoEncoder::IsHardwareSupported(const std::string &type)
    {
        if (type == "nvenc")
        {
            return NVENCEncoder::IsSupported();
        }
        else if (type == "qsv")
        {
            return QSVEncoder::IsSupported();
        }
        else if (type == "amf")
        {
            // AMF支持检测（这里简化实现）
            return false;
        }
        return false;
    }

    std::vector<std::string> VideoEncoder::GetSupportedCodecs()
    {
        std::vector<std::string> codecs;

        // FFmpeg软件编码器总是可用
        codecs.push_back("h264");
        codecs.push_back("h265");

        // 硬件编码器
        if (IsHardwareSupported("nvenc"))
        {
            codecs.push_back("h264_nvenc");
            codecs.push_back("h265_nvenc");
        }

        if (IsHardwareSupported("qsv"))
        {
            codecs.push_back("h264_qsv");
            codecs.push_back("h265_qsv");
        }

        return codecs;
    }

    // NVENCEncoder简化实现（实际需要NVENC SDK）
    NVENCEncoder::NVENCEncoder() : initialized_(false), nvencSession_(nullptr)
    {
        state_ = EncoderState::Idle;
    }

    NVENCEncoder::~NVENCEncoder()
    {
        Shutdown();
    }

    bool NVENCEncoder::IsSupported()
    {
        // 使用FFmpeg的硬件设备检测机制来检查NVENC支持
        // 这样可以确保与FFmpeg编码器的兼容性
        try
        {
            // 初始化FFmpeg（如果尚未初始化）
            static bool initialized = false;
            if (!initialized)
            {
                avformat_network_init();
                initialized = true;
            }

            // 检查是否存在nvenc硬件设备类型
            AVHWDeviceType type = av_hwdevice_find_type_by_name("cuda");
            if (type == AV_HWDEVICE_TYPE_NONE)
            {
                type = av_hwdevice_find_type_by_name("dxva2");
            }

            if (type != AV_HWDEVICE_TYPE_NONE)
            {
                // 尝试创建硬件设备上下文来验证支持
                AVBufferRef *deviceContext = nullptr;
                int ret = av_hwdevice_ctx_create(&deviceContext, type, nullptr, nullptr, 0);
                if (ret >= 0)
                {
                    // 成功创建硬件设备上下文，说明硬件编码可用
                    av_buffer_unref(&deviceContext);
                    return true;
                }
            }

            // 回退到旧的检测方法
            DISPLAY_DEVICE dd = {0};
            dd.cb = sizeof(DISPLAY_DEVICE);

            if (EnumDisplayDevices(NULL, 0, &dd, 0))
            {
                // 正确处理宽字符到多字节字符的转换
                std::string deviceName;
#ifdef UNICODE
                int bufferSize = WideCharToMultiByte(CP_UTF8, 0, dd.DeviceString, -1, nullptr, 0, nullptr, nullptr);
                if (bufferSize > 0)
                {
                    std::vector<char> buffer(bufferSize);
                    WideCharToMultiByte(CP_UTF8, 0, dd.DeviceString, -1, buffer.data(), bufferSize, nullptr, nullptr);
                    deviceName = std::string(buffer.data());
                }
#else
                deviceName = std::string(dd.DeviceString);
#endif
                return (deviceName.find("NVIDIA") != std::string::npos ||
                        deviceName.find("GeForce") != std::string::npos ||
                        deviceName.find("Quadro") != std::string::npos);
            }
        }
        catch (...)
        {
            // 如果FFmpeg方法失败，回退到旧的检测方法
            DISPLAY_DEVICE dd = {0};
            dd.cb = sizeof(DISPLAY_DEVICE);

            if (EnumDisplayDevices(NULL, 0, &dd, 0))
            {
                // 正确处理宽字符到多字节字符的转换
                std::string deviceName;
#ifdef UNICODE
                int bufferSize = WideCharToMultiByte(CP_UTF8, 0, dd.DeviceString, -1, nullptr, 0, nullptr, nullptr);
                if (bufferSize > 0)
                {
                    std::vector<char> buffer(bufferSize);
                    WideCharToMultiByte(CP_UTF8, 0, dd.DeviceString, -1, buffer.data(), bufferSize, nullptr, nullptr);
                    deviceName = std::string(buffer.data());
                }
#else
                deviceName = std::string(dd.DeviceString);
#endif
                return (deviceName.find("NVIDIA") != std::string::npos ||
                        deviceName.find("GeForce") != std::string::npos ||
                        deviceName.find("Quadro") != std::string::npos);
            }
        }

        return false;
    }

    bool NVENCEncoder::Initialize(const Config &config)
    {
        // 简化实现 - 实际需要NVENC API调用
        config_ = config;
        initialized_ = SetupNVENC();
        state_ = initialized_ ? EncoderState::Idle : EncoderState::Error;
        return initialized_;
    }

    void NVENCEncoder::Shutdown()
    {
        if (initialized_)
        {
            CleanupNVENC();
            initialized_ = false;
        }
        state_ = EncoderState::Idle;
    }

    bool NVENCEncoder::SetupNVENC()
    {
        // 这里应该包含NVENC初始化代码
        // 由于需要NVENC SDK，这里返回false表示不可用
        return false;
    }

    void NVENCEncoder::CleanupNVENC()
    {
        // NVENC清理代码
    }

    bool NVENCEncoder::EncodeFrame(const FrameData &frame)
    {
        // 避免未引用参数警告
        (void)frame;

        // 检查编码器是否已初始化
        if (!initialized_)
        {
            return false;
        }

        // 实际的NVENC编码实现
        // 由于需要完整的NVENC SDK，这里提供一个基础框架
        // 在实际应用中，这里会包含完整的NVENC编码逻辑

        // 更新统计信息
        stats_.inputFrames++;
        stats_.totalBytes += frame.size;

        // 模拟编码过程
        auto packet = std::make_unique<EncodedPacket>();
        if (packet)
        {
            packet->size = frame.size / 2; // 模拟压缩
            packet->data = new uint8_t[packet->size];
            packet->pts = frame.timestamp;
            packet->dts = frame.timestamp;
            packet->timestamp = frame.timestamp;
            packet->isKeyFrame = (frame.frameIndex % 30 == 0); // 每30帧一个关键帧

            // 添加到输出队列
            {
                std::lock_guard<std::mutex> lock(packetMutex_);
                packetQueue_.push(std::move(packet));
                stats_.outputPackets++; // 修复：使用正确的统计字段
            }

            return true;
        }

        return false;
    }

    std::unique_ptr<EncodedPacket> NVENCEncoder::GetEncodedPacket(uint32_t timeoutMs)
    {
        // 避免未引用参数警告
        (void)timeoutMs;

        std::lock_guard<std::mutex> lock(packetMutex_);

        if (!packetQueue_.empty())
        {
            auto packet = std::move(packetQueue_.front());
            packetQueue_.pop();
            return packet;
        }

        return nullptr;
    }

    bool NVENCEncoder::Flush()
    {
        // 刷新编码器缓冲区
        std::lock_guard<std::mutex> lock(packetMutex_);
        while (!packetQueue_.empty())
        {
            packetQueue_.pop();
        }
        return true;
    }

    bool NVENCEncoder::SetBitrate(uint32_t bitrate)
    {
        // 设置编码器比特率
        config_.bitrate = bitrate;
        return true;
    }

    bool NVENCEncoder::ForceKeyFrame()
    {
        // 强制下一个帧为关键帧
        return true;
    }

    VideoEncoder::Stats NVENCEncoder::GetStats() const
    {
        return stats_;
    }

    // QSVEncoder简化实现
    QSVEncoder::QSVEncoder() : initialized_(false), qsvSession_(nullptr)
    {
        state_ = EncoderState::Idle;
    }

    QSVEncoder::~QSVEncoder()
    {
        Shutdown();
    }

    bool QSVEncoder::IsSupported()
    {
        // 简化的Intel GPU检测
        DISPLAY_DEVICE dd = {0};
        dd.cb = sizeof(DISPLAY_DEVICE);

        if (EnumDisplayDevices(NULL, 0, &dd, 0))
        {
            // 正确处理宽字符到多字节字符的转换
            std::string deviceName;
#ifdef UNICODE
            int bufferSize = WideCharToMultiByte(CP_UTF8, 0, dd.DeviceString, -1, nullptr, 0, nullptr, nullptr);
            if (bufferSize > 0)
            {
                std::vector<char> buffer(bufferSize);
                WideCharToMultiByte(CP_UTF8, 0, dd.DeviceString, -1, buffer.data(), bufferSize, nullptr, nullptr);
                deviceName = std::string(buffer.data());
            }
#else
            deviceName = dd.DeviceString;
#endif
            return deviceName.find("Intel") != std::string::npos;
        }

        return false;
    }

    bool QSVEncoder::Initialize(const Config &config)
    {
        config_ = config;
        initialized_ = SetupQSV();
        state_ = initialized_ ? EncoderState::Idle : EncoderState::Error;
        return initialized_;
    }

    void QSVEncoder::Shutdown()
    {
        if (initialized_)
        {
            CleanupQSV();
            initialized_ = false;
        }
        state_ = EncoderState::Idle;
    }

    bool QSVEncoder::SetupQSV()
    {
        // QSV初始化代码（需要Intel Media SDK）
        return false;
    }

    void QSVEncoder::CleanupQSV()
    {
        // QSV清理代码
    }

    bool QSVEncoder::EncodeFrame(const FrameData &frame)
    {
        // 避免未引用参数警告
        (void)frame;

        // 检查编码器是否已初始化
        if (!initialized_)
        {
            return false;
        }

        // 实际的QSV编码实现
        // 由于需要完整的Intel Media SDK，这里提供一个基础框架
        // 在实际应用中，这里会包含完整的QSV编码逻辑

        // 更新统计信息
        stats_.inputFrames++;
        stats_.totalBytes += frame.size;

        // 模拟编码过程
        auto packet = std::make_unique<EncodedPacket>();
        if (packet)
        {
            packet->size = frame.size / 2; // 模拟压缩
            packet->data = new uint8_t[packet->size];
            packet->pts = frame.timestamp;
            packet->dts = frame.timestamp;
            packet->timestamp = frame.timestamp;
            packet->isKeyFrame = (frame.frameIndex % 30 == 0); // 每30帧一个关键帧

            // 添加到输出队列
            {
                std::lock_guard<std::mutex> lock(packetMutex_);
                packetQueue_.push(std::move(packet));
                stats_.outputPackets++; // 修复：使用正确的统计字段
            }

            return true;
        }

        return false;
    }

    std::unique_ptr<EncodedPacket> QSVEncoder::GetEncodedPacket(uint32_t timeoutMs)
    {
        // 避免未引用参数警告
        (void)timeoutMs;

        std::lock_guard<std::mutex> lock(packetMutex_);

        if (!packetQueue_.empty())
        {
            auto packet = std::move(packetQueue_.front());
            packetQueue_.pop();
            return packet;
        }

        return nullptr;
    }

    bool QSVEncoder::Flush()
    {
        // 刷新编码器缓冲区
        std::lock_guard<std::mutex> lock(packetMutex_);
        while (!packetQueue_.empty())
        {
            packetQueue_.pop();
        }
        return true;
    }

    bool QSVEncoder::SetBitrate(uint32_t bitrate)
    {
        // 设置编码器比特率
        config_.bitrate = bitrate;
        return true;
    }

    bool QSVEncoder::ForceKeyFrame()
    {
        // 强制下一个帧为关键帧
        return true;
    }

    VideoEncoder::Stats QSVEncoder::GetStats() const
    {
        return stats_;
    }

    // EncoderManager实现
    EncoderManager::EncoderManager()
    {
    }

    EncoderManager::~EncoderManager()
    {
        Shutdown();
    }

    bool EncoderManager::Initialize()
    {
        DetectHardwareCapabilities();
        return true;
    }

    void EncoderManager::Shutdown()
    {
        // 清理资源
    }

    std::unique_ptr<VideoEncoder> EncoderManager::CreateEncoder(const EncoderConfig &config)
    {
        if (config.type == "hardware")
        {
            // 按优先级尝试硬件编码器
            if (hwCapability_.nvencAvailable && (config.codec == "h264" || config.codec == "h265"))
            {
                auto encoder = std::make_unique<NVENCEncoder>();
                VideoEncoder::Config encoderConfig;
                encoderConfig.width = 1920; // 从config中获取
                encoderConfig.height = 1080;
                encoderConfig.fps = 30;
                encoderConfig.bitrate = config.bitrate;
                encoderConfig.codec = config.codec;

                if (encoder->Initialize(encoderConfig))
                {
                    return std::move(encoder);
                }
            }

            if (hwCapability_.qsvAvailable && (config.codec == "h264" || config.codec == "h265"))
            {
                auto encoder = std::make_unique<QSVEncoder>();
                VideoEncoder::Config encoderConfig;
                encoderConfig.width = 1920;
                encoderConfig.height = 1080;
                encoderConfig.fps = 30;
                encoderConfig.bitrate = config.bitrate;
                encoderConfig.codec = config.codec;

                if (encoder->Initialize(encoderConfig))
                {
                    return std::move(encoder);
                }
            }

            // 硬件编码失败，回退到软件编码
        }

        // 软件编码器
        auto encoder = std::make_unique<FFmpegEncoder>();
        VideoEncoder::Config encoderConfig;
        encoderConfig.width = 1920; // 应该从实际配置获取
        encoderConfig.height = 1080;
        encoderConfig.fps = 30;
        encoderConfig.bitrate = config.bitrate;
        encoderConfig.codec = config.codec;
        encoderConfig.preset = config.preset;
        encoderConfig.profile = config.profile;

        if (encoder->Initialize(encoderConfig))
        {
            return std::move(encoder);
        }

        return nullptr;
    }

    std::unique_ptr<VideoEncoder> EncoderManager::CreateBestEncoder(const EncoderConfig &config)
    {
        // 首先尝试硬件编码
        EncoderConfig hwConfig = config;
        hwConfig.type = "hardware";

        auto encoder = CreateEncoder(hwConfig);
        if (encoder)
        {
            return encoder;
        }

        // 回退到软件编码
        EncoderConfig swConfig = config;
        swConfig.type = "software";

        return CreateEncoder(swConfig);
    }

    std::vector<EncoderManager::EncoderCapability> EncoderManager::GetAvailableEncoders()
    {
        std::vector<EncoderCapability> capabilities;

        // FFmpeg软件编码器
        EncoderCapability ffmpeg;
        ffmpeg.name = "FFmpeg";
        ffmpeg.type = "software";
        ffmpeg.available = true;
        ffmpeg.supportedCodecs = {"h264", "h265"};
        ffmpeg.maxWidth = 4096;
        ffmpeg.maxHeight = 4096;
        ffmpeg.maxBitrate = 100000; // 100 Mbps
        capabilities.push_back(ffmpeg);

        // NVENC
        if (hwCapability_.nvencAvailable)
        {
            EncoderCapability nvenc;
            nvenc.name = "NVENC";
            nvenc.type = "hardware";
            nvenc.available = true;
            nvenc.supportedCodecs = {"h264", "h265"};
            nvenc.maxWidth = 4096;
            nvenc.maxHeight = 4096;
            nvenc.maxBitrate = 50000; // 50 Mbps
            capabilities.push_back(nvenc);
        }

        // QSV
        if (hwCapability_.qsvAvailable)
        {
            EncoderCapability qsv;
            qsv.name = "Intel QSV";
            qsv.type = "hardware";
            qsv.available = true;
            qsv.supportedCodecs = {"h264", "h265"};
            qsv.maxWidth = 4096;
            qsv.maxHeight = 4096;
            qsv.maxBitrate = 30000; // 30 Mbps
            capabilities.push_back(qsv);
        }

        return capabilities;
    }

    bool EncoderManager::IsHardwareEncodingAvailable()
    {
        return hwCapability_.nvencAvailable || hwCapability_.qsvAvailable || hwCapability_.amfAvailable;
    }

    void EncoderManager::DetectHardwareCapabilities()
    {
        // 使用FFmpegUtils来检测硬件编码支持
        hwCapability_.nvencAvailable = FFmpegUtils::IsHardwareTypeSupported("cuda") ||
                                       FFmpegUtils::IsHardwareTypeSupported("dxva2") ||
                                       NVENCEncoder::IsSupported();

        hwCapability_.qsvAvailable = FFmpegUtils::IsHardwareTypeSupported("qsv") ||
                                     QSVEncoder::IsSupported();

        hwCapability_.amfAvailable = FFmpegUtils::IsHardwareTypeSupported("d3d11va") ||
                                     FFmpegUtils::IsHardwareTypeSupported("opencl") ||
                                     false; // AMF暂不支持
    }

    void EncoderManager::HandleError(const std::string &encoderName, ErrorCode code, const std::string &message)
    {
        if (errorCallback_)
        {
            ErrorInfo error;
            error.code = code;
            error.message = message;
            error.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                                  std::chrono::system_clock::now().time_since_epoch())
                                  .count();

            errorCallback_(encoderName, error);
        }
    }

    // MultiEncoder implementation
    MultiEncoder::MultiEncoder()
    {
        manager_ = std::make_unique<EncoderManager>();
    }

    MultiEncoder::~MultiEncoder()
    {
        Shutdown();
    }

    bool MultiEncoder::Initialize(const std::vector<StreamConfig> &configs)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        // 清理现有流
        Shutdown();

        // 创建新流
        for (const auto &config : configs)
        {
            if (!AddStream(config))
            {
                LOG_ERROR_F("Failed to add stream: %s", config.streamId.c_str());
                return false;
            }
        }

        return true;
    }

    bool MultiEncoder::Initialize()
    {
        // 默认初始化，不创建任何编码器
        // 编码器将在需要时动态创建
        return true;
    }

    void MultiEncoder::Shutdown()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        // 停止所有处理线程
        for (auto &stream : streams_)
        {
            if (stream->running)
            {
                stream->running = false;
                if (stream->processingThread.joinable())
                {
                    stream->processingThread.join();
                }
            }
        }

        streams_.clear();
    }

    bool MultiEncoder::EncodeFrame(const FrameData &frame)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->enabled && stream->encoder)
            {
                if (!stream->encoder->EncodeFrame(frame))
                {
                    LOG_ERROR_F("Failed to encode frame for stream: %s", stream->id.c_str());
                    success = false;
                }
            }
        }

        return success;
    }

    bool MultiEncoder::EncodeFrameToStream(const std::string &streamId, const FrameData &frame)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->enabled || !stream->encoder)
        {
            return false;
        }

        return stream->encoder->EncodeFrame(frame);
    }

    bool MultiEncoder::EncodeFrame(const std::string &encoderId, std::unique_ptr<FrameData> frame)
    {
        // 这个方法用于按编码器ID编码帧
        // 目前简化实现，直接使用第一个可用的编码器
        std::lock_guard<std::mutex> lock(mutex_);

        if (streams_.empty() || !frame)
        {
            return false;
        }

        // 查找匹配的编码器或使用第一个可用的
        for (auto &stream : streams_)
        {
            if (stream->enabled && stream->encoder)
            {
                return stream->encoder->EncodeFrame(*frame);
            }
        }

        return false;
    }

    std::unique_ptr<EncodedPacket> MultiEncoder::GetEncodedPacket(const std::string &streamId, uint32_t timeoutMs)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->encoder)
        {
            return nullptr;
        }

        return stream->encoder->GetEncodedPacket(timeoutMs);
    }

    std::vector<std::pair<std::string, std::unique_ptr<EncodedPacket>>> MultiEncoder::GetAllEncodedPackets(uint32_t timeoutMs)
    {
        std::vector<std::pair<std::string, std::unique_ptr<EncodedPacket>>> packets;
        std::lock_guard<std::mutex> lock(mutex_);

        for (auto &stream : streams_)
        {
            if (stream->enabled && stream->encoder)
            {
                auto packet = stream->encoder->GetEncodedPacket(timeoutMs);
                if (packet)
                {
                    packets.emplace_back(stream->id, std::move(packet));
                }
            }
        }

        return packets;
    }

    bool MultiEncoder::AddStream(const StreamConfig &config)
    {
        // 检查是否已存在
        if (FindStream(config.streamId))
        {
            LOG_WARNING_F("Stream already exists: %s", config.streamId.c_str());
            return false;
        }

        // 创建编码器
        auto encoder = manager_->CreateEncoder(config.encoderConfig);
        if (!encoder)
        {
            LOG_ERROR_F("Failed to create encoder for stream: %s", config.streamId.c_str());
            return false;
        }

        // 创建流信息
        auto streamInfo = std::make_unique<StreamInfo>();
        streamInfo->id = config.streamId;
        streamInfo->config = config;
        streamInfo->encoder = std::move(encoder);
        streamInfo->enabled = config.enabled;
        streamInfo->running = true;

        // 启动处理线程
        streamInfo->processingThread = std::thread(&MultiEncoder::ProcessingThread, this, streamInfo.get());

        streams_.push_back(std::move(streamInfo));

        LOG_INFO_F("Added stream: %s", config.streamId.c_str());
        return true;
    }

    bool MultiEncoder::RemoveStream(const std::string &streamId)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        if (it == streams_.end())
        {
            return false;
        }

        // 停止处理线程
        (*it)->running = false;
        if ((*it)->processingThread.joinable())
        {
            (*it)->processingThread.join();
        }

        streams_.erase(it);

        LOG_INFO_F("Removed stream: %s", streamId.c_str());
        return true;
    }

    bool MultiEncoder::EnableStream(const std::string &streamId, bool enable)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream)
        {
            return false;
        }

        stream->enabled = enable;
        LOG_INFO_F("Stream %s %s", streamId.c_str(), enable ? "enabled" : "disabled");
        return true;
    }

    std::vector<std::string> MultiEncoder::GetStreamIds() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        std::vector<std::string> ids;
        for (const auto &stream : streams_)
        {
            ids.push_back(stream->id);
        }

        return ids;
    }

    bool MultiEncoder::IsStreamEnabled(const std::string &streamId) const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        return stream ? stream->enabled : false;
    }

    VideoEncoder::Stats MultiEncoder::GetStreamStats(const std::string &streamId) const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->encoder)
        {
            return VideoEncoder::Stats{};
        }

        return stream->encoder->GetStats();
    }

    bool MultiEncoder::SetStreamBitrate(const std::string &streamId, uint32_t bitrate)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->encoder)
        {
            return false;
        }

        return stream->encoder->SetBitrate(bitrate);
    }

    bool MultiEncoder::ForceKeyFrameOnAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->enabled && stream->encoder)
            {
                if (!stream->encoder->ForceKeyFrame())
                {
                    success = false;
                }
            }
        }

        return success;
    }

    bool MultiEncoder::FlushAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->encoder)
            {
                if (!stream->encoder->Flush())
                {
                    success = false;
                }
            }
        }

        return success;
    }

    void MultiEncoder::ProcessingThread(StreamInfo *streamInfo)
    {
        while (streamInfo->running)
        {
            try
            {
                // 获取编码包并通过回调发送
                auto packet = streamInfo->encoder->GetEncodedPacket(10); // 10ms timeout
                if (packet && packetCallback_)
                {
                    packetCallback_(streamInfo->id, std::move(packet));
                }
            }
            catch (...)
            {
                LOG_ERROR_F("Exception in processing thread for stream: %s", streamInfo->id.c_str());
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    MultiEncoder::StreamInfo *MultiEncoder::FindStream(const std::string &streamId)
    {
        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        return (it != streams_.end()) ? it->get() : nullptr;
    }

    const MultiEncoder::StreamInfo *MultiEncoder::FindStream(const std::string &streamId) const
    {
        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        return (it != streams_.end()) ? it->get() : nullptr;
    }

} // namespace StreamCapture