#include "VideoEncoder.h"
#include "Win7Compatibility.h"
#include "StreamCaptureController.h"
#include "FFmpegEncoder.h"
#include <chrono>
#include <algorithm>

namespace StreamCapture
{

    // VideoEncoder静态方法实现
    bool VideoEncoder::IsHardwareSupported(const std::string &type)
    {
        if (type == "nvenc")
        {
            return NVENCEncoder::IsSupported();
        }
        else if (type == "qsv")
        {
            return QSVEncoder::IsSupported();
        }
        else if (type == "amf")
        {
            // AMF支持检测（这里简化实现）
            return false;
        }
        return false;
    }

    std::vector<std::string> VideoEncoder::GetSupportedCodecs()
    {
        std::vector<std::string> codecs;

        // FFmpeg软件编码器总是可用
        codecs.push_back("h264");
        codecs.push_back("h265");

        // 硬件编码器
        if (IsHardwareSupported("nvenc"))
        {
            codecs.push_back("h264_nvenc");
            codecs.push_back("h265_nvenc");
        }

        if (IsHardwareSupported("qsv"))
        {
            codecs.push_back("h264_qsv");
            codecs.push_back("h265_qsv");
        }

        return codecs;
    }

    // FFmpegEncoder实现
    FFmpegEncoder::FFmpegEncoder()
        : codec_(nullptr), codecContext_(nullptr), frame_(nullptr), packet_(nullptr), swsContext_(nullptr), frameIndex_(0)
    {
        state_ = EncoderState::Idle;
        QueryPerformanceFrequency(&perfFrequency_);

        // FFmpeg 4.0+ 不需要手动注册
        // 这些函数在新版本中已经被废弃
    }

    FFmpegEncoder::~FFmpegEncoder()
    {
        Shutdown();
    }

    bool FFmpegEncoder::Initialize(const Config &config)
    {
        if (IsInitialized())
        {
            return false;
        }

        config_ = config;
        state_ = EncoderState::Initializing;

        if (!SetupFFmpeg())
        {
            state_ = EncoderState::Error;
            return false;
        }

        state_ = EncoderState::Idle;
        return true;
    }

    void FFmpegEncoder::Shutdown()
    {
        if (IsInitialized())
        {
            Flush();
            CleanupFFmpeg();
        }

        state_ = EncoderState::Idle;
    }

    bool FFmpegEncoder::SetupFFmpeg()
    {
        try
        {
            // 根据配置选择编码器
            std::string codecName;
            if (config_.codec == "h264")
            {
                codecName = "libx264";
            }
            else if (config_.codec == "h265")
            {
                codecName = "libx265";
            }
            else
            {
                return false;
            }

            // 查找编码器
            codec_ = avcodec_find_encoder_by_name(codecName.c_str());
            if (!codec_)
            {
                return false;
            }

            // 创建编码器上下文
            codecContext_ = avcodec_alloc_context3(codec_);
            if (!codecContext_)
            {
                return false;
            }

            // 设置编码参数
            codecContext_->width = config_.width;
            codecContext_->height = config_.height;
            codecContext_->time_base = {1, static_cast<int>(config_.fps)};
            codecContext_->framerate = {static_cast<int>(config_.fps), 1};
            codecContext_->bit_rate = config_.bitrate * 1000; // 转换为bps
            codecContext_->pix_fmt = AV_PIX_FMT_YUV420P;
            codecContext_->gop_size = config_.keyframeInterval;
            codecContext_->max_b_frames = config_.enableBFrames ? 2 : 0;

            // Windows 7兼容性设置
            if (Win7Compatibility::Instance().IsWindows7())
            {
                // 使用更保守的设置
                codecContext_->thread_count = 1; // 单线程避免兼容性问题
                codecContext_->thread_type = 0;
            }
            else
            {
                codecContext_->thread_count = 0; // 自动检测
            }

            // 设置编码器选项
            AVDictionary *options = nullptr;

            if (config_.codec == "h264")
            {
                if (config_.preset == "speed")
                {
                    av_dict_set(&options, "preset", "ultrafast", 0);
                }
                else if (config_.preset == "balanced")
                {
                    av_dict_set(&options, "preset", "medium", 0);
                }
                else
                { // quality
                    av_dict_set(&options, "preset", "slow", 0);
                }

                av_dict_set(&options, "profile", config_.profile.c_str(), 0);
                av_dict_set(&options, "tune", "zerolatency", 0);
            }

            // 打开编码器
            int ret = avcodec_open2(codecContext_, codec_, &options);
            av_dict_free(&options);
            if (ret < 0)
            {
                return false;
            }

            // 分配帧和数据包
            frame_ = av_frame_alloc();
            if (!frame_)
            {
                return false;
            }

            frame_->format = codecContext_->pix_fmt;
            frame_->width = codecContext_->width;
            frame_->height = codecContext_->height;

            ret = av_frame_get_buffer(frame_, 32);
            if (ret < 0)
            {
                return false;
            }

            packet_ = av_packet_alloc();
            if (!packet_)
            {
                return false;
            }

            // 初始化图像转换上下文
            swsContext_ = sws_getContext(
                config_.width, config_.height, AV_PIX_FMT_RGBA,
                config_.width, config_.height, AV_PIX_FMT_YUV420P,
                SWS_BILINEAR, nullptr, nullptr, nullptr);

            if (!swsContext_)
            {
                return false;
            }

            return true;
        }
        catch (...)
        {
            return false;
        }
    }

    void FFmpegEncoder::CleanupFFmpeg()
    {
        if (swsContext_)
        {
            sws_freeContext(swsContext_);
            swsContext_ = nullptr;
        }

        if (packet_)
        {
            av_packet_free(&packet_);
        }

        if (frame_)
        {
            av_frame_free(&frame_);
        }

        if (codecContext_)
        {
            avcodec_free_context(&codecContext_);
        }

        codec_ = nullptr;
    }

    bool FFmpegEncoder::EncodeFrame(const FrameData &frame)
    {
        if (!IsInitialized() || state_ != EncoderState::Idle)
        {
            return false;
        }

        LARGE_INTEGER startTime;
        QueryPerformanceCounter(&startTime);

        state_ = EncoderState::Encoding;

        try
        {
            // 转换帧格式
            if (!ConvertFrameFormat(frame, frame_))
            {
                state_ = EncoderState::Error;
                return false;
            }

            // 设置帧时间戳
            frame_->pts = frameIndex_++;

            // 发送帧到编码器
            int ret = avcodec_send_frame(codecContext_, frame_);
            if (ret < 0)
            {
                state_ = EncoderState::Error;
                return false;
            }

            // 接收编码后的数据包
            while (ret >= 0)
            {
                ret = avcodec_receive_packet(codecContext_, packet_);
                if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
                {
                    break;
                }
                else if (ret < 0)
                {
                    state_ = EncoderState::Error;
                    return false;
                }

                // 创建编码包
                auto encodedPacket = std::make_unique<EncodedPacket>();
                encodedPacket->size = packet_->size;
                encodedPacket->data = new uint8_t[packet_->size];
                memcpy(encodedPacket->data, packet_->data, packet_->size);
                encodedPacket->timestamp = frame.timestamp;
                encodedPacket->pts = packet_->pts;
                encodedPacket->dts = packet_->dts;
                encodedPacket->isKeyFrame = (packet_->flags & AV_PKT_FLAG_KEY) != 0;

                // 添加到队列
                {
                    std::lock_guard<std::mutex> lock(packetMutex_);
                    packetQueue_.push(std::move(encodedPacket));
                }
                packetCondition_.notify_one();

                av_packet_unref(packet_);
                stats_.outputPackets++;
            }

            stats_.inputFrames++;

            // 计算编码时间
            LARGE_INTEGER endTime;
            QueryPerformanceCounter(&endTime);
            float encodeTime = static_cast<float>(endTime.QuadPart - startTime.QuadPart) /
                               perfFrequency_.QuadPart * 1000.0f;
            stats_.avgEncodeTime = (stats_.avgEncodeTime * 0.9f) + (encodeTime * 0.1f);

            state_ = EncoderState::Idle;
            return true;
        }
        catch (...)
        {
            state_ = EncoderState::Error;
            return false;
        }
    }

    std::unique_ptr<EncodedPacket> FFmpegEncoder::GetEncodedPacket(uint32_t timeoutMs)
    {
        std::unique_lock<std::mutex> lock(packetMutex_);

        if (timeoutMs == 0)
        {
            if (packetQueue_.empty())
            {
                return nullptr;
            }
        }
        else
        {
            if (!packetCondition_.wait_for(lock, std::chrono::milliseconds(timeoutMs),
                                           [this]
                                           { return !packetQueue_.empty(); }))
            {
                return nullptr;
            }
        }

        auto packet = std::move(packetQueue_.front());
        packetQueue_.pop();

        return packet;
    }

    bool FFmpegEncoder::Flush()
    {
        if (!IsInitialized())
        {
            return false;
        }

        // 发送空帧以刷新编码器
        int ret = avcodec_send_frame(codecContext_, nullptr);
        if (ret < 0)
        {
            return false;
        }

        // 接收剩余的数据包
        while (ret >= 0)
        {
            ret = avcodec_receive_packet(codecContext_, packet_);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
            {
                break;
            }
            else if (ret < 0)
            {
                return false;
            }

            auto encodedPacket = std::make_unique<EncodedPacket>();
            encodedPacket->size = packet_->size;
            encodedPacket->data = new uint8_t[packet_->size];
            memcpy(encodedPacket->data, packet_->data, packet_->size);
            encodedPacket->pts = packet_->pts;
            encodedPacket->dts = packet_->dts;
            encodedPacket->isKeyFrame = (packet_->flags & AV_PKT_FLAG_KEY) != 0;

            {
                std::lock_guard<std::mutex> lock(packetMutex_);
                packetQueue_.push(std::move(encodedPacket));
            }
            packetCondition_.notify_one();

            av_packet_unref(packet_);
        }

        return true;
    }

    bool FFmpegEncoder::SetBitrate(uint32_t bitrate)
    {
        if (!IsInitialized())
        {
            return false;
        }

        config_.bitrate = bitrate;
        codecContext_->bit_rate = bitrate * 1000;

        return true;
    }

    bool FFmpegEncoder::ForceKeyFrame()
    {
        if (!IsInitialized())
        {
            return false;
        }

        // 在下一帧强制关键帧 - 使用新的API
        if (frame_)
        {
            frame_->pict_type = AV_PICTURE_TYPE_I;
        }

        return true;
    }

    VideoEncoder::Stats FFmpegEncoder::GetStats() const
    {
        Stats currentStats = stats_;

        // 计算平均比特率
        if (stats_.outputPackets > 0)
        {
            currentStats.avgBitrate = (stats_.totalBytes * 8.0f) / (stats_.outputPackets / static_cast<float>(config_.fps)) / 1000.0f;
        }

        return currentStats;
    }

    bool FFmpegEncoder::ConvertFrameFormat(const FrameData &input, AVFrame *output)
    {
        if (!swsContext_ || !output)
        {
            return false;
        }

        // 设置输入数据
        const uint8_t *srcData[1] = {input.data};
        int srcStride[1] = {static_cast<int>(input.stride)};

        // 转换
        int ret = sws_scale(swsContext_, srcData, srcStride, 0, input.height,
                            output->data, output->linesize);

        return ret == static_cast<int>(input.height);
    }

    // NVENCEncoder简化实现（实际需要NVENC SDK）
    NVENCEncoder::NVENCEncoder() : initialized_(false), nvencSession_(nullptr)
    {
        state_ = EncoderState::Idle;
    }

    NVENCEncoder::~NVENCEncoder()
    {
        Shutdown();
    }

    bool NVENCEncoder::IsSupported()
    {
        // 简化的检测 - 实际应该检查NVENC库和驱动
        DISPLAY_DEVICE dd = {0};
        dd.cb = sizeof(DISPLAY_DEVICE);

        if (EnumDisplayDevices(NULL, 0, &dd, 0))
        {
            std::string deviceName = dd.DeviceString;
            return (deviceName.find("NVIDIA") != std::string::npos ||
                    deviceName.find("GeForce") != std::string::npos ||
                    deviceName.find("Quadro") != std::string::npos);
        }

        return false;
    }

    bool NVENCEncoder::Initialize(const Config &config)
    {
        // 简化实现 - 实际需要NVENC API调用
        config_ = config;
        initialized_ = SetupNVENC();
        state_ = initialized_ ? EncoderState::Idle : EncoderState::Error;
        return initialized_;
    }

    void NVENCEncoder::Shutdown()
    {
        if (initialized_)
        {
            CleanupNVENC();
            initialized_ = false;
        }
        state_ = EncoderState::Idle;
    }

    bool NVENCEncoder::SetupNVENC()
    {
        // 这里应该包含NVENC初始化代码
        // 由于需要NVENC SDK，这里返回false表示不可用
        return false;
    }

    void NVENCEncoder::CleanupNVENC()
    {
        // NVENC清理代码
    }

    bool NVENCEncoder::EncodeFrame(const FrameData &frame)
    {
        // NVENC编码实现
        return false;
    }

    std::unique_ptr<EncodedPacket> NVENCEncoder::GetEncodedPacket(uint32_t timeoutMs)
    {
        return nullptr;
    }

    bool NVENCEncoder::Flush() { return false; }
    bool NVENCEncoder::SetBitrate(uint32_t bitrate) { return false; }
    bool NVENCEncoder::ForceKeyFrame() { return false; }

    VideoEncoder::Stats NVENCEncoder::GetStats() const
    {
        return stats_;
    }

    // QSVEncoder简化实现
    QSVEncoder::QSVEncoder() : initialized_(false), qsvSession_(nullptr)
    {
        state_ = EncoderState::Idle;
    }

    QSVEncoder::~QSVEncoder()
    {
        Shutdown();
    }

    bool QSVEncoder::IsSupported()
    {
        // 简化的Intel GPU检测
        DISPLAY_DEVICE dd = {0};
        dd.cb = sizeof(DISPLAY_DEVICE);

        if (EnumDisplayDevices(NULL, 0, &dd, 0))
        {
            std::string deviceName = dd.DeviceString;
            return deviceName.find("Intel") != std::string::npos;
        }

        return false;
    }

    bool QSVEncoder::Initialize(const Config &config)
    {
        config_ = config;
        initialized_ = SetupQSV();
        state_ = initialized_ ? EncoderState::Idle : EncoderState::Error;
        return initialized_;
    }

    void QSVEncoder::Shutdown()
    {
        if (initialized_)
        {
            CleanupQSV();
            initialized_ = false;
        }
        state_ = EncoderState::Idle;
    }

    bool QSVEncoder::SetupQSV()
    {
        // QSV初始化代码（需要Intel Media SDK）
        return false;
    }

    void QSVEncoder::CleanupQSV()
    {
        // QSV清理代码
    }

    bool QSVEncoder::EncodeFrame(const FrameData &frame) { return false; }
    std::unique_ptr<EncodedPacket> QSVEncoder::GetEncodedPacket(uint32_t timeoutMs) { return nullptr; }
    bool QSVEncoder::Flush() { return false; }
    bool QSVEncoder::SetBitrate(uint32_t bitrate) { return false; }
    bool QSVEncoder::ForceKeyFrame() { return false; }

    VideoEncoder::Stats QSVEncoder::GetStats() const
    {
        return stats_;
    }

    // EncoderManager实现
    EncoderManager::EncoderManager()
    {
    }

    EncoderManager::~EncoderManager()
    {
        Shutdown();
    }

    bool EncoderManager::Initialize()
    {
        DetectHardwareCapabilities();
        return true;
    }

    void EncoderManager::Shutdown()
    {
        // 清理资源
    }

    std::unique_ptr<VideoEncoder> EncoderManager::CreateEncoder(const EncoderConfig &config)
    {
        if (config.type == "hardware")
        {
            // 按优先级尝试硬件编码器
            if (hwCapability_.nvencAvailable && (config.codec == "h264" || config.codec == "h265"))
            {
                auto encoder = std::make_unique<NVENCEncoder>();
                VideoEncoder::Config encoderConfig;
                encoderConfig.width = 1920; // 从config中获取
                encoderConfig.height = 1080;
                encoderConfig.fps = 30;
                encoderConfig.bitrate = config.bitrate;
                encoderConfig.codec = config.codec;

                if (encoder->Initialize(encoderConfig))
                {
                    return std::move(encoder);
                }
            }

            if (hwCapability_.qsvAvailable && (config.codec == "h264" || config.codec == "h265"))
            {
                auto encoder = std::make_unique<QSVEncoder>();
                VideoEncoder::Config encoderConfig;
                encoderConfig.width = 1920;
                encoderConfig.height = 1080;
                encoderConfig.fps = 30;
                encoderConfig.bitrate = config.bitrate;
                encoderConfig.codec = config.codec;

                if (encoder->Initialize(encoderConfig))
                {
                    return std::move(encoder);
                }
            }

            // 硬件编码失败，回退到软件编码
        }

        // 软件编码器
        auto encoder = std::make_unique<FFmpegEncoder>();
        VideoEncoder::Config encoderConfig;
        encoderConfig.width = 1920; // 应该从实际配置获取
        encoderConfig.height = 1080;
        encoderConfig.fps = 30;
        encoderConfig.bitrate = config.bitrate;
        encoderConfig.codec = config.codec;
        encoderConfig.preset = config.preset;
        encoderConfig.profile = config.profile;

        if (encoder->Initialize(encoderConfig))
        {
            return std::move(encoder);
        }

        return nullptr;
    }

    std::unique_ptr<VideoEncoder> EncoderManager::CreateBestEncoder(const EncoderConfig &config)
    {
        // 首先尝试硬件编码
        EncoderConfig hwConfig = config;
        hwConfig.type = "hardware";

        auto encoder = CreateEncoder(hwConfig);
        if (encoder)
        {
            return encoder;
        }

        // 回退到软件编码
        EncoderConfig swConfig = config;
        swConfig.type = "software";

        return CreateEncoder(swConfig);
    }

    std::vector<EncoderManager::EncoderCapability> EncoderManager::GetAvailableEncoders()
    {
        std::vector<EncoderCapability> capabilities;

        // FFmpeg软件编码器
        EncoderCapability ffmpeg;
        ffmpeg.name = "FFmpeg";
        ffmpeg.type = "software";
        ffmpeg.available = true;
        ffmpeg.supportedCodecs = {"h264", "h265"};
        ffmpeg.maxWidth = 4096;
        ffmpeg.maxHeight = 4096;
        ffmpeg.maxBitrate = 100000; // 100 Mbps
        capabilities.push_back(ffmpeg);

        // NVENC
        if (hwCapability_.nvencAvailable)
        {
            EncoderCapability nvenc;
            nvenc.name = "NVENC";
            nvenc.type = "hardware";
            nvenc.available = true;
            nvenc.supportedCodecs = {"h264", "h265"};
            nvenc.maxWidth = 4096;
            nvenc.maxHeight = 4096;
            nvenc.maxBitrate = 50000; // 50 Mbps
            capabilities.push_back(nvenc);
        }

        // QSV
        if (hwCapability_.qsvAvailable)
        {
            EncoderCapability qsv;
            qsv.name = "Intel QSV";
            qsv.type = "hardware";
            qsv.available = true;
            qsv.supportedCodecs = {"h264", "h265"};
            qsv.maxWidth = 4096;
            qsv.maxHeight = 4096;
            qsv.maxBitrate = 30000; // 30 Mbps
            capabilities.push_back(qsv);
        }

        return capabilities;
    }

    bool EncoderManager::IsHardwareEncodingAvailable()
    {
        return hwCapability_.nvencAvailable || hwCapability_.qsvAvailable || hwCapability_.amfAvailable;
    }

    void EncoderManager::DetectHardwareCapabilities()
    {
        hwCapability_.nvencAvailable = NVENCEncoder::IsSupported();
        hwCapability_.qsvAvailable = QSVEncoder::IsSupported();
        hwCapability_.amfAvailable = false; // AMF暂不支持
    }

    void EncoderManager::HandleError(const std::string &encoderName, ErrorCode code, const std::string &message)
    {
        if (errorCallback_)
        {
            ErrorInfo error;
            error.code = code;
            error.message = message;
            error.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                                  std::chrono::system_clock::now().time_since_epoch())
                                  .count();

            errorCallback_(encoderName, error);
        }
    }

    // MultiEncoder implementation
    MultiEncoder::MultiEncoder()
    {
        manager_ = std::make_unique<EncoderManager>();
    }

    MultiEncoder::~MultiEncoder()
    {
        Shutdown();
    }

    bool MultiEncoder::Initialize(const std::vector<StreamConfig> &configs)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        // 清理现有流
        Shutdown();

        // 创建新流
        for (const auto &config : configs)
        {
            if (!AddStream(config))
            {
                LOG_ERROR_F("Failed to add stream: %s", config.streamId.c_str());
                return false;
            }
        }

        return true;
    }

    void MultiEncoder::Shutdown()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        // 停止所有处理线程
        for (auto &stream : streams_)
        {
            if (stream->running)
            {
                stream->running = false;
                if (stream->processingThread.joinable())
                {
                    stream->processingThread.join();
                }
            }
        }

        streams_.clear();
    }

    bool MultiEncoder::EncodeFrame(const FrameData &frame)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->enabled && stream->encoder)
            {
                if (!stream->encoder->EncodeFrame(frame))
                {
                    LOG_ERROR_F("Failed to encode frame for stream: %s", stream->id.c_str());
                    success = false;
                }
            }
        }

        return success;
    }

    bool MultiEncoder::EncodeFrameToStream(const std::string &streamId, const FrameData &frame)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->enabled || !stream->encoder)
        {
            return false;
        }

        return stream->encoder->EncodeFrame(frame);
    }

    std::unique_ptr<EncodedPacket> MultiEncoder::GetEncodedPacket(const std::string &streamId, uint32_t timeoutMs)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->encoder)
        {
            return nullptr;
        }

        return stream->encoder->GetEncodedPacket(timeoutMs);
    }

    std::vector<std::pair<std::string, std::unique_ptr<EncodedPacket>>> MultiEncoder::GetAllEncodedPackets(uint32_t timeoutMs)
    {
        std::vector<std::pair<std::string, std::unique_ptr<EncodedPacket>>> packets;
        std::lock_guard<std::mutex> lock(mutex_);

        for (auto &stream : streams_)
        {
            if (stream->enabled && stream->encoder)
            {
                auto packet = stream->encoder->GetEncodedPacket(timeoutMs);
                if (packet)
                {
                    packets.emplace_back(stream->id, std::move(packet));
                }
            }
        }

        return packets;
    }

    bool MultiEncoder::AddStream(const StreamConfig &config)
    {
        // 检查是否已存在
        if (FindStream(config.streamId))
        {
            LOG_WARNING_F("Stream already exists: %s", config.streamId.c_str());
            return false;
        }

        // 创建编码器
        auto encoder = manager_->CreateEncoder(config.encoderConfig);
        if (!encoder)
        {
            LOG_ERROR_F("Failed to create encoder for stream: %s", config.streamId.c_str());
            return false;
        }

        // 创建流信息
        auto streamInfo = std::make_unique<StreamInfo>();
        streamInfo->id = config.streamId;
        streamInfo->config = config;
        streamInfo->encoder = std::move(encoder);
        streamInfo->enabled = config.enabled;
        streamInfo->running = true;

        // 启动处理线程
        streamInfo->processingThread = std::thread(&MultiEncoder::ProcessingThread, this, streamInfo.get());

        streams_.push_back(std::move(streamInfo));

        LOG_INFO_F("Added stream: %s", config.streamId.c_str());
        return true;
    }

    bool MultiEncoder::RemoveStream(const std::string &streamId)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        if (it == streams_.end())
        {
            return false;
        }

        // 停止处理线程
        (*it)->running = false;
        if ((*it)->processingThread.joinable())
        {
            (*it)->processingThread.join();
        }

        streams_.erase(it);

        LOG_INFO_F("Removed stream: %s", streamId.c_str());
        return true;
    }

    bool MultiEncoder::EnableStream(const std::string &streamId, bool enable)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream)
        {
            return false;
        }

        stream->enabled = enable;
        LOG_INFO_F("Stream %s %s", streamId.c_str(), enable ? "enabled" : "disabled");
        return true;
    }

    std::vector<std::string> MultiEncoder::GetStreamIds() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        std::vector<std::string> ids;
        for (const auto &stream : streams_)
        {
            ids.push_back(stream->id);
        }

        return ids;
    }

    bool MultiEncoder::IsStreamEnabled(const std::string &streamId) const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        return stream ? stream->enabled : false;
    }

    VideoEncoder::Stats MultiEncoder::GetStreamStats(const std::string &streamId) const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->encoder)
        {
            return VideoEncoder::Stats{};
        }

        return stream->encoder->GetStats();
    }

    bool MultiEncoder::SetStreamBitrate(const std::string &streamId, uint32_t bitrate)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->encoder)
        {
            return false;
        }

        return stream->encoder->SetBitrate(bitrate);
    }

    bool MultiEncoder::ForceKeyFrameOnAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->enabled && stream->encoder)
            {
                if (!stream->encoder->ForceKeyFrame())
                {
                    success = false;
                }
            }
        }

        return success;
    }

    bool MultiEncoder::FlushAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->encoder)
            {
                if (!stream->encoder->Flush())
                {
                    success = false;
                }
            }
        }

        return success;
    }

    void MultiEncoder::ProcessingThread(StreamInfo *streamInfo)
    {
        while (streamInfo->running)
        {
            try
            {
                // 获取编码包并通过回调发送
                auto packet = streamInfo->encoder->GetEncodedPacket(10); // 10ms timeout
                if (packet && packetCallback_)
                {
                    packetCallback_(streamInfo->id, std::move(packet));
                }
            }
            catch (...)
            {
                LOG_ERROR_F("Exception in processing thread for stream: %s", streamInfo->id.c_str());
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    MultiEncoder::StreamInfo *MultiEncoder::FindStream(const std::string &streamId)
    {
        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        return (it != streams_.end()) ? it->get() : nullptr;
    }

    const MultiEncoder::StreamInfo *MultiEncoder::FindStream(const std::string &streamId) const
    {
        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        return (it != streams_.end()) ? it->get() : nullptr;
    }

} // namespace StreamCapture