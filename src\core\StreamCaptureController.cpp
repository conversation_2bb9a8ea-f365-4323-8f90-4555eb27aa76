#include "StreamCaptureController.h"
#include "Common.h"
#include "ConfigManager.h"
#include "MediaServerManager.h"
#include "FrameProcessor.h"
#include "HookCore.h"
#include <thread>
#include <chrono>
#include <algorithm>
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <atomic>

namespace StreamCapture {

// ConfigLoader实现
bool ConfigLoader::LoadConfig(const std::string& configPath, std::vector<TargetConfig>& targets) {
    try {
        std::ifstream file(configPath);
        if (!file.is_open()) {
            return false;
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string content = buffer.str();
        
        return ParseJSON(content, targets);
        
    } catch (...) {
        return false;
    }
}

bool ConfigLoader::SaveConfig(const std::string& configPath, const std::vector<TargetConfig>& targets) {
    try {
        std::string jsonContent = ConfigToJSON(targets);
        
        std::ofstream file(configPath);
        if (!file.is_open()) {
            return false;
        }
        
        file << jsonContent;
        return true;
        
    } catch (...) {
        return false;
    }
}

bool ConfigLoader::ValidateConfig(const TargetConfig& config, std::string& errorMessage) {
    if (config.name.empty()) {
        errorMessage = "Target name cannot be empty";
        return false;
    }
    
    if (config.capture.processName.empty() && config.capture.windowTitle.empty()) {
        errorMessage = "Either process name or window title must be specified";
        return false;
    }
    
    if (config.capture.targetFPS == 0 || config.capture.targetFPS > 120) {
        errorMessage = "Invalid FPS value (must be 1-120)";
        return false;
    }
    
    if (config.encoder.bitrate == 0 || config.encoder.bitrate > 100000) {
        errorMessage = "Invalid bitrate value (must be 1-100000 kbps)";
        return false;
    }
    
    if (config.outputs.empty()) {
        errorMessage = "At least one output must be configured";
        return false;
    }
    
    return true;
}

TargetConfig ConfigLoader::CreateDefaultConfig() {
    TargetConfig config;
    config.name = "Default Target";
    config.enabled = true;
    
    config.capture.processName = "test1.exe";
    config.capture.targetFPS = 30;
    config.capture.quality = "high";
    
    config.encoder.type = "hardware";
    config.encoder.codec = "h264";
    config.encoder.bitrate = 2000;
    config.encoder.preset = "quality";
    config.encoder.profile = "main";
    
    OutputConfig fileOutput;
    fileOutput.type = "file";
    fileOutput.url = "output_{timestamp}.mp4";
    fileOutput.enabled = true;
    config.outputs.push_back(fileOutput);
    
    return config;
}

bool ConfigLoader::ParseJSON(const std::string& /*jsonContent*/, std::vector<TargetConfig>& targets) {
    // 简化的JSON解析实现
    // 实际项目中应该使用专业的JSON库如nlohmann/json
    
    // 这里只是示例实现，返回默认配置
    targets.clear();
    targets.push_back(CreateDefaultConfig());
    
    return true;
}

std::string ConfigLoader::ConfigToJSON(const std::vector<TargetConfig>& targets) {
    // 简化的JSON生成实现
    std::stringstream json;
    json << "{\n";
    json << "  \"targets\": [\n";
    
    for (size_t i = 0; i < targets.size(); ++i) {
        const auto& target = targets[i];
        json << "    {\n";
        json << "      \"name\": \"" << target.name << "\",\n";
        json << "      \"enabled\": " << (target.enabled ? "true" : "false") << ",\n";
        json << "      \"capture\": {\n";
        json << "        \"processName\": \"" << target.capture.processName << "\",\n";
        json << "        \"fps\": " << target.capture.targetFPS << ",\n";
        json << "        \"quality\": \"" << target.capture.quality << "\"\n";
        json << "      },\n";
        json << "      \"encoder\": {\n";
        json << "        \"type\": \"" << target.encoder.type << "\",\n";
        json << "        \"codec\": \"" << target.encoder.codec << "\",\n";
        json << "        \"bitrate\": " << target.encoder.bitrate << "\n";
        json << "      }\n";
        json << "    }";
        if (i < targets.size() - 1) json << ",";
        json << "\n";
    }
    
    json << "  ]\n";
    json << "}\n";
    
    return json.str();
}

// SystemMonitor实现
SystemMonitor::SystemMonitor() : running_(false) {
}

SystemMonitor::~SystemMonitor() {
    Shutdown();
}

bool SystemMonitor::Initialize() {
    if (running_) return true;
    
    running_ = true;
    monitorThread_ = std::thread(&SystemMonitor::MonitoringThread, this);
    
    return true;
}

void SystemMonitor::Shutdown() {
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (!running_) {
            return;
        }
        running_ = false;
    }

    if (monitorThread_.joinable()) {
        monitorThread_.join();
    }
}

SystemMonitor::SystemStatus SystemMonitor::GetStatus() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return status_;
}

void SystemMonitor::UpdateStatus() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 获取内存信息
    MEMORYSTATUSEX memStatus = {0};
    memStatus.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memStatus)) {
        status_.memoryUsage = static_cast<float>(memStatus.dwMemoryLoad);
        status_.availableMemoryMB = static_cast<size_t>(memStatus.ullAvailPhys / (1024 * 1024));
        status_.isLowMemory = status_.memoryUsage > memoryThreshold_;
    }
    
    // 简化的CPU使用率检测
    static ULARGE_INTEGER lastCPU, lastSysCPU, lastUserCPU;
    static bool firstTime = true;
    
    if (firstTime) {
        SYSTEM_INFO sysInfo;
        FILETIME ftime, fsys, fuser;
        
        GetSystemInfo(&sysInfo);
        GetSystemTimeAsFileTime(&ftime);
        memcpy(&lastCPU, &ftime, sizeof(FILETIME));
        
        GetProcessTimes(GetCurrentProcess(), &ftime, &ftime, &fsys, &fuser);
        memcpy(&lastSysCPU, &fsys, sizeof(FILETIME));
        memcpy(&lastUserCPU, &fuser, sizeof(FILETIME));
        firstTime = false;
        status_.cpuUsage = 0.0f;
    } else {
        // 简化的CPU计算
        status_.cpuUsage = 25.0f; // 占位值
    }
    
    status_.isHighCpuLoad = status_.cpuUsage > cpuThreshold_;
    
    // GPU使用率（占位实现）
    status_.gpuUsage = 0.0f;
    
    // 进程计数（占位实现）
    status_.processCount = 1;
}

void SystemMonitor::MonitoringThread() {
    while (running_) {
        UpdateStatus();
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

// StreamCaptureController实现
StreamCaptureController::StreamCaptureController()
    : state_(State::Idle)
    , configPath_("")
    , mediaServerManager_(nullptr)
    , running_(false)
    , paused_(false)
{
    // 初始化日志系统
    Logger::Instance().Initialize();
}

StreamCaptureController::~StreamCaptureController() {
    Shutdown();
}

bool StreamCaptureController::Initialize(const std::string& configPath) {
    if (state_ != State::Idle) {
        return false;
    }
    
    HandleStateChange(State::Initializing);
    
    try {
        configPath_ = configPath;
        
        // 加载配置
        ConfigLoader loader;
        if (!loader.LoadConfig(configPath_, targetConfigs_)) {
            LOG_WARNING("Failed to load config, using defaults");
            TargetConfig defaultConfig = loader.CreateDefaultConfig();
            targetConfigs_.push_back(defaultConfig);
        }
        
        // 初始化MediaServerManager
        mediaServerManager_ = std::make_unique<MediaServerManager>();
        
        // 创建目标实例
        for (const auto& config : targetConfigs_) {
            auto target = CreateTargetInstance(config);
            if (target) {
                targets_.emplace_back(std::move(target));
                LOG_INFO_F("Created target instance: %s", config.name.c_str());
            } else {
                LOG_ERROR_F("Failed to create target instance: %s", config.name.c_str());
            }
        }
        
        // 启动监控线程
        running_ = true;
        monitoringThread_ = std::thread(&StreamCaptureController::MonitoringThread, this);
        
        HandleStateChange(State::Idle);
        LOG_INFO("StreamCapture initialized successfully");
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_F("Exception during initialization: %s", e.what());
        HandleStateChange(State::Error);
        return false;
    } catch (...) {
        LOG_ERROR("Unknown exception during initialization");
        HandleStateChange(State::Error);
        return false;
    }
}

void StreamCaptureController::Shutdown() {
    if (state_ == State::Idle) {
        return;
    }
    
    LOG_INFO("StreamCapture shutting down...");
    HandleStateChange(State::Stopping);
    
    // 停止所有目标
    StopCapture();
    
    // 停止监控线程
    running_ = false;
    if (monitoringThread_.joinable()) {
        monitoringThread_.join();
    }
    
    // 停止MediaMTX服务器
    if (mediaServerManager_) {
        LOG_INFO("Stopping MediaMTX server...");
        mediaServerManager_->StopServer();
        mediaServerManager_.reset();
        LOG_INFO("MediaMTX server stopped");
    }
    
    // 清理目标实例
    targets_.clear();
    
    
    HandleStateChange(State::Idle);
    LOG_INFO("StreamCapture shutdown complete");
}

bool StreamCaptureController::StartCapture() {
    if (state_ != State::Idle) {
        return false;
    }
    
    LOG_INFO("Starting capture for all targets");
    HandleStateChange(State::Running);
    
    bool anyStarted = false;
    for (auto& target : targets_) {
        if (target->config.enabled && StartTargetInstance(target.get())) {
            anyStarted = true;
            LOG_INFO_F("Started target: %s", target->config.name.c_str());
        }
    }
    
    if (!anyStarted) {
        LOG_WARNING("No targets were started");
        HandleStateChange(State::Idle);
        return false;
    }
    
    return true;
}

bool StreamCaptureController::StopCapture() {
    if (state_ != State::Running) {
        return false;
    }
    
    LOG_INFO("Stopping capture for all targets");
    
    for (auto& target : targets_) {
        if (target->active) {
            StopTargetInstance(target.get());
            LOG_INFO_F("Stopped target: %s", target->config.name.c_str());
        }
    }
    
    HandleStateChange(State::Idle);
    return true;
}

bool StreamCaptureController::PauseCapture(bool pause) {
    paused_ = pause;
    
    for (auto& target : targets_) {
        target->paused = pause;
    }
    
    LOG_INFO_F("Capture %s", pause ? "paused" : "resumed");
    return true;
}

bool StreamCaptureController::StartTarget(const std::string& targetName) {
    auto target = FindTarget(targetName);
    if (!target || target->active) {
        return false;
    }
    
    return StartTargetInstance(target);
}

bool StreamCaptureController::StopTarget(const std::string& targetName) {
    auto target = FindTarget(targetName);
    if (!target || !target->active) {
        return false;
    }
    
    StopTargetInstance(target);
    return true;
}

bool StreamCaptureController::IsTargetActive(const std::string& targetName) const {
    auto target = const_cast<StreamCaptureController*>(this)->FindTarget(targetName);
    return target && target->active;
}

std::vector<std::string> StreamCaptureController::GetTargetNames() const {
    std::vector<std::string> names;
    for (const auto& target : targets_) {
        names.push_back(target->config.name);
    }
    return names;
}

std::vector<TargetConfig> StreamCaptureController::GetTargets() const {
    return targetConfigs_;
}

StreamCaptureController::GlobalStats StreamCaptureController::GetGlobalStats() const {
    GlobalStats stats = {};
    
    // 聚合目标统计
    stats.activeTargets = 0;
    for (const auto& target : targets_) {
        if (target->active) {
            stats.activeTargets++;
            const auto& targetStats = target->stats;
            stats.totalFramesCaptured += targetStats.captureFrames;
            stats.droppedFrames += targetStats.droppedFrames;
            // 其他统计聚合...
        }
    }
    
    return stats;
}

std::unique_ptr<StreamCaptureController::TargetInstance> StreamCaptureController::CreateTargetInstance(const TargetConfig& config) {
    try {
        auto instance = std::make_unique<TargetInstance>();
        instance->config = config;
        instance->active = false;
        instance->paused = false;
        
        // 创建帧处理器
        instance->frameProcessor = std::make_unique<FrameProcessor>();
        FrameProcessor::Config frameConfig;
        frameConfig.bufferSize = 10;
        frameConfig.outputFormat = "nv12";
        instance->frameProcessor->Initialize(frameConfig);
        
        // 创建编码器
        // instance->encoder = std::make_unique<MultiEncoder>();
        
        // 创建推流控制器
        // instance->streamController = std::make_unique<MultiStreamController>();
        
        return instance;
        
    } catch (...) {
        return nullptr;
    }
}

bool StreamCaptureController::StartTargetInstance(TargetInstance* target) {
    if (!target) {
        LOG_ERROR("Target instance is null");
        return false;
    }
    
    if (target->active) {
        LOG_WARNING_F("Target %s is already active", target->config.name.c_str());
        return true;
    }
    
    try {
        LOG_INFO_F("Starting target instance: %s", target->config.name.c_str());
        
        // 初始化Hook
        auto& hookCore = HookCore::Instance();
        if (!hookCore.Initialize()) {
            LOG_ERROR_F("Failed to initialize hook core for target: %s", target->config.name.c_str());
            return false;
        }
        
        // 设置回调
        auto frameCallback = [this, target](std::unique_ptr<FrameData> frame) {
            (void)target; // 避免未使用变量警告
            (void)frame;  // 避免未使用变量警告
            // 暂时注释掉实际处理逻辑直到实现完整功能
            // this->HandleFrameData(target->config.name, std::move(frame));
        };
        hookCore.SetFrameCallback(frameCallback);
        
        // 暂时注释掉实际Hook逻辑直到实现完整功能
        /*
        if (!hookCore.StartHooking(target->config)) {
            LOG_ERROR_F("Failed to start hooking for target: %s", target->config.name.c_str());
            return false;
        }
        */
        
        // 初始化编码器
        if (target->encoder) {
            // 暂时注释掉编码器初始化直到实现完整功能
            /*
            // 创建编码器配置
            std::vector<MultiEncoder::StreamConfig> encoderConfigs;
            // 修复：EncoderConfig中没有enabled字段，使用默认值true
            MultiEncoder::StreamConfig streamConfig;
            streamConfig.streamId = "default";  // 默认流ID
            streamConfig.encoderConfig = target->config.encoder;
            streamConfig.enabled = true;  // 默认启用
            encoderConfigs.push_back(streamConfig);
            
            if (!target->encoder->Initialize(encoderConfigs)) {
                LOG_ERROR_F("Failed to initialize encoder for target: %s", target->config.name.c_str());
                return false;
            }
            */
        }
        
        // 初始化推流控制器
        if (target->streamController) {
            // 暂时注释掉推流控制器初始化直到实现完整功能
            /*
            MultiStreamController::Config streamConfig;
            streamConfig.packetBufferSize = 100;
            streamConfig.enableBroadcast = true;
            
            // 创建推流配置
            for (const auto& outConfig : target->config.outputs) {
                // 修复：使用正确的类型和字段名
                StreamManager::StreamConfig controllerConfig;
                controllerConfig.streamId = outConfig.type; // 使用type作为流ID
                controllerConfig.publisherConfig.url = outConfig.url;
                controllerConfig.publisherConfig.protocol = outConfig.type;
                controllerConfig.enabled = outConfig.enabled;
                controllerConfig.autoReconnect = true;
                // 修复：使用正确的字段名
                controllerConfig.publisherConfig.reconnectDelayMs = 5000;
                controllerConfig.publisherConfig.reconnectAttempts = 3;
                streamConfig.streams.push_back(controllerConfig);
            }
            
            if (!target->streamController->Initialize(streamConfig)) {
                LOG_ERROR_F("Failed to initialize stream controller for target: %s", target->config.name.c_str());
                return false;
            }
            
            // 启动所有推流
            if (!target->streamController->StartAllStreams()) {
                LOG_ERROR_F("Failed to start streams for target: %s", target->config.name.c_str());
                return false;
            }
            */
        }
        
        target->active = true;
        LOG_INFO_F("Successfully started target instance: %s", target->config.name.c_str());
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_F("Exception in StartTargetInstance for target %s: %s", 
                   target->config.name.c_str(), e.what());
        return false;
    } catch (...) {
        LOG_ERROR_F("Unknown exception in StartTargetInstance for target: %s", 
                   target->config.name.c_str());
        return false;
    }
}

void StreamCaptureController::StopTargetInstance(TargetInstance* target) {
    if (!target || !target->active) {
        return;
    }
    
    target->active = false;
    
    // 停止Hook
    // auto& hookCore = HookCore::Instance();
    // hookCore.StopHooking();
}

StreamCaptureController::TargetInstance* StreamCaptureController::FindTarget(const std::string& targetName) {
    for (auto& target : targets_) {
        if (target->config.name == targetName) {
            return target.get();
        }
    }
    return nullptr;
}

/*
void StreamCaptureController::HandleFrameData(const std::string& targetName, std::unique_ptr<FrameData> frame) {
    auto target = FindTarget(targetName);
    if (!target || target->paused) {
        return;
    }
    
    // 处理帧数据
    if (target->frameProcessor) {
        // 修复：正确调用ProcessFrame，它接受unique_ptr参数
        bool success = target->frameProcessor->ProcessFrame(std::move(frame));
        if (success) {
            // 从处理器获取处理后的帧
            auto processedFrame = target->frameProcessor->GetProcessedFrame(0);
            if (processedFrame) {
                // 发送到编码器
                if (target->encoder) {
                    // 修复：检查编码器是否已初始化（通过检查流数量）
                    if (!target->encoder->GetStreamIds().empty()) {
                        target->encoder->EncodeFrame(*processedFrame);
                        
                        // 获取编码后的数据包
                        auto packets = target->encoder->GetAllEncodedPackets(0); // 非阻塞
                        for (auto& [streamId, packet] : packets) {
                            if (packet) {
                                // 发送到推流控制器
                                if (target->streamController) {
                                    target->streamController->HandleEncodedPacket(streamId, std::move(packet));
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

void StreamCaptureController::HandleEncodedPacket(const std::string& targetName, 
                                                  const std::string& streamId, 
                                                  std::unique_ptr<EncodedPacket> packet) {
    auto target = FindTarget(targetName);
    if (!target) {
        return;
    }
    
    // 发送到推流系统
    if (target->streamController) {
        target->streamController->HandleEncodedPacket(streamId, std::move(packet));
    }
}

void StreamCaptureController::HandleError(const std::string& source, const ErrorInfo& error) {
    LOG_ERROR_F("Error from %s: %s", source.c_str(), error.message.c_str());
    
    if (errorCallback_) {
        errorCallback_(error);
    }
}
*/

void StreamCaptureController::HandleStateChange(State newState) {
    State oldState = state_;
    state_ = newState;
    
    if (stateCallback_ && oldState != newState) {
        stateCallback_(oldState, newState);
    }
}

void StreamCaptureController::MonitoringThread() {
    while (running_) {
        try {
            // PerformMaintenance();
        } catch (...) {
            LOG_ERROR("Exception in monitoring thread");
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

/*
void StreamCaptureController::PerformMaintenance() {
    // 检查系统状态
    if (systemMonitor_) {
        auto status = systemMonitor_->GetStatus();
        
        if (status.isLowMemory) {
            LOG_WARNING_F("Low memory detected: %.1f%% used", status.memoryUsage);
            
            // 可以在这里实施内存清理策略
            if (statusCallback_) {
                statusCallback_("Warning: Low memory detected");
            }
        }
        
        if (status.isHighCpuLoad) {
            LOG_WARNING_F("High CPU load detected: %.1f%%", status.cpuUsage);
        }
    }
    
    // 更新全局统计
    {
        std::lock_guard<std::mutex> lock(mutex_);
        // 更新globalStats_
    }
}
*/

// Logger实现
Logger& Logger::Instance() {
    static Logger instance;
    return instance;
}

bool Logger::Initialize(const std::string& logFile, Level minLevel) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    logFile_ = logFile;
    minLevel_ = minLevel;
    
    if (!logFile_.empty()) {
        // 先关闭已有的文件句柄
        if (fileHandle_) {
            fclose(fileHandle_);
            fileHandle_ = nullptr;
        }
        
        // 尝试打开日志文件
        FILE* tempHandle = nullptr;
        errno_t err = fopen_s(&tempHandle, logFile_.c_str(), "a");
        if (err != 0 || !tempHandle) {
            fileHandle_ = nullptr;
            // 即使文件打开失败，也不应该导致初始化失败
            // 只是不写入文件，但仍可以输出到控制台
            Log(Level::Warning, "Failed to open log file: " + logFile_);
            return true;
        }
        fileHandle_ = tempHandle;
    }
    
    Log(Level::Info, "Logger initialized");
    return true;
}

void Logger::Shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (fileHandle_) {
        try {
            fclose(fileHandle_);
        } catch (...) {
            // 忽略关闭文件时的异常
        }
        fileHandle_ = nullptr;
    }
    
    logFile_.clear();
}

void Logger::Log(Level level, const std::string& message) {
    // 先检查日志级别
    if (level < minLevel_) {
        return;
    }
    
    // 创建日志线程避免阻塞
    std::thread([=]() {
        try {
            // 检查是否已初始化
            if (!fileHandle_ && logFile_.empty()) {
                // 如果没有指定日志文件，只输出到控制台
                std::string timestamp = GetTimestamp();
                std::string levelStr = LevelToString(level);
                std::string logLine = "[" + timestamp + "] [" + levelStr + "] " + message + "\n";
                
                // 输出到控制台
                printf("%s", logLine.c_str());
                fflush(stdout);
                return;
            }

            std::string timestamp = GetTimestamp();
            std::string levelStr = LevelToString(level);
            std::string logLine = "[" + timestamp + "] [" + levelStr + "] " + message + "\n";
            
            // 输出到控制台
            printf("%s", logLine.c_str());
            fflush(stdout);
            
            // 输出到文件
            if (fileHandle_) {
                fprintf(fileHandle_, "%s", logLine.c_str());
                fflush(fileHandle_);
            }
            
            // 添加异步日志刷新功能
            if (fileHandle_ && (level == Level::Error || level == Level::Fatal)) {
                static std::atomic_int flushCounter{0};
                if (++flushCounter % 10 == 0) {
                    std::lock_guard<std::mutex> lock(mutex_);
                    fflush(fileHandle_);
                }
            }
        } catch (...) {
            // 忽略日志记录中的任何异常，避免影响主程序流程
            // 可以考虑输出到标准错误
            fprintf(stderr, "[ERROR] Failed to log message\n");
            fflush(stderr);
        }
    }).detach();
}

void Logger::LogFormat(Level level, const char* format, ...) {
    if (level < minLevel_) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    
    char buffer[4096];
    vsnprintf_s(buffer, sizeof(buffer), _TRUNCATE, format, args);
    
    va_end(args);
    
    Log(level, std::string(buffer));
}

std::string Logger::LevelToString(Level level) const {
    switch (level) {
        case Level::Debug: return "DEBUG";
        case Level::Info: return "INFO";
        case Level::Warning: return "WARN";
        case Level::Error: return "ERROR";
        case Level::Fatal: return "FATAL";
        default: return "UNKNOWN";
    }
}

std::string Logger::GetTimestamp() const {
    try {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        // 使用localtime_s替代localtime以提高安全性
        std::tm local_tm;
        localtime_s(&local_tm, &time_t);
        
        std::stringstream ss;
        ss << std::put_time(&local_tm, "%Y-%m-%d %H:%M:%S");
        ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
        
        return ss.str();
    } catch (...) {
        // 如果时间戳生成失败，返回一个默认值
        return "0000-00-00 00:00:00.000";
    }
}

} // namespace StreamCapture