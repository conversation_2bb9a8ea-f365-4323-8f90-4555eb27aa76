#include "StreamCaptureController.h"
#include "Win7Compatibility.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <cstdarg>

namespace StreamCapture {

// ConfigLoader实现
bool ConfigLoader::LoadConfig(const std::string& configPath, std::vector<TargetConfig>& targets) {
    try {
        std::ifstream file(configPath);
        if (!file.is_open()) {
            return false;
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string content = buffer.str();
        
        return ParseJSON(content, targets);
        
    } catch (...) {
        return false;
    }
}

bool ConfigLoader::SaveConfig(const std::string& configPath, const std::vector<TargetConfig>& targets) {
    try {
        std::string jsonContent = ConfigToJSON(targets);
        
        std::ofstream file(configPath);
        if (!file.is_open()) {
            return false;
        }
        
        file << jsonContent;
        return true;
        
    } catch (...) {
        return false;
    }
}

bool ConfigLoader::ValidateConfig(const TargetConfig& config, std::string& errorMessage) {
    if (config.name.empty()) {
        errorMessage = "Target name cannot be empty";
        return false;
    }
    
    if (config.capture.processName.empty() && config.capture.windowTitle.empty()) {
        errorMessage = "Either process name or window title must be specified";
        return false;
    }
    
    if (config.capture.targetFPS == 0 || config.capture.targetFPS > 120) {
        errorMessage = "Invalid FPS value (must be 1-120)";
        return false;
    }
    
    if (config.encoder.bitrate == 0 || config.encoder.bitrate > 100000) {
        errorMessage = "Invalid bitrate value (must be 1-100000 kbps)";
        return false;
    }
    
    if (config.outputs.empty()) {
        errorMessage = "At least one output must be configured";
        return false;
    }
    
    return true;
}

TargetConfig ConfigLoader::CreateDefaultConfig() {
    TargetConfig config;
    config.name = "Default Target";
    config.enabled = true;
    
    config.capture.processName = "test1.exe";
    config.capture.targetFPS = 30;
    config.capture.quality = "high";
    
    config.encoder.type = "hardware";
    config.encoder.codec = "h264";
    config.encoder.bitrate = 2000;
    config.encoder.preset = "quality";
    config.encoder.profile = "main";
    
    OutputConfig fileOutput;
    fileOutput.type = "file";
    fileOutput.url = "output_{timestamp}.mp4";
    fileOutput.enabled = true;
    config.outputs.push_back(fileOutput);
    
    return config;
}

bool ConfigLoader::ParseJSON(const std::string& /*jsonContent*/, std::vector<TargetConfig>& targets) {
    // 简化的JSON解析实现
    // 实际项目中应该使用专业的JSON库如nlohmann/json
    
    // 这里只是示例实现，返回默认配置
    targets.clear();
    targets.push_back(CreateDefaultConfig());
    
    return true;
}

std::string ConfigLoader::ConfigToJSON(const std::vector<TargetConfig>& targets) {
    // 简化的JSON生成实现
    std::stringstream json;
    json << "{\n";
    json << "  \"targets\": [\n";
    
    for (size_t i = 0; i < targets.size(); ++i) {
        const auto& target = targets[i];
        json << "    {\n";
        json << "      \"name\": \"" << target.name << "\",\n";
        json << "      \"enabled\": " << (target.enabled ? "true" : "false") << ",\n";
        json << "      \"capture\": {\n";
        json << "        \"processName\": \"" << target.capture.processName << "\",\n";
        json << "        \"fps\": " << target.capture.targetFPS << ",\n";
        json << "        \"quality\": \"" << target.capture.quality << "\"\n";
        json << "      },\n";
        json << "      \"encoder\": {\n";
        json << "        \"type\": \"" << target.encoder.type << "\",\n";
        json << "        \"codec\": \"" << target.encoder.codec << "\",\n";
        json << "        \"bitrate\": " << target.encoder.bitrate << "\n";
        json << "      }\n";
        json << "    }";
        if (i < targets.size() - 1) json << ",";
        json << "\n";
    }
    
    json << "  ]\n";
    json << "}\n";
    
    return json.str();
}

// SystemMonitor实现
SystemMonitor::SystemMonitor() : running_(false) {
}

SystemMonitor::~SystemMonitor() {
    Shutdown();
}

bool SystemMonitor::Initialize() {
    if (running_) return true;
    
    running_ = true;
    monitorThread_ = std::thread(&SystemMonitor::MonitoringThread, this);
    
    return true;
}

void SystemMonitor::Shutdown() {
    if (running_) {
        running_ = false;
        if (monitorThread_.joinable()) {
            monitorThread_.join();
        }
    }
}

SystemMonitor::SystemStatus SystemMonitor::GetStatus() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return status_;
}

void SystemMonitor::UpdateStatus() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 获取内存信息
    MEMORYSTATUSEX memStatus = {0};
    memStatus.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memStatus)) {
        status_.memoryUsage = static_cast<float>(memStatus.dwMemoryLoad);
        status_.availableMemoryMB = static_cast<size_t>(memStatus.ullAvailPhys / (1024 * 1024));
        status_.isLowMemory = status_.memoryUsage > memoryThreshold_;
    }
    
    // 简化的CPU使用率检测
    static ULARGE_INTEGER lastCPU, lastSysCPU, lastUserCPU;
    static bool firstTime = true;
    
    if (firstTime) {
        SYSTEM_INFO sysInfo;
        FILETIME ftime, fsys, fuser;
        
        GetSystemInfo(&sysInfo);
        GetSystemTimeAsFileTime(&ftime);
        memcpy(&lastCPU, &ftime, sizeof(FILETIME));
        
        GetProcessTimes(GetCurrentProcess(), &ftime, &ftime, &fsys, &fuser);
        memcpy(&lastSysCPU, &fsys, sizeof(FILETIME));
        memcpy(&lastUserCPU, &fuser, sizeof(FILETIME));
        firstTime = false;
        status_.cpuUsage = 0.0f;
    } else {
        // 简化的CPU计算
        status_.cpuUsage = 25.0f; // 占位值
    }
    
    status_.isHighCpuLoad = status_.cpuUsage > cpuThreshold_;
    
    // GPU使用率（占位实现）
    status_.gpuUsage = 0.0f;
    
    // 进程计数（占位实现）
    status_.processCount = 1;
}

void SystemMonitor::MonitoringThread() {
    while (running_) {
        UpdateStatus();
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

// StreamCaptureController实现
StreamCaptureController::StreamCaptureController() 
    : state_(State::Idle)
    , running_(false)
    , paused_(false) {
}

StreamCaptureController::~StreamCaptureController() {
    Shutdown();
}

bool StreamCaptureController::Initialize(const std::string& configPath) {
    if (state_ != State::Idle) {
        return false;
    }
    
    HandleStateChange(State::Initializing);
    
    try {
        configPath_ = configPath;
        
        // 初始化Windows 7兼容性
        if (!Win7Compatibility::Instance().Initialize()) {
            LOG_ERROR("Failed to initialize Windows 7 compatibility");
            HandleStateChange(State::Error);
            return false;
        }
        
        // 初始化日志系统
        Logger::Instance().Initialize("streamcapture.log", Logger::Level::Info);
        LOG_INFO("StreamCapture initializing...");
        
        // 加载配置
        if (!ConfigLoader::LoadConfig(configPath_, targetConfigs_)) {
            LOG_WARNING("Failed to load config, using default configuration");
            targetConfigs_.clear();
            targetConfigs_.push_back(ConfigLoader::CreateDefaultConfig());
        }
        
        // 验证配置
        for (const auto& config : targetConfigs_) {
            std::string errorMsg;
            if (!ConfigLoader::ValidateConfig(config, errorMsg)) {
                LOG_ERROR_F("Invalid config for target '%s': %s", config.name.c_str(), errorMsg.c_str());
                HandleStateChange(State::Error);
                return false;
            }
        }
        
        // 创建目标实例
        for (const auto& config : targetConfigs_) {
            auto target = CreateTargetInstance(config);
            if (target) {
                targets_.emplace_back(target);
                LOG_INFO_F("Created target instance: %s", config.name.c_str());
            } else {
                LOG_ERROR_F("Failed to create target instance: %s", config.name.c_str());
            }
        }
        
        // 初始化系统监控
        systemMonitor_ = std::make_unique<SystemMonitor>();
        systemMonitor_->Initialize();
        
        // 启动监控线程
        running_ = true;
        monitoringThread_ = std::thread(&StreamCaptureController::MonitoringThread, this);
        
        HandleStateChange(State::Idle);
        LOG_INFO("StreamCapture initialized successfully");
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_F("Exception during initialization: %s", e.what());
        HandleStateChange(State::Error);
        return false;
    } catch (...) {
        LOG_ERROR("Unknown exception during initialization");
        HandleStateChange(State::Error);
        return false;
    }
}

void StreamCaptureController::Shutdown() {
    if (state_ == State::Idle) {
        return;
    }
    
    LOG_INFO("StreamCapture shutting down...");
    HandleStateChange(State::Stopping);
    
    // 停止所有目标
    StopCapture();
    
    // 停止监控线程
    running_ = false;
    if (monitoringThread_.joinable()) {
        monitoringThread_.join();
    }
    
    // 清理目标实例
    targets_.clear();
    
    // 清理系统监控
    if (systemMonitor_) {
        systemMonitor_->Shutdown();
        systemMonitor_.reset();
    }
    
    HandleStateChange(State::Idle);
    LOG_INFO("StreamCapture shutdown complete");
}

bool StreamCaptureController::StartCapture() {
    if (state_ != State::Idle) {
        return false;
    }
    
    LOG_INFO("Starting capture for all targets");
    HandleStateChange(State::Running);
    
    bool anyStarted = false;
    for (auto& target : targets_) {
        if (target->config.enabled && StartTargetInstance(target.get())) {
            anyStarted = true;
            LOG_INFO_F("Started target: %s", target->config.name.c_str());
        }
    }
    
    if (!anyStarted) {
        LOG_WARNING("No targets were started");
        HandleStateChange(State::Idle);
        return false;
    }
    
    return true;
}

bool StreamCaptureController::StopCapture() {
    if (state_ != State::Running) {
        return false;
    }
    
    LOG_INFO("Stopping capture for all targets");
    
    for (auto& target : targets_) {
        if (target->active) {
            StopTargetInstance(target.get());
            LOG_INFO_F("Stopped target: %s", target->config.name.c_str());
        }
    }
    
    HandleStateChange(State::Idle);
    return true;
}

bool StreamCaptureController::PauseCapture(bool pause) {
    paused_ = pause;
    
    for (auto& target : targets_) {
        target->paused = pause;
    }
    
    LOG_INFO_F("Capture %s", pause ? "paused" : "resumed");
    return true;
}

bool StreamCaptureController::StartTarget(const std::string& targetName) {
    auto target = FindTarget(targetName);
    if (!target || target->active) {
        return false;
    }
    
    return StartTargetInstance(target);
}

bool StreamCaptureController::StopTarget(const std::string& targetName) {
    auto target = FindTarget(targetName);
    if (!target || !target->active) {
        return false;
    }
    
    StopTargetInstance(target);
    return true;
}

bool StreamCaptureController::IsTargetActive(const std::string& targetName) const {
    auto target = const_cast<StreamCaptureController*>(this)->FindTarget(targetName);
    return target && target->active;
}

std::vector<std::string> StreamCaptureController::GetTargetNames() const {
    std::vector<std::string> names;
    for (const auto& target : targets_) {
        names.push_back(target->config.name);
    }
    return names;
}

std::vector<TargetConfig> StreamCaptureController::GetTargets() const {
    return targetConfigs_;
}

StreamCaptureController::GlobalStats StreamCaptureController::GetGlobalStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    GlobalStats stats = globalStats_;
    
    // 更新系统状态
    if (systemMonitor_) {
        stats.systemStatus = systemMonitor_->GetStatus();
    }
    
    // 聚合目标统计
    stats.activeTargets = 0;
    for (const auto& target : targets_) {
        if (target->active) {
            stats.activeTargets++;
            const auto& targetStats = target->stats;
            stats.totalFramesCaptured += targetStats.captureFrames;
            stats.droppedFrames += targetStats.droppedFrames;
            // 其他统计聚合...
        }
    }
    
    return stats;
}

StreamCaptureController::TargetInstance* StreamCaptureController::CreateTargetInstance(const TargetConfig& config) {
    try {
        auto instance = std::make_unique<TargetInstance>();
        instance->config = config;
        instance->active = false;
        instance->paused = false;
        
        // 创建Hook核心
        // instance->hookCore = std::make_unique<HookCore>();
        // 由于HookCore是单例，这里需要特殊处理
        
        // 创建帧处理器
        instance->frameProcessor = std::make_unique<FrameProcessor>();
        FrameProcessor::Config frameConfig;
        frameConfig.bufferSize = 10;
        frameConfig.outputFormat = "nv12";
        instance->frameProcessor->Initialize(frameConfig);
        
        // 创建编码器
        instance->encoder = std::make_unique<MultiEncoder>();
        
        // 创建推流控制器
        instance->streamController = std::make_unique<MultiStreamController>();
        
        return instance.release();
        
    } catch (...) {
        return nullptr;
    }
}

bool StreamCaptureController::StartTargetInstance(TargetInstance* target) {
    if (!target || target->active) {
        return false;
    }
    
    try {
        // 设置回调
        auto frameCallback = [this, target](std::unique_ptr<FrameData> frame) {
            this->HandleFrameData(target->config.name, std::move(frame));
        };
        
        // 启动Hook（使用单例）
        auto& hookCore = HookCore::Instance();
        hookCore.SetFrameCallback(frameCallback);
        
        if (!hookCore.StartHooking(target->config)) {
            return false;
        }
        
        target->active = true;
        return true;
        
    } catch (...) {
        return false;
    }
}

void StreamCaptureController::StopTargetInstance(TargetInstance* target) {
    if (!target || !target->active) {
        return;
    }
    
    target->active = false;
    
    // 停止Hook
    auto& hookCore = HookCore::Instance();
    hookCore.StopHooking();
}

StreamCaptureController::TargetInstance* StreamCaptureController::FindTarget(const std::string& targetName) {
    for (auto& target : targets_) {
        if (target->config.name == targetName) {
            return target.get();
        }
    }
    return nullptr;
}

void StreamCaptureController::HandleFrameData(const std::string& targetName, std::unique_ptr<FrameData> frame) {
    auto target = FindTarget(targetName);
    if (!target || target->paused) {
        return;
    }
    
    // 处理帧数据
    if (target->frameProcessor) {
        target->frameProcessor->ProcessFrame(std::move(frame));
    }
}

void StreamCaptureController::HandleEncodedPacket(const std::string& targetName, 
                                                  const std::string& streamId, 
                                                  std::unique_ptr<EncodedPacket> packet) {
    auto target = FindTarget(targetName);
    if (!target) {
        return;
    }
    
    // 发送到推流系统
    if (target->streamController) {
        target->streamController->HandleEncodedPacket(streamId, std::move(packet));
    }
}

void StreamCaptureController::HandleError(const std::string& source, const ErrorInfo& error) {
    LOG_ERROR_F("Error from %s: %s", source.c_str(), error.message.c_str());
    
    if (errorCallback_) {
        errorCallback_(error);
    }
}

void StreamCaptureController::HandleStateChange(State newState) {
    State oldState = state_.exchange(newState);
    
    if (stateCallback_ && oldState != newState) {
        stateCallback_(oldState, newState);
    }
}

void StreamCaptureController::MonitoringThread() {
    while (running_) {
        try {
            PerformMaintenance();
        } catch (...) {
            LOG_ERROR("Exception in monitoring thread");
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

void StreamCaptureController::PerformMaintenance() {
    // 检查系统状态
    if (systemMonitor_) {
        auto status = systemMonitor_->GetStatus();
        
        if (status.isLowMemory) {
            LOG_WARNING_F("Low memory detected: %.1f%% used", status.memoryUsage);
            
            // 可以在这里实施内存清理策略
            if (statusCallback_) {
                statusCallback_("Warning: Low memory detected");
            }
        }
        
        if (status.isHighCpuLoad) {
            LOG_WARNING_F("High CPU load detected: %.1f%%", status.cpuUsage);
        }
    }
    
    // 更新全局统计
    {
        std::lock_guard<std::mutex> lock(mutex_);
        // 更新globalStats_
    }
}

// Logger实现
Logger& Logger::Instance() {
    static Logger instance;
    return instance;
}

bool Logger::Initialize(const std::string& logFile, Level minLevel) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    logFile_ = logFile;
    minLevel_ = minLevel;
    
    if (!logFile_.empty()) {
        if (fopen_s(&fileHandle_, logFile_.c_str(), "a") != 0) {
            fileHandle_ = nullptr;
            return false;
        }
    }
    
    Log(Level::Info, "Logger initialized");
    return true;
}

void Logger::Shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (fileHandle_) {
        fclose(fileHandle_);
        fileHandle_ = nullptr;
    }
}

void Logger::Log(Level level, const std::string& message) {
    if (level < minLevel_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::string timestamp = GetTimestamp();
    std::string levelStr = LevelToString(level);
    std::string logLine = "[" + timestamp + "] [" + levelStr + "] " + message + "\n";
    
    // 输出到控制台
    printf("%s", logLine.c_str());
    
    // 输出到文件
    if (fileHandle_) {
        fprintf(fileHandle_, "%s", logLine.c_str());
        fflush(fileHandle_);
    }
}

void Logger::LogFormat(Level level, const char* format, ...) {
    if (level < minLevel_) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    
    char buffer[4096];
    vsnprintf_s(buffer, sizeof(buffer), _TRUNCATE, format, args);
    
    va_end(args);
    
    Log(level, std::string(buffer));
}

std::string Logger::LevelToString(Level level) const {
    switch (level) {
        case Level::Debug: return "DEBUG";
        case Level::Info: return "INFO";
        case Level::Warning: return "WARN";
        case Level::Error: return "ERROR";
        case Level::Fatal: return "FATAL";
        default: return "UNKNOWN";
    }
}

std::string Logger::GetTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    
    return ss.str();
}

} // namespace StreamCapture