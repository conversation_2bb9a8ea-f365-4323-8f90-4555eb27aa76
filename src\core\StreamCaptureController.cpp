#include "StreamCaptureController.h"
#include "Common.h"
#include "ConfigManager.h"
#include "MediaServerManager.h"
#include "FrameProcessor.h"
#include "HookCore.h"
#include "DebugUtils.h"
#include <future>
#include <thread>
#include <chrono>
#include <algorithm>
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <atomic>

namespace StreamCapture
{

    // ConfigLoader实现
    bool ConfigLoader::LoadConfig(const std::string &configPath, std::vector<TargetConfig> &targets)
    {
        try
        {
            std::ifstream file(configPath);
            if (!file.is_open())
            {
                return false;
            }

            std::stringstream buffer;
            buffer << file.rdbuf();
            std::string content = buffer.str();

            return ParseJSON(content, targets);
        }
        catch (...)
        {
            return false;
        }
    }

    bool ConfigLoader::SaveConfig(const std::string &configPath, const std::vector<TargetConfig> &targets)
    {
        try
        {
            std::string jsonContent = ConfigToJSON(targets);

            std::ofstream file(configPath);
            if (!file.is_open())
            {
                return false;
            }

            file << jsonContent;
            return true;
        }
        catch (...)
        {
            return false;
        }
    }

    bool ConfigLoader::ValidateConfig(const TargetConfig &config, std::string &errorMessage)
    {
        if (config.name.empty())
        {
            errorMessage = "Target name cannot be empty";
            return false;
        }

        if (config.capture.processName.empty() && config.capture.windowTitle.empty())
        {
            errorMessage = "Either process name or window title must be specified";
            return false;
        }

        if (config.capture.targetFPS == 0 || config.capture.targetFPS > 120)
        {
            errorMessage = "Invalid FPS value (must be 1-120)";
            return false;
        }

        if (config.encoder.bitrate == 0 || config.encoder.bitrate > 100000)
        {
            errorMessage = "Invalid bitrate value (must be 1-100000 kbps)";
            return false;
        }

        if (config.outputs.empty())
        {
            errorMessage = "At least one output must be configured";
            return false;
        }

        return true;
    }

    TargetConfig ConfigLoader::CreateDefaultConfig()
    {
        TargetConfig config;
        config.name = "Default Target";
        config.enabled = true;

        config.capture.processName = "test1.exe";
        config.capture.targetFPS = 30;
        config.capture.quality = "high";

        config.encoder.type = "hardware";
        config.encoder.codec = "h264";
        config.encoder.bitrate = 2000;
        config.encoder.preset = "quality";
        config.encoder.profile = "main";

        OutputConfig fileOutput;
        fileOutput.type = "file";
        fileOutput.url = "output_{timestamp}.mp4";
        fileOutput.enabled = true;
        config.outputs.push_back(fileOutput);

        return config;
    }

    bool ConfigLoader::ParseJSON(const std::string & /*jsonContent*/, std::vector<TargetConfig> &targets)
    {
        // 简化的JSON解析实现
        // 实际项目中应该使用专业的JSON库如nlohmann/json

        // 这里只是示例实现，返回默认配置
        targets.clear();
        targets.push_back(CreateDefaultConfig());

        return true;
    }

    std::string ConfigLoader::ConfigToJSON(const std::vector<TargetConfig> &targets)
    {
        // 简化的JSON生成实现
        std::stringstream json;
        json << "{\n";
        json << "  \"targets\": [\n";

        for (size_t i = 0; i < targets.size(); ++i)
        {
            const auto &target = targets[i];
            json << "    {\n";
            json << "      \"name\": \"" << target.name << "\",\n";
            json << "      \"enabled\": " << (target.enabled ? "true" : "false") << ",\n";
            json << "      \"capture\": {\n";
            json << "        \"processName\": \"" << target.capture.processName << "\",\n";
            json << "        \"fps\": " << target.capture.targetFPS << ",\n";
            json << "        \"quality\": \"" << target.capture.quality << "\"\n";
            json << "      },\n";
            json << "      \"encoder\": {\n";
            json << "        \"type\": \"" << target.encoder.type << "\",\n";
            json << "        \"codec\": \"" << target.encoder.codec << "\",\n";
            json << "        \"bitrate\": " << target.encoder.bitrate << "\n";
            json << "      }\n";
            json << "    }";
            if (i < targets.size() - 1)
                json << ",";
            json << "\n";
        }

        json << "  ]\n";
        json << "}\n";

        return json.str();
    }

    // SystemMonitor实现
    SystemMonitor::SystemMonitor() : running_(false)
    {
    }

    SystemMonitor::~SystemMonitor()
    {
        Shutdown();
    }

    bool SystemMonitor::Initialize()
    {
        if (running_)
            return true;

        running_ = true;
        monitorThread_ = std::thread(&SystemMonitor::MonitoringThread, this);

        return true;
    }

    void SystemMonitor::Shutdown()
    {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (!running_)
            {
                return;
            }
            running_ = false;
        }

        if (monitorThread_.joinable())
        {
            monitorThread_.join();
        }
    }

    SystemMonitor::SystemStatus SystemMonitor::GetStatus() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return status_;
    }

    void SystemMonitor::UpdateStatus()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        // 获取内存信息
        MEMORYSTATUSEX memStatus = {0};
        memStatus.dwLength = sizeof(MEMORYSTATUSEX);
        if (GlobalMemoryStatusEx(&memStatus))
        {
            status_.memoryUsage = static_cast<float>(memStatus.dwMemoryLoad);
            status_.availableMemoryMB = static_cast<size_t>(memStatus.ullAvailPhys / (1024 * 1024));
            status_.isLowMemory = status_.memoryUsage > memoryThreshold_;
        }

        // 简化的CPU使用率检测
        static ULARGE_INTEGER lastCPU, lastSysCPU, lastUserCPU;
        static bool firstTime = true;

        if (firstTime)
        {
            SYSTEM_INFO sysInfo;
            FILETIME ftime, fsys, fuser;

            GetSystemInfo(&sysInfo);
            GetSystemTimeAsFileTime(&ftime);
            memcpy(&lastCPU, &ftime, sizeof(FILETIME));

            GetProcessTimes(GetCurrentProcess(), &ftime, &ftime, &fsys, &fuser);
            memcpy(&lastSysCPU, &fsys, sizeof(FILETIME));
            memcpy(&lastUserCPU, &fuser, sizeof(FILETIME));
            firstTime = false;
            status_.cpuUsage = 0.0f;
        }
        else
        {
            // 简化的CPU计算
            status_.cpuUsage = 25.0f; // 占位值
        }

        status_.isHighCpuLoad = status_.cpuUsage > cpuThreshold_;

        // GPU使用率（占位实现）
        status_.gpuUsage = 0.0f;

        // 进程计数（占位实现）
        status_.processCount = 1;
    }

    void SystemMonitor::MonitoringThread()
    {
        while (running_)
        {
            UpdateStatus();
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    // StreamCaptureController实现
    StreamCaptureController::StreamCaptureController()
        : state_(State::Idle), configPath_(""), mediaServerManager_(nullptr), running_(false), paused_(false)
    {
        // 初始化日志系统
        Logger::Instance().Initialize();
    }

    StreamCaptureController::~StreamCaptureController()
    {
        Shutdown();
    }

    bool StreamCaptureController::Initialize(const std::string &configPath)
    {
        if (state_ != State::Idle)
        {
            return false;
        }

        HandleStateChange(State::Initializing);

        try
        {
            configPath_ = configPath;

            // 加载配置
            configManager_ = std::make_unique<ConfigManager>();
            if (!configManager_->LoadFromFile(configPath_))
            {
                LOG_WARNING("Failed to load config file, using defaults");
                configManager_->SetDefaultConfig();
            }

            // 获取配置
            auto config = configManager_->GetConfig();
            LOG_INFO_F("Loaded config for target process: %s", config.capture.targetProcess.c_str());

            // 初始化调试功能
            if (!DebugUtils::Instance().Initialize(config.global))
            {
                LOG_WARNING("Failed to initialize debug utils");
            }

            DEBUG_LOG("StreamCaptureController initialization started");

            // 初始化MediaServerManager
            mediaServerManager_ = std::make_unique<MediaServerManager>();

            // 初始化Hook核心（使用单例）
            auto &hookCore = HookCore::Instance();
            if (!hookCore.Initialize())
            {
                LOG_ERROR("Failed to initialize hook core");
                HandleStateChange(State::Error);
                return false;
            }

            // 初始化帧处理器
            frameProcessor_ = std::make_unique<FrameProcessor>();
            FrameProcessor::Config fpConfig;
            fpConfig.enableResize = true;
            fpConfig.outputFormat = "nv12";
            if (!frameProcessor_->Initialize(fpConfig))
            {
                LOG_ERROR("Failed to initialize frame processor");
                HandleStateChange(State::Error);
                return false;
            }

            // 初始化多编码器
            multiEncoder_ = std::make_unique<MultiEncoder>();
            if (!multiEncoder_->Initialize())
            {
                LOG_ERROR("Failed to initialize multi encoder");
                HandleStateChange(State::Error);
                return false;
            }

            // 初始化多流控制器
            multiStreamController_ = std::make_unique<MultiStreamController>();
            if (!multiStreamController_->Initialize(config.streams))
            {
                LOG_ERROR("Failed to initialize multi stream controller");
                HandleStateChange(State::Error);
                return false;
            }

            // 启动监控线程
            running_ = true;
            monitoringThread_ = std::thread(&StreamCaptureController::MonitoringThread, this);

            HandleStateChange(State::Idle);
            LOG_INFO("StreamCapture initialized successfully");

            return true;
        }
        catch (const std::exception &e)
        {
            LOG_ERROR_F("Exception during initialization: %s", e.what());
            HandleStateChange(State::Error);
            return false;
        }
        catch (...)
        {
            LOG_ERROR("Unknown exception during initialization");
            HandleStateChange(State::Error);
            return false;
        }
    }

    void StreamCaptureController::Shutdown()
    {
        if (state_ == State::Idle)
        {
            return;
        }

        LOG_INFO("StreamCapture shutting down...");
        HandleStateChange(State::Stopping);

        try
        {
            // 停止所有目标
            StopCapture();

            // 停止监控线程（带超时保护）
            running_ = false;
            if (monitoringThread_.joinable())
            {
                auto future = std::async(std::launch::async, [this]()
                                         { monitoringThread_.join(); });

                if (future.wait_for(std::chrono::seconds(3)) == std::future_status::timeout)
                {
                    LOG_WARNING("Monitoring thread join timeout, continuing shutdown");
                }
            }

            // 清理核心组件
            if (frameProcessor_)
            {
                frameProcessor_->Shutdown();
                frameProcessor_.reset();
            }

            if (multiEncoder_)
            {
                multiEncoder_->Shutdown();
                multiEncoder_.reset();
            }

            if (multiStreamController_)
            {
                multiStreamController_->Shutdown();
                multiStreamController_.reset();
            }

            // 停止MediaMTX服务器
            if (mediaServerManager_)
            {
                LOG_INFO("Stopping MediaMTX server...");
                mediaServerManager_->StopServer();
                mediaServerManager_.reset();
                LOG_INFO("MediaMTX server stopped");
            }

            // 关闭调试工具
            DebugUtils::Instance().Shutdown();

            HandleStateChange(State::Idle);
            LOG_INFO("StreamCapture shutdown complete");
        }
        catch (const std::exception &e)
        {
            LOG_ERROR_F("Exception during shutdown: %s", e.what());
            HandleStateChange(State::Idle);
        }
        catch (...)
        {
            LOG_ERROR("Unknown exception during shutdown");
            HandleStateChange(State::Idle);
        }
    }

    bool StreamCaptureController::StartCapture()
    {
        if (state_ != State::Idle)
        {
            return false;
        }

        LOG_INFO("Starting capture process");
        HandleStateChange(State::Running);

        try
        {
            auto config = configManager_->GetConfig();

            // 创建目标配置
            TargetConfig targetConfig;
            targetConfig.name = config.capture.targetProcess; // 使用进程名作为目标名
            targetConfig.enabled = true;

            // 启动Hook进程注入
            auto &hookCore = HookCore::Instance();
            if (!hookCore.StartHooking(targetConfig))
            {
                LOG_ERROR_F("Failed to start hooking process: %s", config.capture.targetProcess.c_str());
                HandleStateChange(State::Error);
                return false;
            }

            LOG_INFO_F("Successfully started hooking process: %s", config.capture.targetProcess.c_str());

            // 设置帧回调
            hookCore.SetFrameCallback([this](std::unique_ptr<FrameData> frame)
                                      {
                if (frame) {
                    DEBUG_LOG_F("Received frame from hook: %dx%d, size: %zu bytes",
                               frame->width, frame->height, frame->size);

                    // 保存调试帧
                    DEBUG_SAVE_FRAME(*frame, "captured");

                    if (frameProcessor_) {
                        frameProcessor_->ProcessFrame(std::move(frame));
                    }
                } });

            // 设置帧处理器回调
            frameProcessor_->SetProcessedFrameCallback([this](std::unique_ptr<FrameData> frame)
                                                       {
                if (frame && multiEncoder_) {
                    DEBUG_LOG_F("Processed frame ready for encoding: %dx%d, size: %zu bytes",
                               frame->width, frame->height, frame->size);

                    // 保存处理后的帧
                    DEBUG_SAVE_FRAME(*frame, "processed");

                    // 将帧发送给所有启用的编码器
                    auto config = configManager_->GetConfig();
                    for (const auto& encoderConfig : config.encoders) {
                        if (encoderConfig.enabled) {
                            DEBUG_LOG_F("Sending frame to encoder: %s", encoderConfig.id.c_str());
                            DebugUtils::Instance().LogEncodingStart(encoderConfig.id, frame->width, frame->height);

                            bool success = multiEncoder_->EncodeFrame(encoderConfig.id, std::move(frame));
                            DebugUtils::Instance().LogEncodingResult(encoderConfig.id, success);
                        }
                    }
                } });

            LOG_INFO("Capture started successfully");
            return true;
        }
        catch (const std::exception &e)
        {
            LOG_ERROR_F("Exception during capture start: %s", e.what());
            HandleStateChange(State::Error);
            return false;
        }
    }

    bool StreamCaptureController::StopCapture()
    {
        if (state_ != State::Running)
        {
            return false;
        }

        LOG_INFO("Stopping capture process");
        HandleStateChange(State::Stopping);

        try
        {
            // 停止Hook注入
            auto &hookCore = HookCore::Instance();
            hookCore.StopHooking();

            // 停止多流控制器
            if (multiStreamController_)
            {
                multiStreamController_->Shutdown();
            }

            // 停止多编码器
            if (multiEncoder_)
            {
                multiEncoder_->Shutdown();
            }

            // 停止帧处理器
            if (frameProcessor_)
            {
                frameProcessor_->Shutdown();
            }

            LOG_INFO("Capture stopped successfully");
            HandleStateChange(State::Idle);
            return true;
        }
        catch (const std::exception &e)
        {
            LOG_ERROR_F("Exception during capture stop: %s", e.what());
            HandleStateChange(State::Error);
            return false;
        }
    }

    bool StreamCaptureController::PauseCapture(bool pause)
    {
        paused_ = pause;

        // 暂停/恢复Hook捕获
        auto &hookCore = HookCore::Instance();
        hookCore.EnableCapture(!pause);

        LOG_INFO_F("Capture %s", pause ? "paused" : "resumed");
        return true;
    }

    bool StreamCaptureController::StartTarget(const std::string &targetName)
    {
        // 简化实现：直接启动捕获
        return StartCapture();
    }

    bool StreamCaptureController::StopTarget(const std::string &targetName)
    {
        // 简化实现：直接停止捕获
        return StopCapture();
    }

    bool StreamCaptureController::IsTargetActive(const std::string &targetName) const
    {
        // 简化实现：检查整体状态
        return state_ == State::Running;
    }

    std::vector<std::string> StreamCaptureController::GetTargetNames() const
    {
        std::vector<std::string> names;
        if (configManager_)
        {
            auto config = configManager_->GetConfig();
            names.push_back(config.capture.targetProcess);
        }
        return names;
    }

    std::vector<TargetConfig> StreamCaptureController::GetTargets() const
    {
        std::vector<TargetConfig> targets;
        if (configManager_)
        {
            auto config = configManager_->GetConfig();
            TargetConfig target;
            target.name = config.capture.targetProcess; // 使用进程名作为目标名
            target.enabled = config.capture.enabled;
            targets.push_back(target);
        }
        return targets;
    }

    StreamCaptureController::GlobalStats StreamCaptureController::GetGlobalStats() const
    {
        GlobalStats stats = {};

        // 简化统计
        stats.activeTargets = (state_ == State::Running) ? 1 : 0;

        // 从Hook核心获取统计
        auto &hookCore = HookCore::Instance();
        auto hookStats = hookCore.GetStats();
        stats.totalFramesCaptured = hookStats.captureFrames;
        stats.droppedFrames = hookStats.droppedFrames;

        return stats;
    }

    // 旧方法已移除，使用新的架构

    // 旧方法已移除

    void StreamCaptureController::HandleStateChange(State newState)
    {
        State oldState = state_;
        state_ = newState;

        if (stateCallback_ && oldState != newState)
        {
            stateCallback_(oldState, newState);
        }
    }

    void StreamCaptureController::MonitoringThread()
    {
        while (running_)
        {
            try
            {
                // PerformMaintenance();
            }
            catch (...)
            {
                LOG_ERROR("Exception in monitoring thread");
            }

            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }

    // Logger实现
    Logger &Logger::Instance()
    {
        static Logger instance;
        return instance;
    }

    bool Logger::Initialize(const std::string &logFile, Level minLevel)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        logFile_ = logFile;
        minLevel_ = minLevel;

        if (!logFile_.empty())
        {
            // 先关闭已有的文件句柄
            if (fileHandle_)
            {
                fclose(fileHandle_);
                fileHandle_ = nullptr;
            }

            // 尝试打开日志文件
            FILE *tempHandle = nullptr;
            errno_t err = fopen_s(&tempHandle, logFile_.c_str(), "a");
            if (err != 0 || !tempHandle)
            {
                fileHandle_ = nullptr;
                // 即使文件打开失败，也不应该导致初始化失败
                // 只是不写入文件，但仍可以输出到控制台
                Log(Level::Warning, "Failed to open log file: " + logFile_);
                return true;
            }
            fileHandle_ = tempHandle;
        }

        Log(Level::Info, "Logger initialized");
        return true;
    }

    void Logger::Shutdown()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (fileHandle_)
        {
            try
            {
                fclose(fileHandle_);
            }
            catch (...)
            {
                // 忽略关闭文件时的异常
            }
            fileHandle_ = nullptr;
        }

        logFile_.clear();
    }

    void Logger::Log(Level level, const std::string &message)
    {
        // 先检查日志级别
        if (level < minLevel_)
        {
            return;
        }

        // 创建日志线程避免阻塞
        std::thread([=]()
                    {
        try {
            // 检查是否已初始化
            if (!fileHandle_ && logFile_.empty()) {
                // 如果没有指定日志文件，只输出到控制台
                std::string timestamp = GetTimestamp();
                std::string levelStr = LevelToString(level);
                std::string logLine = "[" + timestamp + "] [" + levelStr + "] " + message + "\n";
                
                // 输出到控制台
                printf("%s", logLine.c_str());
                fflush(stdout);
                return;
            }

            std::string timestamp = GetTimestamp();
            std::string levelStr = LevelToString(level);
            std::string logLine = "[" + timestamp + "] [" + levelStr + "] " + message + "\n";
            
            // 输出到控制台
            printf("%s", logLine.c_str());
            fflush(stdout);
            
            // 输出到文件
            if (fileHandle_) {
                fprintf(fileHandle_, "%s", logLine.c_str());
                fflush(fileHandle_);
            }
            
            // 添加异步日志刷新功能
            if (fileHandle_ && (level == Level::Error || level == Level::Fatal)) {
                static std::atomic_int flushCounter{0};
                if (++flushCounter % 10 == 0) {
                    std::lock_guard<std::mutex> lock(mutex_);
                    fflush(fileHandle_);
                }
            }
        } catch (...) {
            // 忽略日志记录中的任何异常，避免影响主程序流程
            // 可以考虑输出到标准错误
            fprintf(stderr, "[ERROR] Failed to log message\n");
            fflush(stderr);
        } })
            .detach();
    }

    void Logger::LogFormat(Level level, const char *format, ...)
    {
        if (level < minLevel_)
        {
            return;
        }

        va_list args;
        va_start(args, format);

        char buffer[4096];
        vsnprintf_s(buffer, sizeof(buffer), _TRUNCATE, format, args);

        va_end(args);

        Log(level, std::string(buffer));
    }

    std::string Logger::LevelToString(Level level) const
    {
        switch (level)
        {
        case Level::Debug:
            return "DEBUG";
        case Level::Info:
            return "INFO";
        case Level::Warning:
            return "WARN";
        case Level::Error:
            return "ERROR";
        case Level::Fatal:
            return "FATAL";
        default:
            return "UNKNOWN";
        }
    }

    std::string Logger::GetTimestamp() const
    {
        try
        {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                          now.time_since_epoch()) %
                      1000;

            // 使用localtime_s替代localtime以提高安全性
            std::tm local_tm;
            localtime_s(&local_tm, &time_t);

            std::stringstream ss;
            ss << std::put_time(&local_tm, "%Y-%m-%d %H:%M:%S");
            ss << '.' << std::setfill('0') << std::setw(3) << ms.count();

            return ss.str();
        }
        catch (...)
        {
            // 如果时间戳生成失败，返回一个默认值
            return "0000-00-00 00:00:00.000";
        }
    }

} // namespace StreamCapture