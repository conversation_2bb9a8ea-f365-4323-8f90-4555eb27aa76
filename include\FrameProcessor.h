#pragma once

#include "Common.h"
#include <queue>
#include <condition_variable>

namespace StreamCapture {

// 线程安全的帧缓冲区
class FrameBuffer {
public:
    explicit FrameBuffer(size_t maxFrames = 10);
    ~FrameBuffer();
    
    // 帧操作
    bool PushFrame(std::unique_ptr<FrameData> frame);
    std::unique_ptr<FrameData> PopFrame(uint32_t timeoutMs = 0);
    
    // 状态查询
    size_t GetSize() const;
    size_t GetMaxSize() const;
    bool IsEmpty() const;
    bool IsFull() const;
    
    // 控制
    void Clear();
    void SetMaxSize(size_t maxFrames);
    
    // 统计
    struct Stats {
        uint64_t totalPushed = 0;
        uint64_t totalPopped = 0;
        uint64_t droppedFrames = 0;
        size_t currentSize = 0;
        size_t maxSize = 0;
    };
    
    Stats GetStats() const;
    
private:
    mutable std::mutex mutex_;
    std::condition_variable condition_;
    std::queue<std::unique_ptr<FrameData>> frames_;
    size_t maxFrames_;
    Stats stats_;
};

// 图像格式转换器
class ImageConverter {
public:
    ImageConverter();
    ~ImageConverter();
    
    // 初始化
    bool Initialize();
    void Shutdown();
    
    // 格式转换
    bool ConvertRGBAToNV12(const FrameData& input, FrameData& output);
    bool ConvertRGBAToI420(const FrameData& input, FrameData& output);
    bool ConvertBGRAToRGBA(const FrameData& input, FrameData& output);
    
    // 缩放
    bool ResizeFrame(const FrameData& input, FrameData& output, 
                     uint32_t targetWidth, uint32_t targetHeight);
    
    // 裁剪
    bool CropFrame(const FrameData& input, FrameData& output,
                   uint32_t x, uint32_t y, uint32_t width, uint32_t height);
    
    // 格式检测
    static bool IsSupportedFormat(DXGI_FORMAT format);
    static size_t GetBytesPerPixel(DXGI_FORMAT format);
    static std::string FormatToString(DXGI_FORMAT format);
    
private:
    // 转换实现
    void ConvertRGBAToNV12_CPU(const uint8_t* rgba, uint8_t* nv12,
                               uint32_t width, uint32_t height, uint32_t stride);
    void ConvertRGBAToI420_CPU(const uint8_t* rgba, uint8_t* i420,
                               uint32_t width, uint32_t height, uint32_t stride);
    void ResizeFrame_CPU(const uint8_t* input, uint8_t* output,
                         uint32_t inputWidth, uint32_t inputHeight, uint32_t inputStride,
                         uint32_t outputWidth, uint32_t outputHeight, uint32_t outputStride,
                         size_t bytesPerPixel);
    
    // 辅助函数
    uint8_t ClampToByte(int value);
    
    bool initialized_;
};

// 帧处理器 - 整合缓冲和转换
class FrameProcessor {
public:
    struct Config {
        size_t bufferSize = 10;
        bool enableResize = false;
        uint32_t targetWidth = 0;
        uint32_t targetHeight = 0;
        bool enableCrop = false;
        uint32_t cropX = 0;
        uint32_t cropY = 0;
        uint32_t cropWidth = 0;
        uint32_t cropHeight = 0;
        std::string outputFormat = "nv12";  // nv12, i420, rgba
    };
    
    FrameProcessor();
    ~FrameProcessor();
    
    // 生命周期
    bool Initialize(const Config& config);
    void Shutdown();
    
    // 帧处理
    bool ProcessFrame(std::unique_ptr<FrameData> inputFrame);
    std::unique_ptr<FrameData> GetProcessedFrame(uint32_t timeoutMs = 0);
    
    // 配置
    void UpdateConfig(const Config& config);
    Config GetConfig() const;
    
    // 统计
    struct Stats {
        uint64_t inputFrames = 0;
        uint64_t outputFrames = 0;
        uint64_t droppedFrames = 0;
        float avgProcessingTime = 0.0f;
        size_t bufferUtilization = 0;
    };
    
    Stats GetStats() const;
    
    // 回调
    using ProcessedFrameCallback = std::function<void(std::unique_ptr<FrameData>)>;
    void SetProcessedFrameCallback(ProcessedFrameCallback callback);
    
private:
    void ProcessingThread();
    bool ProcessSingleFrame(const FrameData& input, FrameData& output);
    
    Config config_;
    Stats stats_;
    
    std::unique_ptr<FrameBuffer> inputBuffer_;
    std::unique_ptr<FrameBuffer> outputBuffer_;
    std::unique_ptr<ImageConverter> converter_;
    
    std::thread processingThread_;
    std::atomic<bool> running_;
    
    ProcessedFrameCallback callback_;
    
    mutable std::mutex mutex_;
    LARGE_INTEGER perfFrequency_;
};

} // namespace StreamCapture