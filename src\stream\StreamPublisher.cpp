#include "StreamPublisher.h"
#include "Common.h"
#include "StreamCaptureController.h"
#include <thread>
#include <chrono>

namespace StreamCapture
{

    // MultiStreamController implementation
    MultiStreamController::MultiStreamController()
        : running_(false)
    {
        streamManager_ = std::make_unique<StreamManager>();
    }

    MultiStreamController::~MultiStreamController()
    {
        Shutdown();
    }

    bool MultiStreamController::Initialize(const Config &config)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        config_ = config;

        // 初始化流管理器
        if (!streamManager_)
        {
            return false;
        }

        // 配置流管理器
        for (const auto &streamConfig : config_.streams)
        {
            if (!streamManager_->AddStream(streamConfig))
            {
                return false;
            }
        }

        // 启动处理线程
        running_ = true;
        processingThread_ = std::thread(&MultiStreamController::ProcessingThread, this);

        return true;
    }

    void MultiStreamController::Shutdown()
    {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (!running_)
            {
                return;
            }

            running_ = false;
        }

        // 通知处理线程退出
        packetCondition_.notify_all();

        // 等待处理线程结束
        if (processingThread_.joinable())
        {
            processingThread_.join();
        }

        // 停止所有流
        if (streamManager_)
        {
            streamManager_->StopAllStreams();
        }

        // 清空数据包队列
        {
            std::lock_guard<std::mutex> lock(packetMutex_);
            while (!packetQueue_.empty())
            {
                packetQueue_.pop();
            }
        }
        
        // 确保流管理器也正确关闭
        if (streamManager_) {
            streamManager_->Shutdown();
        }
    }

    bool MultiStreamController::HandleEncodedPacket(const std::string &encoderId, std::unique_ptr<EncodedPacket> packet)
    {
        // 避免未引用参数警告
        (void)encoderId;
        
        if (!packet)
        {
            return false;
        }

        {
            std::lock_guard<std::mutex> lock(packetMutex_);

            // 检查队列大小
            if (packetQueue_.size() >= config_.packetBufferSize)
            {
                // 丢弃最旧的数据包
                packetQueue_.pop();
                stats_.totalDroppedPackets++;
            }

            packetQueue_.push(std::move(packet));
            stats_.totalInputPackets++;
        }

        // 通知处理线程
        packetCondition_.notify_one();

        return true;
    }

    bool MultiStreamController::StartAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (!streamManager_)
        {
            return false;
        }

        return streamManager_->StartAllStreams();
    }

    void MultiStreamController::StopAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (streamManager_)
        {
            streamManager_->StopAllStreams();
        }
    }

    bool MultiStreamController::IsAnyStreamActive() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (!streamManager_)
        {
            return false;
        }

        return streamManager_->IsAnyStreamActive();
    }

    bool MultiStreamController::UpdateConfig(const Config &config)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        config_ = config;

        // 重新配置流管理器
        if (streamManager_)
        {
            // 停止现有流
            streamManager_->StopAllStreams();

            // 添加新流配置
            for (const auto &streamConfig : config_.streams)
            {
                streamManager_->AddStream(streamConfig);
            }
        }

        return true;
    }

    MultiStreamController::Config MultiStreamController::GetConfig() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return config_;
    }

    MultiStreamController::Stats MultiStreamController::GetStats() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        Stats currentStats = stats_;

        if (streamManager_)
        {
            currentStats.streamStats = streamManager_->GetGlobalStats();
        }

        return currentStats;
    }

    void MultiStreamController::ProcessingThread()
    {
        while (running_)
        {
            std::unique_ptr<EncodedPacket> packet;

            // 获取数据包
            {
                std::unique_lock<std::mutex> lock(packetMutex_);

                // 等待数据包或退出信号
                packetCondition_.wait(lock, [this]
                                      { return !packetQueue_.empty() || !running_; });

                if (!running_)
                {
                    break;
                }

                if (!packetQueue_.empty())
                {
                    packet = std::move(packetQueue_.front());
                    packetQueue_.pop();
                }
            }

            // 处理数据包
            if (packet)
            {
                try
                {
                    DistributePacket(*packet);
                    stats_.totalOutputPackets++;
                }
                catch (...)
                {
                    stats_.totalDroppedPackets++;
                }
            }
        }
    }

    void MultiStreamController::DistributePacket(const EncodedPacket &packet)
    {
        if (!streamManager_)
        {
            return;
        }

        if (config_.enableBroadcast)
        {
            // 广播到所有活跃流
            streamManager_->BroadcastPacket(packet);
        }
        else
        {
            // 发送到特定流（这里需要根据实际需求实现路由逻辑）
            // 暂时广播到所有流
            streamManager_->BroadcastPacket(packet);
        }

        // 更新统计信息
        if (statusCallback_)
        {
            statusCallback_("Packet distributed successfully");
        }
    }

    // StreamManager implementation
    StreamManager::StreamManager() : running_(false)
    {
    }

    StreamManager::~StreamManager()
    {
        Shutdown();
    }

    bool StreamManager::Initialize()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        running_ = true;
        return true;
    }

    void StreamManager::Shutdown()
    {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (!running_)
            {
                return;
            }
            running_ = false;
        }

        // 停止所有流
        StopAllStreams();

        // 等待重连线程结束
        for (auto &stream : streams_)
        {
            if (stream->reconnectThread.joinable())
            {
                stream->shouldReconnect = false;
                // 使用超时等待或者通知线程退出
                stream->reconnectThread.join();
            }
        }

        streams_.clear();
    }

    bool StreamManager::AddStream(const StreamConfig &config)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        // 检查是否已存在
        if (FindStream(config.streamId))
        {
            return false;
        }

        // 创建推流器
        auto publisher = CreatePublisher(config.publisherConfig);
        if (!publisher)
        {
            return false;
        }

        // 创建流信息
        auto streamInfo = std::make_unique<StreamInfo>();
        streamInfo->id = config.streamId;
        streamInfo->config = config;
        streamInfo->publisher = std::move(publisher);
        streamInfo->shouldReconnect = config.autoReconnect;
        streamInfo->running = false;

        streams_.push_back(std::move(streamInfo));

        return true;
    }

    bool StreamManager::RemoveStream(const std::string &streamId)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        if (it == streams_.end())
        {
            return false;
        }

        // 停止流
        (*it)->running = false;
        (*it)->shouldReconnect = false;

        if ((*it)->publisher)
        {
            (*it)->publisher->Disconnect();
        }

        if ((*it)->reconnectThread.joinable())
        {
            (*it)->reconnectThread.join();
        }

        streams_.erase(it);

        return true;
    }

    bool StreamManager::EnableStream(const std::string &streamId, bool enable)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream)
        {
            return false;
        }

        stream->config.enabled = enable;

        if (enable)
        {
            return ConnectStream(streamId);
        }
        else
        {
            DisconnectStream(streamId);
            return true;
        }
    }

    bool StreamManager::SendPacket(const std::string &streamId, const EncodedPacket &packet)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->config.enabled || !stream->publisher)
        {
            return false;
        }

        return stream->publisher->SendPacket(packet);
    }

    bool StreamManager::SendPacketToAllStreams(const EncodedPacket &packet)
    {
        return BroadcastPacket(packet);
    }

    bool StreamManager::BroadcastPacket(const EncodedPacket &packet)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->config.enabled && stream->publisher && stream->publisher->IsConnected())
            {
                if (!stream->publisher->SendPacket(packet))
                {
                    success = false;
                }
            }
        }

        return success;
    }

    bool StreamManager::StartAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        bool success = true;
        for (auto &stream : streams_)
        {
            if (stream->config.enabled)
            {
                if (!ConnectStream(stream->id))
                {
                    success = false;
                }
            }
        }

        return success;
    }

    void StreamManager::StopAllStreams()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        for (auto &stream : streams_)
        {
            DisconnectStream(stream->id);
        }
    }

    bool StreamManager::IsAnyStreamActive() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        for (const auto &stream : streams_)
        {
            if (stream->config.enabled && stream->publisher && stream->publisher->IsConnected())
            {
                return true;
            }
        }

        return false;
    }

    bool StreamManager::ConnectStream(const std::string &streamId)
    {
        auto stream = FindStream(streamId);
        if (!stream || !stream->publisher)
        {
            return false;
        }

        if (stream->publisher->IsConnected())
        {
            return true;
        }

        bool success = stream->publisher->Connect();
        if (success)
        {
            stream->running = true;
        }

        return success;
    }

    void StreamManager::DisconnectStream(const std::string &streamId)
    {
        auto stream = FindStream(streamId);
        if (!stream || !stream->publisher)
        {
            return;
        }

        stream->running = false;
        stream->shouldReconnect = false;

        if (stream->publisher->IsConnected())
        {
            stream->publisher->Disconnect();
        }
    }

    bool StreamManager::IsStreamConnected(const std::string &streamId) const
    {
        auto stream = FindStream(streamId);
        if (!stream || !stream->publisher)
        {
            return false;
        }

        return stream->publisher->IsConnected();
    }

    std::vector<std::string> StreamManager::GetStreamIds() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        std::vector<std::string> ids;
        for (const auto &stream : streams_)
        {
            ids.push_back(stream->id);
        }

        return ids;
    }

    StreamState StreamManager::GetStreamState(const std::string &streamId) const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->publisher)
        {
            return StreamState::Disconnected;
        }

        return stream->publisher->GetState();
    }

    StreamPublisher::Stats StreamManager::GetStreamStats(const std::string &streamId) const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto stream = FindStream(streamId);
        if (!stream || !stream->publisher)
        {
            return StreamPublisher::Stats{};
        }

        return stream->publisher->GetStats();
    }

    StreamManager::GlobalStats StreamManager::GetGlobalStats() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        GlobalStats stats;
        stats.totalStreams = static_cast<uint32_t>(streams_.size());

        for (const auto &stream : streams_)
        {
            if (stream->publisher)
            {
                if (stream->publisher->IsConnected())
                {
                    stats.connectedStreams++;
                }

                auto streamStats = stream->publisher->GetStats();
                stats.totalPackets += streamStats.totalPackets;
                stats.totalBytes += streamStats.totalBytes;
                stats.totalDropped += streamStats.droppedPackets;
            }
        }

        return stats;
    }

    std::unique_ptr<StreamPublisher> StreamManager::CreatePublisher(const StreamPublisher::Config &config)
    {
        if (config.protocol == "rtmp")
        {
            return std::make_unique<RTMPPublisher>();
        }
        else if (config.protocol == "srt")
        {
            return std::make_unique<SRTPublisher>();
        }
        else if (config.protocol == "file")
        {
            return std::make_unique<FilePublisher>();
        }

        return nullptr;
    }

    StreamManager::StreamInfo *StreamManager::FindStream(const std::string &streamId)
    {
        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        return (it != streams_.end()) ? it->get() : nullptr;
    }

    const StreamManager::StreamInfo *StreamManager::FindStream(const std::string &streamId) const
    {
        auto it = std::find_if(streams_.begin(), streams_.end(),
                               [&streamId](const std::unique_ptr<StreamInfo> &stream)
                               {
                                   return stream->id == streamId;
                               });

        return (it != streams_.end()) ? it->get() : nullptr;
    }

    void StreamManager::ReconnectThread(StreamInfo *streamInfo)
    {
        // 重连逻辑实现
        while (streamInfo->shouldReconnect && running_)
        {
            if (!streamInfo->publisher->IsConnected())
            {
                if (streamInfo->publisher->Connect())
                {
                    streamInfo->running = true;
                }
                else
                {
                    std::this_thread::sleep_for(std::chrono::milliseconds(streamInfo->config.publisherConfig.reconnectDelayMs));
                }
            }

            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    /*
    bool StreamManager::SendMetadata(const std::string &streamId, const std::string &metadata)
    {
        // 避免未引用参数警告
        (void)streamId;
        (void)metadata;
        
        // 实现发送元数据逻辑
        return false;
    }
    */

    void StreamManager::HandleStreamStateChange(const std::string &streamId, StreamState oldState, StreamState newState)
    {
        if (streamStateCallback_)
        {
            streamStateCallback_(streamId, oldState, newState);
        }
    }

    void StreamManager::HandleStreamError(const std::string &streamId, const ErrorInfo &error)
    {
        if (streamErrorCallback_)
        {
            streamErrorCallback_(streamId, error);
        }
    }

    // RTMPPublisher implementation
    RTMPPublisher::RTMPPublisher()
        : formatContext_(nullptr), videoStream_(nullptr), codecContext_(nullptr),
          headerWritten_(false), startTime_(0), frameIndex_(0) {}

    RTMPPublisher::~RTMPPublisher()
    {
        Shutdown();
    }

    bool RTMPPublisher::Initialize(const Config &config)
    {
        config_ = config;
        return SetupFFmpegContext();
    }

    void RTMPPublisher::Shutdown()
    {
        Disconnect();
        CleanupFFmpegContext();
    }

    bool RTMPPublisher::Connect()
    {
        // 简单实现，实际应该连接到RTMP服务器
        state_ = StreamState::Connected;
        return WriteHeader();
    }

    void RTMPPublisher::Disconnect()
    {
        if (state_ == StreamState::Connected)
        {
            WriteTrailer();
        }
        state_ = StreamState::Disconnected;
    }

    bool RTMPPublisher::IsConnected() const
    {
        return state_ == StreamState::Connected;
    }

    bool RTMPPublisher::SendPacket(const EncodedPacket &packet)
    {
        if (!IsConnected())
            return false;

        // 简单实现，实际应该发送到RTMP服务器
        stats_.totalPackets++;
        stats_.totalBytes += packet.size;
        return true;
    }

    bool RTMPPublisher::SendMetadata(const std::string &metadata)
    {
      (void)metadata;
        return IsConnected();
    }

    StreamPublisher::Stats RTMPPublisher::GetStats() const
    {
        return stats_;
    }

    bool RTMPPublisher::SetupFFmpegContext()
    {
        // 简单实现，实际应该设置FFmpeg上下文
        return true;
    }

    void RTMPPublisher::CleanupFFmpegContext()
    {
        // 清理FFmpeg上下文
    }

    bool RTMPPublisher::WriteHeader()
    {
        // 写入RTMP头
        headerWritten_ = true;
        return true;
    }

    bool RTMPPublisher::WriteTrailer()
    {
        // 写入RTMP尾部
        return true;
    }

    // SRTPublisher implementation
    SRTPublisher::SRTPublisher() : srtSocket_(nullptr) {}

    SRTPublisher::~SRTPublisher()
    {
        Shutdown();
    }

    bool SRTPublisher::Initialize(const Config &config)
    {
        config_ = config;
        return true; // 简单实现
    }

    void SRTPublisher::Shutdown()
    {
        Disconnect();
    }

    bool SRTPublisher::Connect()
    {
        state_ = StreamState::Connected;
        return true;
    }

    void SRTPublisher::Disconnect()
    {
        state_ = StreamState::Disconnected;
    }

    bool SRTPublisher::IsConnected() const
    {
        return state_ == StreamState::Connected;
    }

    bool SRTPublisher::SendPacket(const EncodedPacket &packet)
    {
        if (!IsConnected())
            return false;

        stats_.totalPackets++;
        stats_.totalBytes += packet.size;
        return true;
    }

    bool SRTPublisher::SendMetadata(const std::string &metadata)
    {
      (void)metadata;
        return IsConnected();
    }

    StreamPublisher::Stats SRTPublisher::GetStats() const
    {
        return stats_;
    }

    // FilePublisher implementation
    FilePublisher::FilePublisher()
        : formatContext_(nullptr), videoStream_(nullptr),
          headerWritten_(false), startTime_(0), frameIndex_(0) {}

    FilePublisher::~FilePublisher()
    {
        Shutdown();
    }

    bool FilePublisher::Initialize(const Config &config)
    {
        config_ = config;
        return SetupFileOutput();
    }

    void FilePublisher::Shutdown()
    {
        Disconnect();
        CleanupFileOutput();
    }

    bool FilePublisher::Connect()
    {
        // 打开文件
        currentFileName_ = GenerateFileName();
        state_ = StreamState::Connected;
        return true;
    }

    void FilePublisher::Disconnect()
    {
        state_ = StreamState::Disconnected;
    }

    bool FilePublisher::IsConnected() const
    {
        return state_ == StreamState::Connected;
    }

    bool FilePublisher::SendPacket(const EncodedPacket &packet)
    {
        if (!IsConnected())
            return false;

        stats_.totalPackets++;
        stats_.totalBytes += packet.size;
        return true;
    }

    bool FilePublisher::SendMetadata(const std::string &metadata)
    {
      (void)metadata;
        return IsConnected();
    }

    StreamPublisher::Stats FilePublisher::GetStats() const
    {
        return stats_;
    }

    bool FilePublisher::SetupFileOutput()
    {
        // 简单实现，实际应该设置文件输出
        return true;
    }

    void FilePublisher::CleanupFileOutput()
    {
        // 清理文件输出
    }

    std::string FilePublisher::GenerateFileName() const
    {
        // 生成文件名
        return "output.mp4";
    }

} // namespace StreamCapture
