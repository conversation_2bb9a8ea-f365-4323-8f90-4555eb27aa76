#include "StreamPublisher.h"

namespace StreamCapture
{

  // RTMPPublisher 实现
  RTMPPublisher::RTMPPublisher() = default;
  RTMPPublisher::~RTMPPublisher() = default;

  bool RTMPPublisher::Initialize(const Config &config) { return false; }
  void RTMPPublisher::Shutdown() {}
  bool RTMPPublisher::Connect() { return false; }
  void RTMPPublisher::Disconnect() {}
  bool RTMPPublisher::IsConnected() const { return false; }
  bool RTMPPublisher::SendPacket(const EncodedPacket &packet) { return false; }
  bool RTMPPublisher::SendMetadata(const std::string &metadata) { return false; }
  StreamPublisher::Stats RTMPPublisher::GetStats() const { return Stats{}; }

  // SRTPublisher 实现
  SRTPublisher::SRTPublisher() = default;
  SRTPublisher::~SRTPublisher() = default;

  bool SRTPublisher::Initialize(const Config &config) { return false; }
  void SRTPublisher::Shutdown() {}
  bool SRTPublisher::Connect() { return false; }
  void SRTPublisher::Disconnect() {}
  bool SRTPublisher::IsConnected() const { return false; }
  bool SRTPublisher::SendPacket(const EncodedPacket &packet) { return false; }
  bool SRTPublisher::SendMetadata(const std::string &metadata) { return false; }
  StreamPublisher::Stats SRTPublisher::GetStats() const { return Stats{}; }

  // FilePublisher 实现
  FilePublisher::FilePublisher() = default;
  FilePublisher::~FilePublisher() = default;

  bool FilePublisher::Initialize(const Config &config) { return false; }
  void FilePublisher::Shutdown() {}
  bool FilePublisher::Connect() { return false; }
  void FilePublisher::Disconnect() {}
  bool FilePublisher::IsConnected() const { return false; }
  bool FilePublisher::SendPacket(const EncodedPacket &packet) { return false; }
  bool FilePublisher::SendMetadata(const std::string &metadata) { return false; }
  StreamPublisher::Stats FilePublisher::GetStats() const { return Stats{}; }

}