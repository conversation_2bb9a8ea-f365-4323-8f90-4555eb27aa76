f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\hookcore.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\videoencoder.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\frameprocessor.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\streampublisher.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\ffmpegpublisher.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\ffmpegencoder.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\win7compatibility.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\configmanager.obj
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\cmakefiles\generate.stamp
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\vc143.pdb
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\streamcapture.tlog\cl.17320.write.1.tlog
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\streamcapture.tlog\cl.command.1.tlog
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\streamcapture.tlog\cl.read.1.tlog
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\streamcapture.tlog\custombuild.command.1.tlog
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\streamcapture.tlog\custombuild.read.1.tlog
f:\dev\unity3d_project\sm-record-main\streamcapture\build_fresh\streamcapture.dir\relwithdebinfo\streamcapture.tlog\custombuild.write.1.tlog
