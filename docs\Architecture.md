# StreamCapture 系统架构设计

## 概述
StreamCapture 是一个轻量级、Windows 7兼容的多路视频采集和推流系统，专门设计用于hook Direct3D 11应用程序并进行实时视频编码推流。

## 核心原则
1. **Windows 7兼容性优先** - 使用保守的API和内存管理策略
2. **稳健轻量** - 最小化依赖，严格的错误处理
3. **模块化设计** - 组件间松耦合，便于维护和扩展
4. **硬件加速优先** - 优先使用硬件编码，软编码作为后备

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    StreamCapture 主控制器                    │
├─────────────────────────────────────────────────────────────┤
│  配置管理器  │  进程管理器  │  任务调度器  │  状态监控器  │
└─────────────────────────────────────────────────────────────┘
                              │
              ┌───────────────┼───────────────┐
              │               │               │
    ┌─────────▼─────────┐ ┌───▼────┐ ┌───────▼─────────┐
    │  Hook注入模块     │ │ 帧采集 │ │  编码推流模块   │
    ├─────────────────┤ │ 缓冲区 │ ├─────────────────┤
    │ • MinHook核心   │ │        │ │ • 硬件编码器    │
    │ • D3D11 Hook    │ └────────┘ │ • 软件编码器    │
    │ • Present拦截   │             │ • RTMP推流      │
    │ • 错误恢复      │             │ • 文件录制      │
    └─────────────────┘             └─────────────────┘
```

## 核心组件

### 1. Hook注入模块 (src/core/)
- **HookCore.cpp**: 核心Hook管理器
- **D3D11Hook.cpp**: Direct3D 11 Present方法拦截
- **ProcessInjector.cpp**: 安全的进程注入
- **Win7Compatibility.cpp**: Windows 7特定兼容性处理

### 2. 帧采集模块 (src/capture/)
- **FrameCapture.cpp**: 帧数据捕获和预处理
- **TextureManager.cpp**: D3D11纹理管理
- **FrameBuffer.cpp**: 线程安全的帧缓冲区

### 3. 编码推流模块 (src/encoder/)
- **EncoderManager.cpp**: 编码器管理和选择
- **HardwareEncoder.cpp**: NVENC/QSV硬件编码
- **SoftwareEncoder.cpp**: FFmpeg软编码
- **StreamPublisher.cpp**: RTMP推流和文件输出

### 4. 工具模块 (src/utils/)
- **ConfigParser.cpp**: JSON配置文件解析
- **Logger.cpp**: 轻量级日志系统
- **ThreadPool.cpp**: 线程池管理
- **PerformanceMonitor.cpp**: 性能监控

## 技术选型

### Hook技术
- **MinHook**: 主要Hook库，稳定可靠
- **Kiero**: D3D11函数表访问（作为备选）
- **CreateRemoteThread**: 进程注入（Windows 7兼容版本）

### 视频编码
- **硬件编码优先级**:
  1. NVIDIA NVENC (H.264/H.265)
  2. Intel Quick Sync Video
  3. AMD AMF
- **软件编码后备**: FFmpeg libx264/libx265

### 推流协议
- **RTMP**: 主要推流协议
- **SRT**: 低延迟替代方案
- **文件输出**: MP4/FLV格式

## 数据流

```
目标进程 → D3D11 Present → 帧捕获 → 格式转换 → 编码队列
                                                    │
推流输出 ← RTMP推流 ← 编码完成 ← 硬件/软件编码 ←─────┘
```

## 配置系统

### 主配置文件 (config/capture.json)
```json
{
  "global": {
    "logLevel": "INFO",
    "maxMemoryMB": 512,
    "compatibility": "win7"
  },
  "targets": [
    {
      "name": "Unity Game 1",
      "processName": "test1.exe",
      "windowTitle": "*Unity*",
      "capture": {
        "fps": 30,
        "quality": "high"
      },
      "encoder": {
        "type": "hardware",
        "codec": "h264",
        "bitrate": 2000
      },
      "output": {
        "rtmp": "rtmp://server/live/stream1",
        "file": "output1.mp4"
      }
    }
  ]
}
```

## Windows 7兼容性策略

### 1. API兼容性
- 避免使用Windows 8+特有API
- 使用GetProcAddress动态加载新API
- 提供降级实现

### 2. 内存管理
- 使用较小的内存块
- 频繁的内存释放
- 避免大块内存分配

### 3. 异常处理
- 使用传统的SEH而非C++异常
- 每个关键操作都有错误检查
- 提供安全的回退机制

## 性能优化

### 1. 多线程设计
- Hook线程: 最小化Present调用延迟
- 捕获线程: 帧数据复制和预处理
- 编码线程: 视频编码处理
- 推流线程: 网络IO处理

### 2. 内存优化
- 使用对象池减少内存分配
- 零拷贝设计（尽可能）
- 智能缓冲区管理

### 3. GPU优化
- 直接从GPU内存读取
- 避免CPU-GPU同步
- 使用异步操作

## 错误处理和恢复

### 1. Hook错误
- 自动重试机制
- 安全卸载和重新注入
- 目标进程崩溃检测

### 2. 编码错误
- 硬件编码失败自动切换到软编码
- 编码参数自适应调整
- 丢帧处理策略

### 3. 网络错误
- 自动重连机制
- 缓冲区管理
- 降级推流质量

## 安全考虑

### 1. 进程注入安全
- 目标进程权限检查
- 注入前的完整性验证
- 安全的内存操作

### 2. 系统稳定性
- 资源泄漏检测
- 异常情况下的优雅退出
- 最小化系统影响

## 部署和维护

### 1. 依赖管理
- 使用vcpkg管理C++依赖
- 静态链接减少部署复杂性
- 版本兼容性检查

### 2. 调试支持
- 详细的日志记录
- 性能指标收集
- 问题诊断工具

## 扩展性

### 1. 插件架构
- 编码器插件接口
- 推流协议插件
- 自定义处理插件

### 2. 多平台支持
- 抽象操作系统相关功能
- 条件编译支持
- 平台特定优化