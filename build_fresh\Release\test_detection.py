#!/usr/bin/env python3
"""
Simple Unity3D window detection test script
"""
import subprocess
import time
import sys

def check_unity_process():
    """Check if Unity3D process is running"""
    try:
        result = subprocess.run(['tasklist'], capture_output=True, text=True, shell=True)
        for line in result.stdout.split('\n'):
            if 'test1.exe' in line:
                print(f"[OK] Found Unity3D process: {line.strip()}")
                return True
        print("[FAIL] test1.exe process not found")
        return False
    except Exception as e:
        print(f"[ERROR] Error checking process: {e}")
        return False

def check_mediamtx_process():
    """Check if MediaMTX process is running"""
    try:
        result = subprocess.run(['tasklist'], capture_output=True, text=True, shell=True)
        for line in result.stdout.split('\n'):
            if 'mediamtx.exe' in line:
                print(f"[OK] Found MediaMTX process: {line.strip()}")
                return True
        print("[FAIL] mediamtx.exe process not found")
        return False
    except Exception as e:
        print(f"[ERROR] Error checking process: {e}")
        return False

def check_network_ports():
    """Check if network ports are open"""
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True, shell=True)
        ports_to_check = ['1935', '8554', '8888', '8889']
        found_ports = []
        
        for line in result.stdout.split('\n'):
            for port in ports_to_check:
                if f':{port}' in line and 'LISTENING' in line:
                    found_ports.append(port)
                    print(f"[OK] Port {port} is listening")
        
        if len(found_ports) == len(ports_to_check):
            print("[OK] All MediaMTX ports are listening")
            return True
        else:
            print(f"[FAIL] Some ports not listening, found: {found_ports}")
            return False
    except Exception as e:
        print(f"[ERROR] Error checking ports: {e}")
        return False

def main():
    print("=== Unity3D + MediaMTX Environment Detection ===")
    print()
    
    print("1. Checking Unity3D test program...")
    unity_ok = check_unity_process()
    print()
    
    print("2. Checking MediaMTX server...")
    mediamtx_ok = check_mediamtx_process()
    print()
    
    print("3. Checking network ports...")
    ports_ok = check_network_ports()
    print()
    
    print("=== Detection Results ===")
    if unity_ok and mediamtx_ok and ports_ok:
        print("[SUCCESS] Environment check passed! All components are running normally")
        print("[SUCCESS] Ready for video capture and streaming test")
        return 0
    else:
        print("[FAILURE] Environment check failed! Please check related components")
        return 1

if __name__ == "__main__":
    sys.exit(main())