#include "MultiStreamController.h"
#include "FFmpegPublisher.h"
#include <chrono>

namespace StreamCapture {

MultiStreamController::MultiStreamController()
    : running_(false), maxQueueSize_(100) {
}

MultiStreamController::~MultiStreamController() {
    Shutdown();
}

bool MultiStreamController::Initialize(const std::vector<StreamConfigEx>& configs) {
    if (running_) {
        return false;
    }
    
    configs_ = configs;
    
    // 创建发布器
    for (const auto& config : configs_) {
        if (!config.enabled) {
            continue;
        }
        
        auto publisher = CreatePublisher(config);
        if (publisher) {
            publishers_.push_back(std::move(publisher));
        }
    }
    
    if (publishers_.empty()) {
        return false;
    }
    
    // 启动处理线程
    running_ = true;
    processingThread_ = std::thread(&MultiStreamController::ProcessingThreadFunc, this);
    
    ResetStats();
    
    return true;
}

void MultiStreamController::Shutdown() {
    if (!running_) {
        return;
    }
    
    // 停止处理线程
    running_ = false;
    queueCondition_.notify_all();
    
    if (processingThread_.joinable()) {
        processingThread_.join();
    }
    
    // 清理发布器
    for (auto& publisher : publishers_) {
        if (publisher) {
            publisher->Disconnect();
        }
    }
    publishers_.clear();
    
    // 清理队列
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        while (!packetQueue_.empty()) {
            packetQueue_.pop();
        }
    }
}

bool MultiStreamController::SendPacket(const EncodedPacket& packet) {
    if (!running_) {
        return false;
    }
    
    // 创建数据包副本
    auto packetCopy = std::make_unique<EncodedPacket>();
    packetCopy->size = packet.size;
    packetCopy->data = new uint8_t[packet.size];
    memcpy(packetCopy->data, packet.data, packet.size);
    packetCopy->timestamp = packet.timestamp;
    packetCopy->dts = packet.dts;
    packetCopy->pts = packet.pts;
    packetCopy->isKeyFrame = packet.isKeyFrame;
    
    // 添加到队列
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        
        // 检查队列大小
        if (packetQueue_.size() >= maxQueueSize_) {
            // 丢弃最旧的包
            packetQueue_.pop();
            stats_.droppedPackets++;
        }
        
        packetQueue_.push(std::move(packetCopy));
    }
    
    queueCondition_.notify_one();
    return true;
}

size_t MultiStreamController::GetActiveStreamCount() const {
    size_t count = 0;
    for (const auto& publisher : publishers_) {
        if (publisher && publisher->IsConnected()) {
            count++;
        }
    }
    return count;
}

MultiStreamController::Stats MultiStreamController::GetStats() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    Stats currentStats = stats_;
    currentStats.activeStreams = static_cast<uint32_t>(GetActiveStreamCount());
    return currentStats;
}

void MultiStreamController::ProcessingThreadFunc() {
    while (running_) {
        std::unique_ptr<EncodedPacket> packet;
        
        // 获取数据包
        {
            std::unique_lock<std::mutex> lock(queueMutex_);
            queueCondition_.wait(lock, [this] { return !packetQueue_.empty() || !running_; });
            
            if (!running_) {
                break;
            }
            
            if (!packetQueue_.empty()) {
                packet = std::move(packetQueue_.front());
                packetQueue_.pop();
            }
        }
        
        // 发送到所有发布器
        if (packet) {
            for (auto& publisher : publishers_) {
                if (publisher && publisher->IsConnected()) {
                    publisher->SendPacket(*packet);
                }
            }
            
            UpdateStats(packet->size);
        }
    }
}

void MultiStreamController::UpdateStats(size_t packetSize) {
    std::lock_guard<std::mutex> lock(statsMutex_);
    
    stats_.totalPacketsSent++;
    stats_.totalBytesTransferred += packetSize;
    
    // 计算平均比特率（简化版本）
    static auto startTime = std::chrono::steady_clock::now();
    auto currentTime = std::chrono::steady_clock::now();
    auto elapsedSeconds = std::chrono::duration<double>(currentTime - startTime).count();
    
    if (elapsedSeconds > 0) {
        stats_.avgBitrate = (stats_.totalBytesTransferred * 8.0) / elapsedSeconds / 1000.0; // kbps
    }
}

void MultiStreamController::ResetStats() {
    std::lock_guard<std::mutex> lock(statsMutex_);
    stats_ = Stats{};
}

std::unique_ptr<StreamPublisher> MultiStreamController::CreatePublisher(const StreamConfigEx& config) {
    if (config.type == "rtmp") {
        auto publisher = std::make_unique<FFmpegRTMPPublisher>();
        if (publisher->InitializeEx(config)) {
            if (publisher->Connect()) {
                return std::move(publisher);
            }
        }
    }
    // 可以添加其他类型的发布器
    
    return nullptr;
}

} // namespace StreamCapture
