﻿  FFmpegEncoder.cpp
  StreamCaptureTest.cpp
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(213,27): warning C4244: “return”: 从“int”转换到“uint8_t”，可能丢失数据
  (编译源文件“../src/encoder/FFmpegEncoder.cpp”)
  
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(224,34): warning C4244: “return”: 从“int”转换到“int8_t”，可能丢失数据
  (编译源文件“../src/encoder/FFmpegEncoder.cpp”)
  
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(235,29): warning C4244: “return”: 从“int”转换到“uint16_t”，可能丢失数据
  (编译源文件“../src/encoder/FFmpegEncoder.cpp”)
  
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(246,38): warning C4244: “return”: 从“int”转换到“int16_t”，可能丢失数据
  (编译源文件“../src/encoder/FFmpegEncoder.cpp”)
  
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(213,27): warning C4244: “return”: 从“int”转换到“uint8_t”，可能丢失数据
  (编译源文件“../src/test/StreamCaptureTest.cpp”)
  
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(224,34): warning C4244: “return”: 从“int”转换到“int8_t”，可能丢失数据
  (编译源文件“../src/test/StreamCaptureTest.cpp”)
  
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(235,29): warning C4244: “return”: 从“int”转换到“uint16_t”，可能丢失数据
  (编译源文件“../src/test/StreamCaptureTest.cpp”)
  
C:\dev\vcpkg\installed\x64-windows\include\libavutil\common.h(246,38): warning C4244: “return”: 从“int”转换到“int16_t”，可能丢失数据
  (编译源文件“../src/test/StreamCaptureTest.cpp”)
  
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp(112,72): warning C4100: 'operation': 未引用的参数
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp(839,79): warning C4100: 'operation': 未引用的参数
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp(853,42): warning C4100: 'isKeyFrame': 未引用的参数
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp(931,50): warning C4100: 'config': 未引用的参数
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\test\StreamCaptureTest.cpp(386,46): warning C4100: 'config': 未引用的参数
FFmpegEncoder.obj : error LNK2005: "public: virtual bool __cdecl StreamCapture::FFmpegEncoder::Initialize(struct StreamCapture::VideoEncoder::Config const &)" (?Initialize@FFmpegEncoder@StreamCapture@@UEAA_NAEBUConfig@VideoEncoder@2@@Z) 已经在 StreamCaptureTest.obj 中定义
StreamCaptureTest.obj : error LNK2019: 无法解析的外部符号 "public: __cdecl StreamCapture::FFmpegRTMPPublisher::FFmpegRTMPPublisher(void)" (??0FFmpegRTMPPublisher@StreamCapture@@QEAA@XZ)，函数 "private: bool __cdecl StreamCaptureTest::CreatePublishers(void)" (?CreatePublishers@StreamCaptureTest@@AEAA_NXZ) 中引用了该符号
StreamCaptureTest.obj : error LNK2019: 无法解析的外部符号 "public: bool __cdecl StreamCapture::FFmpegRTMPPublisher::InitializeEx(struct StreamCapture::StreamConfigEx const &)" (?InitializeEx@FFmpegRTMPPublisher@StreamCapture@@QEAA_NAEBUStreamConfigEx@2@@Z)，函数 "private: bool __cdecl StreamCaptureTest::CreatePublishers(void)" (?CreatePublishers@StreamCaptureTest@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_version_info，函数 "public: static class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > __cdecl StreamCapture::FFmpegUtils::GetFFmpegVersion(void)" (?GetFFmpegVersion@FFmpegUtils@StreamCapture@@SA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_strerror，函数 "public: static class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > __cdecl StreamCapture::FFmpegUtils::GetErrorString(int)" (?GetErrorString@FFmpegUtils@StreamCapture@@SA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_rescale_q，函数 "private: class std::unique_ptr<struct StreamCapture::EncodedPacket,struct std::default_delete<struct StreamCapture::EncodedPacket> > __cdecl StreamCapture::FFmpegEncoder::CreatePacketFromAVPacket(struct AVPacket *)" (?CreatePacketFromAVPacket@FFmpegEncoder@StreamCapture@@AEAA?AV?$unique_ptr@UEncodedPacket@StreamCapture@@U?$default_delete@UEncodedPacket@StreamCapture@@@std@@@std@@PEAUAVPacket@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_log_set_level，函数 "public: static bool __cdecl StreamCapture::FFmpegUtils::InitializeFFmpeg(void)" (?InitializeFFmpeg@FFmpegUtils@StreamCapture@@SA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_buffer_ref，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupPixelFormat(void)" (?SetupPixelFormat@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_buffer_unref，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_dict_get，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetHardwareEncoderOptions(void)" (?SetHardwareEncoderOptions@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_dict_set，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetHardwareEncoderOptions(void)" (?SetHardwareEncoderOptions@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_dict_set_int，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetSoftwareEncoderOptions(void)" (?SetSoftwareEncoderOptions@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_dict_free，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetHardwareEncoderOptions(void)" (?SetHardwareEncoderOptions@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_frame_alloc，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::AllocateFrames(void)" (?AllocateFrames@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_frame_free，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::AllocateFrames(void)" (?AllocateFrames@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_frame_get_buffer，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::AllocateFrames(void)" (?AllocateFrames@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_frame_make_writable，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::ConvertFrame(struct StreamCapture::FrameData const &,struct AVFrame *)" (?ConvertFrame@FFmpegEncoder@StreamCapture@@AEAA_NAEBUFrameData@2@PEAUAVFrame@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_hwdevice_find_type_by_name，函数 "public: bool __cdecl StreamCapture::FFmpegEncoder::InitializeHardwareEncoder(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &)" (?InitializeHardwareEncoder@FFmpegEncoder@StreamCapture@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_hwdevice_get_type_name，函数 "public: static class std::vector<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::allocator<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > > > __cdecl StreamCapture::FFmpegUtils::GetSupportedHardwareTypes(void)" (?GetSupportedHardwareTypes@FFmpegUtils@StreamCapture@@SA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@XZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_hwdevice_iterate_types，函数 "public: static class std::vector<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::allocator<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > > > __cdecl StreamCapture::FFmpegUtils::GetSupportedHardwareTypes(void)" (?GetSupportedHardwareTypes@FFmpegUtils@StreamCapture@@SA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@XZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_hwdevice_ctx_create，函数 "public: bool __cdecl StreamCapture::FFmpegEncoder::InitializeHardwareEncoder(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &)" (?InitializeHardwareEncoder@FFmpegEncoder@StreamCapture@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_hwframe_get_buffer，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::AllocateFrames(void)" (?AllocateFrames@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_hwframe_transfer_data，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::ConvertToHardwareFrame(struct AVFrame *,struct AVFrame *)" (?ConvertToHardwareFrame@FFmpegEncoder@StreamCapture@@AEAA_NPEAUAVFrame@@0@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_codec_iterate，函数 "public: static class std::vector<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::allocator<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > > > __cdecl StreamCapture::FFmpegUtils::GetSupportedCodecs(void)" (?GetSupportedCodecs@FFmpegUtils@StreamCapture@@SA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@XZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_find_encoder_by_name，函数 "public: static bool __cdecl StreamCapture::FFmpegUtils::IsCodecSupported(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &)" (?IsCodecSupported@FFmpegUtils@StreamCapture@@SA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_codec_is_encoder，函数 "public: static class std::vector<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::allocator<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > > > __cdecl StreamCapture::FFmpegUtils::GetSupportedCodecs(void)" (?GetSupportedCodecs@FFmpegUtils@StreamCapture@@SA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@XZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_packet_alloc，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::AllocateFrames(void)" (?AllocateFrames@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_packet_free，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_packet_unref，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::EncodeAVFrame(struct AVFrame *)" (?EncodeAVFrame@FFmpegEncoder@StreamCapture@@AEAA_NPEAUAVFrame@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_version，函数 "public: static class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > __cdecl StreamCapture::FFmpegUtils::GetCodecVersion(void)" (?GetCodecVersion@FFmpegUtils@StreamCapture@@SA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_alloc_context3，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetupCodec(void)" (?SetupCodec@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_free_context，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_open2，函数 "public: bool __cdecl StreamCapture::FFmpegEncoder::InitializeEx(struct StreamCapture::EncoderConfigEx const &)" (?InitializeEx@FFmpegEncoder@StreamCapture@@QEAA_NAEBUEncoderConfigEx@2@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_send_frame，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::EncodeAVFrame(struct AVFrame *)" (?EncodeAVFrame@FFmpegEncoder@StreamCapture@@AEAA_NPEAUAVFrame@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avcodec_receive_packet，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::EncodeAVFrame(struct AVFrame *)" (?EncodeAVFrame@FFmpegEncoder@StreamCapture@@AEAA_NPEAUAVFrame@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avformat_network_init，函数 "public: static bool __cdecl StreamCapture::FFmpegUtils::InitializeFFmpeg(void)" (?InitializeFFmpeg@FFmpegUtils@StreamCapture@@SA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 avformat_network_deinit，函数 "public: static void __cdecl StreamCapture::FFmpegUtils::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegUtils@StreamCapture@@SAXXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_opt_set，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::SetHardwareEncoderOptions(void)" (?SetHardwareEncoderOptions@FFmpegEncoder@StreamCapture@@AEAA_NXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 sws_freeContext，函数 "private: void __cdecl StreamCapture::FFmpegEncoder::CleanupFFmpeg(void)" (?CleanupFFmpeg@FFmpegEncoder@StreamCapture@@AEAAXXZ) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 sws_getContext，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::ConvertFrame(struct StreamCapture::FrameData const &,struct AVFrame *)" (?ConvertFrame@FFmpegEncoder@StreamCapture@@AEAA_NAEBUFrameData@2@PEAUAVFrame@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 sws_scale，函数 "private: bool __cdecl StreamCapture::FFmpegEncoder::ConvertFrame(struct StreamCapture::FrameData const &,struct AVFrame *)" (?ConvertFrame@FFmpegEncoder@StreamCapture@@AEAA_NAEBUFrameData@2@PEAUAVFrame@@@Z) 中引用了该符号
FFmpegEncoder.obj : error LNK2019: 无法解析的外部符号 av_gettime，函数 "public: bool __cdecl StreamCapture::FFmpegEncoder::InitializeEx(struct StreamCapture::EncoderConfigEx const &)" (?InitializeEx@FFmpegEncoder@StreamCapture@@QEAA_NAEBUEncoderConfigEx@2@@Z) 中引用了该符号
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\redist_desk\RelWithDebInfo\StreamCaptureTest.exe : fatal error LNK1120: 41 个无法解析的外部命令
