#pragma once

#include "Common.h"
#include "FrameProcessor.h"
#include <atomic>
#include <thread>
#include <queue>

// FFmpeg包含
extern "C"
{
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
}

namespace StreamCapture
{

    // 编码数据包
    struct EncodedPacket
    {
        uint8_t *data = nullptr;
        size_t size = 0;
        uint64_t timestamp = 0;
        uint64_t dts = 0;
        uint64_t pts = 0;
        bool isKeyFrame = false;

        ~EncodedPacket()
        {
            if (data)
            {
                delete[] data;
                data = nullptr;
            }
        }

        // 禁止拷贝，只允许移动
        EncodedPacket(const EncodedPacket &) = delete;
        EncodedPacket &operator=(const EncodedPacket &) = delete;

        EncodedPacket(EncodedPacket &&other) noexcept
            : data(other.data), size(other.size), timestamp(other.timestamp), dts(other.dts), pts(other.pts), isKeyFrame(other.isKeyFrame)
        {
            other.data = nullptr;
            other.size = 0;
        }

        EncodedPacket &operator=(EncodedPacket &&other) noexcept
        {
            if (this != &other)
            {
                if (data)
                    delete[] data;
                data = other.data;
                size = other.size;
                timestamp = other.timestamp;
                dts = other.dts;
                pts = other.pts;
                isKeyFrame = other.isKeyFrame;
                other.data = nullptr;
                other.size = 0;
            }
            return *this;
        }

        EncodedPacket() = default;
    };

    // 编码器基类
    class VideoEncoder
    {
    public:
        struct Config
        {
            uint32_t width = 1920;
            uint32_t height = 1080;
            uint32_t fps = 30;
            uint32_t bitrate = 2000; // kbps
            std::string codec = "h264";
            std::string preset = "quality"; // speed, balanced, quality
            std::string profile = "main";
            uint32_t keyframeInterval = 60;
            bool enableBFrames = true;
            uint32_t bufferSize = 0; // 0 = auto
        };

        VideoEncoder() = default;
        virtual ~VideoEncoder() = default;

        // 生命周期
        virtual bool Initialize(const Config &config) = 0;
        virtual void Shutdown() = 0;

        // 编码
        virtual bool EncodeFrame(const FrameData &frame) = 0;
        virtual std::unique_ptr<EncodedPacket> GetEncodedPacket(uint32_t timeoutMs = 0) = 0;

        // 控制
        virtual bool Flush() = 0;
        virtual bool SetBitrate(uint32_t bitrate) = 0;
        virtual bool ForceKeyFrame() = 0;

        // 状态查询
        virtual bool IsInitialized() const = 0;
        virtual EncoderState GetState() const = 0;
        virtual std::string GetEncoderName() const = 0;

        // 统计
        struct Stats
        {
            uint64_t inputFrames = 0;
            uint64_t outputPackets = 0;
            uint64_t droppedFrames = 0;
            float avgEncodeTime = 0.0f;
            uint64_t totalBytes = 0;
            float avgBitrate = 0.0f;
            uint32_t currentQuality = 0;
        };

        virtual Stats GetStats() const = 0;

        // 能力查询
        static bool IsHardwareSupported(const std::string &type); // nvenc, qsv, amf
        static std::vector<std::string> GetSupportedCodecs();

    protected:
        Config config_;
        Stats stats_;
        std::atomic<EncoderState> state_;
    };

    // NVENC硬件编码器
    class NVENCEncoder : public VideoEncoder
    {
    public:
        NVENCEncoder();
        ~NVENCEncoder() override;

        bool Initialize(const Config &config) override;
        void Shutdown() override;

        bool EncodeFrame(const FrameData &frame) override;
        std::unique_ptr<EncodedPacket> GetEncodedPacket(uint32_t timeoutMs = 0) override;

        bool Flush() override;
        bool SetBitrate(uint32_t bitrate) override;
        bool ForceKeyFrame() override;

        bool IsInitialized() const override { return initialized_; }
        EncoderState GetState() const override { return state_; }
        std::string GetEncoderName() const override { return "NVENC"; }

        Stats GetStats() const override;

        static bool IsSupported();

    private:
        bool SetupNVENC();
        void CleanupNVENC();

        bool initialized_;
        // NVENC相关成员变量会在实际实现中添加
        void *nvencSession_; // 实际类型应该是NV_ENCODE_API_FUNCTION_LIST*
        void *inputBuffer_;
        void *outputBuffer_;

        std::mutex packetMutex_;
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::condition_variable packetCondition_;
    };

    // Intel Quick Sync编码器
    class QSVEncoder : public VideoEncoder
    {
    public:
        QSVEncoder();
        ~QSVEncoder() override;

        bool Initialize(const Config &config) override;
        void Shutdown() override;

        bool EncodeFrame(const FrameData &frame) override;
        std::unique_ptr<EncodedPacket> GetEncodedPacket(uint32_t timeoutMs = 0) override;

        bool Flush() override;
        bool SetBitrate(uint32_t bitrate) override;
        bool ForceKeyFrame() override;

        bool IsInitialized() const override { return initialized_; }
        EncoderState GetState() const override { return state_; }
        std::string GetEncoderName() const override { return "Intel QSV"; }

        Stats GetStats() const override;

        static bool IsSupported();

    private:
        bool SetupQSV();
        void CleanupQSV();

        bool initialized_;
        // QSV相关成员变量
        void *qsvSession_;
        void *allocator_;

        std::mutex packetMutex_;
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::condition_variable packetCondition_;
    };

    // FFmpeg软件编码器
    class FFmpegEncoder : public VideoEncoder
    {
    public:
        FFmpegEncoder();
        ~FFmpegEncoder() override;

        bool Initialize(const Config &config) override;
        void Shutdown() override;

        bool EncodeFrame(const FrameData &frame) override;
        std::unique_ptr<EncodedPacket> GetEncodedPacket(uint32_t timeoutMs = 0) override;

        bool Flush() override;
        bool SetBitrate(uint32_t bitrate) override;
        bool ForceKeyFrame() override;

        bool IsInitialized() const override { return codecContext_ != nullptr; }
        EncoderState GetState() const override { return state_; }
        std::string GetEncoderName() const override { return "FFmpeg Software"; }

        Stats GetStats() const override;

    private:
        bool SetupFFmpeg();
        void CleanupFFmpeg();
        bool ConvertFrameFormat(const FrameData &input, AVFrame *output);

        const AVCodec *codec_;
        AVCodecContext *codecContext_;
        AVFrame *frame_;
        AVPacket *packet_;
        SwsContext *swsContext_;

        std::mutex packetMutex_;
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::condition_variable packetCondition_;

        uint64_t frameIndex_;
        LARGE_INTEGER perfFrequency_;
    };

    // 编码器管理器
    class EncoderManager
    {
    public:
        EncoderManager();
        ~EncoderManager();

        // 生命周期
        bool Initialize();
        void Shutdown();

        // 编码器创建
        std::unique_ptr<VideoEncoder> CreateEncoder(const EncoderConfig &config);
        std::unique_ptr<VideoEncoder> CreateBestEncoder(const EncoderConfig &config);

        // 能力查询
        struct EncoderCapability
        {
            std::string name;
            std::string type; // hardware, software
            bool available;
            std::vector<std::string> supportedCodecs;
            uint32_t maxWidth;
            uint32_t maxHeight;
            uint32_t maxBitrate;
        };

        std::vector<EncoderCapability> GetAvailableEncoders();
        bool IsHardwareEncodingAvailable();

        // 回调
        using EncoderErrorCallback = std::function<void(const std::string &encoderName, const ErrorInfo &)>;
        void SetErrorCallback(EncoderErrorCallback callback) { errorCallback_ = callback; }

    private:
        void DetectHardwareCapabilities();
        void HandleError(const std::string &encoderName, ErrorCode code, const std::string &message);

        struct HardwareCapability
        {
            bool nvencAvailable = false;
            bool qsvAvailable = false;
            bool amfAvailable = false;
        } hwCapability_;

        EncoderErrorCallback errorCallback_;
        std::mutex mutex_;
    };

    // 多路编码管理器
    class MultiEncoder
    {
    public:
        struct StreamConfig
        {
            std::string streamId;
            EncoderConfig encoderConfig;
            bool enabled = true;
        };

        MultiEncoder();
        ~MultiEncoder();

        // 生命周期
        bool Initialize(const std::vector<StreamConfig> &configs);
        void Shutdown();

        // 编码控制
        bool EncodeFrame(const FrameData &frame); // 编码到所有流
        bool EncodeFrameToStream(const std::string &streamId, const FrameData &frame);

        // 数据包获取
        std::unique_ptr<EncodedPacket> GetEncodedPacket(const std::string &streamId, uint32_t timeoutMs = 0);
        std::vector<std::pair<std::string, std::unique_ptr<EncodedPacket>>> GetAllEncodedPackets(uint32_t timeoutMs = 0);

        // 流管理
        bool AddStream(const StreamConfig &config);
        bool RemoveStream(const std::string &streamId);
        bool EnableStream(const std::string &streamId, bool enable);

        // 状态查询
        std::vector<std::string> GetStreamIds() const;
        bool IsStreamEnabled(const std::string &streamId) const;
        VideoEncoder::Stats GetStreamStats(const std::string &streamId) const;

        // 控制
        bool SetStreamBitrate(const std::string &streamId, uint32_t bitrate);
        bool ForceKeyFrameOnAllStreams();
        bool FlushAllStreams();

        // 回调
        using PacketCallback = std::function<void(const std::string &streamId, std::unique_ptr<EncodedPacket>)>;
        void SetPacketCallback(PacketCallback callback) { packetCallback_ = callback; }

    private:
        struct StreamInfo
        {
            std::string id;
            StreamConfig config;
            std::unique_ptr<VideoEncoder> encoder;
            bool enabled;
            std::thread processingThread;
            std::atomic<bool> running;
        };

        void ProcessingThread(StreamInfo *streamInfo);
        StreamInfo *FindStream(const std::string &streamId);
        const StreamInfo *FindStream(const std::string &streamId) const;

        std::vector<std::unique_ptr<StreamInfo>> streams_;
        std::unique_ptr<EncoderManager> manager_;

        PacketCallback packetCallback_;

        mutable std::mutex mutex_;
        std::atomic<bool> running_;
    };

} // namespace StreamCapture