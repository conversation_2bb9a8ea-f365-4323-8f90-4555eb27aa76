F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\main.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\StreamCaptureController.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\StreamCaptureController.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\HookCore.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\HookCore.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\ConfigManager.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\ConfigManager.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\MediaServerManager.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\DebugUtils.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\DebugUtils.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\utils\Win7Compatibility.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\Win7Compatibility.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\capture\FrameProcessor.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\FrameProcessor.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\VideoEncoder.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\VideoEncoder.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\FFmpegEncoder.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\StreamPublisher.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\StreamPublisher.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\FFmpegPublisher.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\FFmpegPublisher.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\MultiStreamController.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\MultiStreamController.obj
