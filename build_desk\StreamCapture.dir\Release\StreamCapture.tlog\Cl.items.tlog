F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\main.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\main.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\StreamCaptureController.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\StreamCaptureController.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\HookCore.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\HookCore.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\utils\Win7Compatibility.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\Win7Compatibility.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\capture\FrameProcessor.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\FrameProcessor.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\VideoEncoder.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\VideoEncoder.obj
F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\StreamPublisher.cpp;F:\dev\Unity3D_project\SM-Record-main\StreamCapture\build_desk\StreamCapture.dir\Release\StreamPublisher.obj
