{"Version": 1, "WorkspaceRootPath": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\build_desk\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\HookCore.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\MediaServerManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files (x86)\\Windows Kits\\10\\Source\\10.0.26100.0\\ucrt\\startup\\abort.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\main.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\StreamCaptureController.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A9AD216C-087E-35C1-A054-AC519EBC9F8B}|StreamCapture.vcxproj|F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\include\\MediaServerManager.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 567, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "HookCore.cpp", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\HookCore.cpp", "RelativeDocumentMoniker": "..\\src\\core\\HookCore.cpp", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\HookCore.cpp", "RelativeToolTip": "..\\src\\core\\HookCore.cpp", "ViewState": "AgIAALwAAAAAAAAAAAAnwMcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-03T16:07:29.888Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "abort.cpp", "DocumentMoniker": "C:\\Program Files (x86)\\Windows Kits\\10\\Source\\10.0.26100.0\\ucrt\\startup\\abort.cpp", "ToolTip": "C:\\Program Files (x86)\\Windows Kits\\10\\Source\\10.0.26100.0\\ucrt\\startup\\abort.cpp", "ViewState": "AgIAAD4AAAAAAAAAAIAywEwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-03T15:33:24.434Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "main.cpp", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\main.cpp", "RelativeDocumentMoniker": "..\\src\\main.cpp", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\main.cpp", "RelativeToolTip": "..\\src\\main.cpp", "ViewState": "AgIAAP4BAAAAAAAAAIAywP0BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-03T15:10:20.871Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "MediaServerManager.h", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\include\\MediaServerManager.h", "RelativeDocumentMoniker": "..\\include\\MediaServerManager.h", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\include\\MediaServerManager.h", "RelativeToolTip": "..\\include\\MediaServerManager.h", "ViewState": "AgIAAEcAAAAAAAAAAAAAAFYAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-03T15:10:04.454Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MediaServerManager.cpp", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\MediaServerManager.cpp", "RelativeDocumentMoniker": "..\\src\\core\\MediaServerManager.cpp", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\MediaServerManager.cpp", "RelativeToolTip": "..\\src\\core\\MediaServerManager.cpp", "ViewState": "AgIAAHIBAAAAAAAAAAAAAIoBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-03T15:07:56.842Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "StreamCaptureController.cpp", "DocumentMoniker": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\StreamCaptureController.cpp", "RelativeDocumentMoniker": "..\\src\\core\\StreamCaptureController.cpp", "ToolTip": "F:\\dev\\Unity3D_project\\SM-Record-main\\StreamCapture\\src\\core\\StreamCaptureController.cpp", "RelativeToolTip": "..\\src\\core\\StreamCaptureController.cpp", "ViewState": "AgIAAOYBAAAAAAAAAIAywPIBAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-03T15:07:43.614Z", "EditorCaption": ""}]}]}]}