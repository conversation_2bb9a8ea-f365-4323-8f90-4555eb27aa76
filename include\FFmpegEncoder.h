#pragma once

#include "VideoEncoder.h"
#include "ConfigManager.h"
#include <memory>
#include <thread>
#include <atomic>
#include <queue>
#include <condition_variable>

// FFmpeg 前向声明 (避免直接依赖 FFmpeg 头文件)
struct AVCodecContext;
struct AVCodec;
struct AVFrame;
struct AVPacket;
struct SwsContext;
struct AVBufferRef;
struct AVRational;
enum AVHWDeviceType;
enum AVPixelFormat;

namespace StreamCapture
{

    // FFmpeg 编码器实现
    class FFmpegEncoder : public VideoEncoder
    {
    public:
        FFmpegEncoder();
        ~FFmpegEncoder() override;

        // VideoEncoder 接口实现
        bool Initialize(const Config &config) override;
        void Shutdown() override;
        bool EncodeFrame(const FrameData &frame) override;
        std::unique_ptr<EncodedPacket> GetEncodedPacket(uint32_t timeoutMs) override;
        bool SetBitrate(uint32_t bitrate) override;
        bool ForceKeyFrame() override;
        bool Flush() override;
        Stats GetStats() const override;

        // 扩展配置支持
        bool InitializeEx(const EncoderConfigEx &config);

        // 硬件加速支持
        bool InitializeHardwareEncoder(const std::string &hwType);
        bool IsHardwareAccelerated() const { return hwDeviceCtx_ != nullptr; }

        // 性能优化
        bool EnableZeroCopy(bool enable);
        bool SetThreadCount(int threads);
        bool SetLowLatencyMode(bool enable);

    private:
        // FFmpeg 上下文
        AVCodecContext *codecCtx_;
        const AVCodec *codec_;
        AVFrame *frame_;
        AVFrame *hwFrame_;
        AVPacket *packet_;
        SwsContext *swsCtx_;

        // 硬件加速
        AVBufferRef *hwDeviceCtx_;
        AVHWDeviceType hwType_;
        bool useHardware_;

        // 配置
        EncoderConfigEx config_;
        bool initialized_;

        // 统计
        mutable std::mutex statsMutex_;
        Stats stats_;

        // 编码队列和线程
        std::thread encodingThread_;
        std::atomic<bool> running_;
        std::queue<std::unique_ptr<FrameData>> inputQueue_;
        std::queue<std::unique_ptr<EncodedPacket>> outputQueue_;
        std::mutex inputMutex_;
        std::mutex outputMutex_;
        std::condition_variable inputCondition_;
        std::condition_variable outputCondition_;
        size_t maxQueueSize_;

        // 时间戳管理
        int64_t frameCount_;
        int64_t startTime_;
        AVRational timeBase_;

        // 初始化辅助函数
        bool SetupCodec();
        bool SetupHardwareAcceleration();
        bool SetupPixelFormat();
        bool SetupEncodingParameters();
        bool AllocateFrames();

        // 编码辅助函数
        bool ConvertFrame(const FrameData &input, AVFrame *output);
        bool ConvertToHardwareFrame(AVFrame *swFrame, AVFrame *hwFrame);
        bool EncodeAVFrame(AVFrame *frame);
        std::unique_ptr<EncodedPacket> CreatePacketFromAVPacket(AVPacket *avPacket);

        // 线程函数
        void EncodingThreadFunc();

        // 清理函数
        void CleanupFFmpeg();
        void CleanupQueues();

        // 硬件类型转换
        AVHWDeviceType StringToHWDeviceType(const std::string &hwType);
        std::string HWDeviceTypeToString(AVHWDeviceType type);

        // 像素格式转换
        AVPixelFormat DXGIFormatToAVPixelFormat(DXGI_FORMAT format);
        AVPixelFormat GetHardwarePixelFormat(AVHWDeviceType hwType);

        // 编码参数设置
        bool SetEncoderOptions();
        bool SetHardwareEncoderOptions();
        bool SetSoftwareEncoderOptions();

        // 质量控制
        bool SetQualitySettings();
        bool SetRateControlSettings();

        // 错误处理
        void HandleEncodingError(int errorCode, const std::string &operation);
        std::string GetFFmpegErrorString(int errorCode);

        // 性能监控
        void UpdateStats(bool isKeyFrame, size_t packetSize);
        void ResetStats();
    };

    // FFmpeg 多编码器管理器
    class FFmpegMultiEncoder : public MultiEncoder
    {
    public:
        FFmpegMultiEncoder();
        ~FFmpegMultiEncoder() override;

        // MultiEncoder 接口实现
        bool Initialize(const std::vector<StreamConfig> &configs) override;
        void Shutdown() override;
        bool EncodeFrame(const FrameData &frame) override;
        bool EncodeFrameToStream(const std::string &streamId, const FrameData &frame) override;
        std::unique_ptr<EncodedPacket> GetEncodedPacket(const std::string &streamId, uint32_t timeoutMs) override;
        std::vector<std::pair<std::string, std::unique_ptr<EncodedPacket>>> GetAllEncodedPackets(uint32_t timeoutMs) override;

        bool AddStream(const StreamConfig &config) override;
        bool RemoveStream(const std::string &streamId) override;
        bool EnableStream(const std::string &streamId, bool enable) override;
        std::vector<std::string> GetStreamIds() const override;
        bool IsStreamEnabled(const std::string &streamId) const override;
        VideoEncoder::Stats GetStreamStats(const std::string &streamId) const override;
        bool SetStreamBitrate(const std::string &streamId, uint32_t bitrate) override;
        bool ForceKeyFrameOnAllStreams() override;
        bool FlushAllStreams() override;

        // 扩展功能
        bool InitializeEx(const std::vector<EncoderConfigEx> &configs);
        bool AddStreamEx(const EncoderConfigEx &config);

        // 硬件资源管理
        bool InitializeHardwareContext();
        void CleanupHardwareContext();
        bool IsHardwareAvailable(const std::string &hwType) const;
        std::vector<std::string> GetAvailableHardwareTypes() const;

        // 性能优化
        bool EnableGPUScheduling(bool enable);
        bool SetGlobalThreadPool(int threads);

    private:
        struct EncoderInfo
        {
            std::string id;
            std::unique_ptr<FFmpegEncoder> encoder;
            EncoderConfigEx config;
            bool enabled;
            std::thread processingThread;
            std::atomic<bool> running;
        };

        std::vector<std::unique_ptr<EncoderInfo>> encoders_;
        mutable std::mutex encodersMutex_;

        // 硬件上下文共享
        AVBufferRef *sharedHwDeviceCtx_;
        bool hardwareInitialized_;

        // 线程池
        std::vector<std::thread> threadPool_;
        std::atomic<bool> globalRunning_;

        // 帧分发
        bool DistributeFrame(const FrameData &frame);
        void ProcessingThreadFunc(EncoderInfo *info);

        // 查找辅助函数
        EncoderInfo *FindEncoder(const std::string &streamId);
        const EncoderInfo *FindEncoder(const std::string &streamId) const;

        // 硬件检测
        bool DetectHardwareCapabilities();
        bool TestHardwareEncoder(const std::string &hwType);

        // 资源管理
        void CleanupEncoder(EncoderInfo *info);
        void CleanupAllEncoders();
    };

    // FFmpeg 工具函数
    class FFmpegUtils
    {
    public:
        // 初始化和清理
        static bool InitializeFFmpeg();
        static void CleanupFFmpeg();

        // 硬件检测
        static std::vector<std::string> GetSupportedHardwareTypes();
        static bool IsHardwareTypeSupported(const std::string &hwType);
        static std::vector<std::string> GetSupportedCodecs();
        static bool IsCodecSupported(const std::string &codec);

        // 格式转换
        static AVPixelFormat DXGIToAVPixelFormat(DXGI_FORMAT format);
        static DXGI_FORMAT AVPixelFormatToDXGI(AVPixelFormat format);

        // 错误处理
        static std::string GetErrorString(int errorCode);
        static void LogFFmpegError(int errorCode, const std::string &operation);

        // 版本信息
        static std::string GetFFmpegVersion();
        static std::string GetCodecVersion();

    private:
        static bool initialized_;
        static std::mutex initMutex_;
    };

} // namespace StreamCapture
