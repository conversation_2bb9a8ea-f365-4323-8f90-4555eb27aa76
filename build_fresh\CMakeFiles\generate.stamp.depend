# CMake generation dependency list for this directory.
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDependentOption.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeFindDependencyMacro.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeRCInformation.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/Compiler/MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/Compiler/MSVC.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/FindPackageHandleStandardArgs.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/FindPackageMessage.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/Platform/Windows-MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/Platform/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/Platform/Windows.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/Platform/WindowsPaths.cmake
C:/Program Files/CMake/share/cmake-3.26/Modules/SelectLibraryConfigurations.cmake
C:/dev/vcpkg/installed/x64-windows/share/ffmpeg/FindFFMPEG.cmake
C:/dev/vcpkg/installed/x64-windows/share/ffmpeg/vcpkg-cmake-wrapper.cmake
C:/dev/vcpkg/installed/x64-windows/share/minhook/minhook-config-version.cmake
C:/dev/vcpkg/installed/x64-windows/share/minhook/minhook-config.cmake
C:/dev/vcpkg/installed/x64-windows/share/minhook/minhook-targets-debug.cmake
C:/dev/vcpkg/installed/x64-windows/share/minhook/minhook-targets-release.cmake
C:/dev/vcpkg/installed/x64-windows/share/minhook/minhook-targets.cmake
C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake
F:/dev/Unity3D_project/SM-Record-main/StreamCapture/CMakeLists.txt
F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_fresh/CMakeFiles/3.26.4/CMakeCXXCompiler.cmake
F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_fresh/CMakeFiles/3.26.4/CMakeRCCompiler.cmake
F:/dev/Unity3D_project/SM-Record-main/StreamCapture/build_fresh/CMakeFiles/3.26.4/CMakeSystem.cmake
F:/dev/Unity3D_project/SM-Record-main/StreamCapture/config/capture.json
