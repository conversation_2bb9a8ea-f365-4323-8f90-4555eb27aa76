﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\test\StreamCaptureTest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\ConfigManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\core\MediaServerManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\encoder\FFmpegEncoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\src\stream\FFmpegPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\dev\Unity3D_project\SM-Record-main\StreamCapture\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{F9422F7E-884D-3F98-88DF-33FEBF27D269}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
