# StreamCapture

一个轻量级、Windows 7兼容的多路视频采集和推流系统，专门设计用于捕获Direct3D 11应用程序并进行实时视频编码推流。

## 核心特性

### 🎯 主要功能
- **Direct3D 11 Hook**: 安全稳定的游戏/应用程序帧捕获
- **多路编码**: 同时编码多个不同质量的视频流
- **硬件加速**: 优先使用NVENC/QSV/AMF硬件编码，软件编码作为后备
- **实时推流**: 支持RTMP/SRT协议和文件输出
- **Windows 7兼容**: 专门优化的兼容性层，支持Windows 7 SP1及以上版本

### 🔧 技术栈
- **Hook技术**: MinHook + Direct3D 11函数拦截
- **视频编码**: FFmpeg + 硬件编码器(NVENC/QSV)
- **推流协议**: RTMP、SRT、文件输出
- **开发语言**: C++17
- **构建系统**: CMake + vcpkg

## 快速开始

### 系统要求
- Windows 7 SP1 或更高版本
- Direct3D 11 支持
- 推荐 4GB+ 内存
- 可选：支持硬件编码的显卡

### 编译依赖
通过vcpkg安装依赖：
```bash
vcpkg install ffmpeg[core,avcodec,avformat,swscale]:x64-windows-static
vcpkg install minhook:x64-windows-static
```

### 构建步骤
```bash
# 克隆仓库
git clone <repository-url>
cd StreamCapture

# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake .. -DCMAKE_TOOLCHAIN_FILE=[vcpkg路径]/scripts/buildsystems/vcpkg.cmake

# 编译
cmake --build . --config Release
```

### 使用方法

#### 基础使用
```bash
# 使用默认配置运行
StreamCapture.exe

# 使用自定义配置
StreamCapture.exe -c custom_config.json

# 列出可捕获的目标
StreamCapture.exe --list-targets

# 测试配置文件
StreamCapture.exe --test-config
```

#### 配置文件示例
```json
{
  "global": {
    "logLevel": "INFO",
    "compatibility": "win7"
  },
  "targets": [
    {
      "name": "Unity Game",
      "enabled": true,
      "processName": "test1.exe",
      "capture": {
        "fps": 30,
        "quality": "high"
      },
      "encoder": {
        "type": "hardware",
        "codec": "h264",
        "bitrate": 2500
      },
      "outputs": [
        {
          "type": "file",
          "path": "output/game_{timestamp}.mp4",
          "enabled": true
        },
        {
          "type": "rtmp",
          "url": "rtmp://localhost/live/stream1",
          "enabled": false
        }
      ]
    }
  ]
}
```

## 系统架构

### 模块化设计
```
┌─────────────────────────────────────────────────────────────┐
│                StreamCapture 主控制器                       │
├─────────────────────────────────────────────────────────────┤
│  配置管理  │  进程管理  │  任务调度  │  状态监控  │
└─────────────────────────────────────────────────────────────┘
                              │
              ┌───────────────┼───────────────┐
              │               │               │
    ┌─────────▼─────────┐ ┌───▼────┐ ┌───────▼─────────┐
    │  Hook注入模块     │ │ 帧采集 │ │  编码推流模块   │
    │ • MinHook核心     │ │ 缓冲区 │ │ • 硬件编码器    │
    │ • D3D11 Hook      │ │        │ │ • 软件编码器    │
    │ • Present拦截     │ └────────┘ │ • RTMP推流      │
    │ • 错误恢复        │             │ • 文件录制      │
    └───────────────────┘             └─────────────────┘
```

### 数据流
```
目标进程 → D3D11 Present → 帧捕获 → 格式转换 → 编码队列
                                                    │
推流输出 ← RTMP推流 ← 编码完成 ← 硬件/软件编码 ←─────┘
```

## Windows 7兼容性

### 特殊优化
- **内存管理**: 使用保守的内存分配策略，避免大块内存分配
- **API兼容**: 动态加载较新的API，提供降级实现
- **异常处理**: 使用传统的SEH异常处理机制
- **线程管理**: 简化的线程同步，避免Windows 7的已知问题

### 测试环境
- Windows 7 Professional SP1 (虚拟机测试)
- Windows 10/11 (主要开发环境)
- VMware Workstation (兼容性测试)

## 编码器支持

### 硬件编码器
| 编码器 | 支持的编解码器 | 最大分辨率 | 最大码率 |
|--------|----------------|------------|----------|
| NVENC  | H.264, H.265   | 4096x4096  | 50 Mbps  |
| QSV    | H.264, H.265   | 4096x4096  | 30 Mbps  |
| AMF    | H.264, H.265   | 4096x4096  | 25 Mbps  |

### 软件编码器
- **FFmpeg libx264**: H.264编码，所有系统支持
- **FFmpeg libx265**: H.265编码，更高压缩率

## 推流协议

### 支持的协议
- **RTMP**: 最广泛支持的推流协议
- **SRT**: 低延迟，适合专业应用
- **文件输出**: MP4/FLV格式本地录制

### 输出配置
```json
"outputs": [
  {
    "type": "rtmp",
    "url": "rtmp://streaming-server.com/live/stream-key",
    "enabled": true
  },
  {
    "type": "file", 
    "path": "recordings/capture_{timestamp}.mp4",
    "enabled": true
  }
]
```

## 性能监控

### 实时统计
- 捕获帧率和丢帧统计
- 编码性能和质量监控
- 系统资源使用情况
- 网络推流状态

### 性能优化
- 零拷贝设计（尽可能）
- 多线程并行处理
- 智能缓冲区管理
- 自适应质量调整

## 故障排除

### 常见问题

#### 1. Hook失败
**症状**: 无法捕获目标程序画面
**解决方案**:
- 确保目标程序使用Direct3D 11
- 以管理员权限运行
- 检查防病毒软件是否阻止

#### 2. Windows 7崩溃
**症状**: 目标程序在注入后崩溃
**解决方案**:
- 启用兼容模式 `"compatibility": "win7"`
- 降低捕获帧率
- 使用软件编码器

#### 3. 编码失败
**症状**: 无法生成视频文件
**解决方案**:
- 检查FFmpeg依赖是否完整
- 尝试切换到软件编码
- 验证输出路径权限

### 日志分析
启用详细日志进行问题诊断：
```bash
StreamCapture.exe --log-level debug --log-file debug.log
```

## 开发指南

### 项目结构
```
StreamCapture/
├── src/
│   ├── core/           # 核心控制逻辑
│   ├── capture/        # 帧捕获模块
│   ├── encoder/        # 视频编码器
│   ├── stream/         # 推流模块
│   └── utils/          # 工具函数
├── include/            # 头文件
├── config/             # 配置文件
├── docs/               # 文档
└── build/              # 构建目录
```

### 扩展开发
1. **新编码器**: 继承`VideoEncoder`基类
2. **新推流协议**: 继承`StreamPublisher`基类
3. **新捕获方式**: 扩展`HookCore`模块

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交问题报告和功能请求到 [Issues](../../issues)。

## 更新日志

### v1.0.0 (2025-08-02)
- 初始版本发布
- Direct3D 11帧捕获
- 硬件/软件编码支持
- RTMP/文件输出
- Windows 7兼容性优化

## 技术支持

- 文档: [docs/](docs/)
- 问题报告: [GitHub Issues](../../issues)
- 讨论: [GitHub Discussions](../../discussions)

---

**注意**: 本软件仅用于合法的视频捕获和流媒体用途。请遵守相关法律法规和软件许可协议。