#include "FFmpegPublisher.h"
#include "StreamCaptureController.h"
#include <chrono>
#include <iomanip>
#include <sstream>

// 移除FFmpegUtils相关调用，因为这些函数不存在

namespace StreamCapture
{
    // FFmpegRTMPPublisher implementation
    FFmpegRTMPPublisher::FFmpegRTMPPublisher()
        : formatCtx_(nullptr), videoStream_(nullptr), codecCtx_(nullptr),
          initialized_(false), connected_(false), running_(false),
          maxQueueSize_(100), startTime_(0), lastDts_(0), lastPts_(0),
          reconnectAttempts_(0)
    {
        // FFmpegUtils::InitializeFFmpeg();
    }

    FFmpegRTMPPublisher::~FFmpegRTMPPublisher()
    {
        Shutdown();
    }

    bool FFmpegRTMPPublisher::InitializeEx(const StreamConfigEx &config)
    {
        config_ = config;
        maxQueueSize_ = 100; // config_.bufferSize; // 修复：使用固定值

        printf("Initializing RTMP publisher for stream: %s, URL: %s\n",
               config_.id.c_str(), config_.url.c_str());

        // 初始化FFmpeg
        if (!SetupOutputFormat())
        {
            printf("Failed to setup output format for RTMP stream: %s\n", config_.id.c_str());
            return false;
        }

        printf("RTMP publisher initialized successfully for stream: %s\n", config_.id.c_str());
        initialized_ = true;
        return true;
    }

    bool FFmpegRTMPPublisher::Initialize(const Config &config)
    {
        StreamConfigEx extConfig;
        extConfig.url = config.url;
        extConfig.type = config.protocol; // 修复：使用type而不是protocol
        // 修复：移除不存在的字段
        return InitializeEx(extConfig);
    }

    void FFmpegRTMPPublisher::Shutdown()
    {
        Disconnect();

        // 停止发送线程
        running_ = false;
        queueCondition_.notify_all();

        if (sendingThread_.joinable())
        {
            sendingThread_.join();
        }

        // 清理FFmpeg资源
        CleanupFFmpeg();

        initialized_ = false;
    }

    bool FFmpegRTMPPublisher::Connect()
    {
        if (!initialized_)
        {
            return false;
        }

        // 设置RTMP选项
        if (!SetupRTMPOptions())
        {
            return false;
        }

        // 打开输出
        if (!OpenOutput())
        {
            return false;
        }

        // 写入头部
        int ret = avformat_write_header(formatCtx_, nullptr);
        if (ret < 0)
        {
            // FFmpegUtils::LogFFmpegError(ret, "avformat_write_header");
            return false;
        }

        connected_ = true;
        running_ = true;

        // 启动发送线程
        sendingThread_ = std::thread(&FFmpegRTMPPublisher::SendingThreadFunc, this);

        return true;
    }

    void FFmpegRTMPPublisher::Disconnect()
    {
        connected_ = false;

        // 通知发送线程
        queueCondition_.notify_all();

        // 等待发送线程结束
        if (sendingThread_.joinable())
        {
            sendingThread_.join();
        }

        // 写入尾部并关闭
        if (formatCtx_ && formatCtx_->pb)
        {
            av_write_trailer(formatCtx_);
            avio_closep(&formatCtx_->pb);
        }
    }

    bool FFmpegRTMPPublisher::IsConnected() const
    {
        return connected_;
    }

    bool FFmpegRTMPPublisher::SendPacket(const EncodedPacket &packet)
    {
        if (!connected_ || !running_)
        {
            return false;
        }

        // 创建数据包副本
        auto packetCopy = std::make_unique<EncodedPacket>();
        packetCopy->size = packet.size;
        packetCopy->pts = packet.pts;
        packetCopy->dts = packet.dts;
        packetCopy->timestamp = packet.timestamp;
        packetCopy->isKeyFrame = packet.isKeyFrame;

        if (packet.data && packet.size > 0)
        {
            packetCopy->data = new uint8_t[packet.size];
            memcpy(packetCopy->data, packet.data, packet.size);
        }

        // 添加到队列
        {
            std::lock_guard<std::mutex> lock(queueMutex_);

            if (packetQueue_.size() >= maxQueueSize_)
            {
                // 丢弃最旧的数据包
                packetQueue_.pop();
                {
                    std::lock_guard<std::mutex> statsLock(statsMutex_);
                    stats_.droppedPackets++;
                }
            }

            packetQueue_.push(std::move(packetCopy));
            {
                std::lock_guard<std::mutex> statsLock(statsMutex_);
                stats_.totalPackets++;
                stats_.totalBytes += packet.size;
            }
        }

        // 通知发送线程
        queueCondition_.notify_one();
        return true;
    }

    bool FFmpegRTMPPublisher::SendMetadata(const std::string &metadata)
    {
        // 避免未引用参数警告
        (void)metadata;

        // 简单实现，实际应该发送元数据
        return connected_;
    }

    StreamPublisher::Stats FFmpegRTMPPublisher::GetStats() const
    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        return stats_;
    }

    bool FFmpegRTMPPublisher::SetupOutputFormat()
    {
        // 分配输出格式上下文
        const AVOutputFormat *outputFormat = av_guess_format("flv", nullptr, nullptr);
        if (!outputFormat)
        {
            printf("Failed to find FLV output format\n");
            return false;
        }

        int ret = avformat_alloc_output_context2(&formatCtx_, outputFormat, nullptr, config_.url.c_str());
        if (ret < 0)
        {
            printf("Failed to allocate output context: error code %d\n", ret);
            return false;
        }

        printf("Output format setup successful for URL: %s\n", config_.url.c_str());
        return true;
    }

    bool FFmpegRTMPPublisher::SetupRTMPOptions()
    {
        if (!formatCtx_)
        {
            return false;
        }

        // 设置RTMP选项
        av_opt_set(formatCtx_, "rtmp_buffer", "1000", 0);
        av_opt_set(formatCtx_, "rtmp_live", "live", 0);
        av_opt_set(formatCtx_, "flush_packets", "1", 0);

        return true;
    }

    bool FFmpegRTMPPublisher::OpenOutput()
    {
        if (!formatCtx_)
        {
            return false;
        }

        // 打开输出URL
        int ret = avio_open(&formatCtx_->pb, config_.url.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0)
        {
            // FFmpegUtils::LogFFmpegError(ret, "avio_open");
            return false;
        }

        return true;
    }

    void FFmpegRTMPPublisher::SendingThreadFunc()
    {
        while (running_)
        {
            std::unique_ptr<EncodedPacket> packet;

            // 获取数据包
            {
                std::unique_lock<std::mutex> lock(queueMutex_);
                queueCondition_.wait(lock, [this]
                                     { return !packetQueue_.empty() || !running_ || !connected_; });

                if (!running_ || !connected_)
                {
                    break;
                }

                if (!packetQueue_.empty())
                {
                    packet = std::move(packetQueue_.front());
                    packetQueue_.pop();
                }
            }

            // 发送数据包
            if (packet)
            {
                if (!SendAVPacket(*packet))
                {
                    // 发送失败，可能需要重连
                    {
                        std::lock_guard<std::mutex> lock(statsMutex_);
                        stats_.droppedPackets++;
                    }
                }
            }
        }
    }

    bool FFmpegRTMPPublisher::SendAVPacket(const EncodedPacket &packet)
    {
        AVPacket *avPacket = CreateAVPacket(packet);
        if (!avPacket)
        {
            return false;
        }

        bool success = WritePacketToStream(avPacket);
        av_packet_free(&avPacket);
        return success;
    }

    AVPacket *FFmpegRTMPPublisher::CreateAVPacket(const EncodedPacket &packet)
    {
        AVPacket *avPacket = av_packet_alloc();
        if (!avPacket)
        {
            return nullptr;
        }

        avPacket->data = packet.data;
        avPacket->size = static_cast<int>(packet.size);
        avPacket->pts = packet.pts;
        avPacket->dts = packet.dts;

        if (packet.isKeyFrame)
        {
            avPacket->flags |= AV_PKT_FLAG_KEY;
        }

        return avPacket;
    }

    bool FFmpegRTMPPublisher::WritePacketToStream(AVPacket *avPacket)
    {
        if (!formatCtx_ || !avPacket)
        {
            return false;
        }

        // 写入数据包
        int ret = av_interleaved_write_frame(formatCtx_, avPacket);
        if (ret < 0)
        {
            // FFmpegUtils::LogFFmpegError(ret, "av_interleaved_write_frame");
            return false;
        }

        return true;
    }

    void FFmpegRTMPPublisher::CleanupFFmpeg()
    {
        if (formatCtx_)
        {
            avformat_free_context(formatCtx_);
            formatCtx_ = nullptr;
        }

        videoStream_ = nullptr;
        codecCtx_ = nullptr;
    }

    int64_t FFmpegRTMPPublisher::ConvertTimestamp(uint64_t timestamp)
    {
        // 转换时间戳到合适的格式
        return static_cast<int64_t>(timestamp);
    }

    // FFmpegSRTPublisher implementation
    FFmpegSRTPublisher::FFmpegSRTPublisher()
        : srtSocket_(nullptr), initialized_(false), connected_(false), running_(false)
    {
        // FFmpegUtils::InitializeFFmpeg();
    }

    FFmpegSRTPublisher::~FFmpegSRTPublisher()
    {
        Shutdown();
    }

    bool FFmpegSRTPublisher::InitializeEx(const StreamConfigEx &config)
    {
        config_ = config;
        // maxQueueSize_ = config_.bufferSize; // 修复：移除不存在的字段
        initialized_ = true;
        return true;
    }

    bool FFmpegSRTPublisher::Initialize(const Config &config)
    {
        StreamConfigEx extConfig;
        extConfig.url = config.url;
        extConfig.type = config.protocol; // 修复：使用type而不是protocol
        // 修复：移除不存在的字段
        return InitializeEx(extConfig);
    }

    void FFmpegSRTPublisher::Shutdown()
    {
        Disconnect();

        // 停止发送线程
        running_ = false;
        queueCondition_.notify_all();

        if (sendingThread_.joinable())
        {
            sendingThread_.join();
        }

        // 清理SRT资源
        CleanupSRT();

        initialized_ = false;
    }

    bool FFmpegSRTPublisher::Connect()
    {
        if (!initialized_)
        {
            return false;
        }

        // 设置SRT选项
        if (!SetupSRTSocket())
        {
            return false;
        }

        connected_ = true;
        running_ = true;

        // 启动发送线程
        sendingThread_ = std::thread(&FFmpegSRTPublisher::SendingThreadFunc, this);

        return true;
    }

    void FFmpegSRTPublisher::Disconnect()
    {
        connected_ = false;

        // 通知发送线程
        queueCondition_.notify_all();

        // 等待发送线程结束
        if (sendingThread_.joinable())
        {
            sendingThread_.join();
        }

        // 关闭SRT连接
        DisconnectSRT();
    }

    bool FFmpegSRTPublisher::IsConnected() const
    {
        return connected_;
    }

    bool FFmpegSRTPublisher::SendPacket(const EncodedPacket &packet)
    {
        if (!connected_ || !running_)
        {
            return false;
        }

        // 创建数据包副本
        auto packetCopy = std::make_unique<EncodedPacket>();
        packetCopy->size = packet.size;
        packetCopy->pts = packet.pts;
        packetCopy->dts = packet.dts;
        packetCopy->timestamp = packet.timestamp;
        packetCopy->isKeyFrame = packet.isKeyFrame;

        if (packet.data && packet.size > 0)
        {
            packetCopy->data = new uint8_t[packet.size];
            memcpy(packetCopy->data, packet.data, packet.size);
        }

        // 添加到队列
        {
            std::lock_guard<std::mutex> lock(queueMutex_);

            if (packetQueue_.size() >= 100) // 修复：使用固定值
            {
                // 丢弃最旧的数据包
                packetQueue_.pop();
                {
                    std::lock_guard<std::mutex> statsLock(statsMutex_);
                    stats_.droppedPackets++;
                }
            }

            packetQueue_.push(std::move(packetCopy));
            {
                std::lock_guard<std::mutex> statsLock(statsMutex_);
                stats_.totalPackets++;
                stats_.totalBytes += packet.size;
            }
        }

        // 通知发送线程
        queueCondition_.notify_one();
        return true;
    }

    bool FFmpegSRTPublisher::SendMetadata(const std::string &metadata)
    {
        // 避免未引用参数警告
        (void)metadata;

        // 简单实现，实际应该发送元数据
        return connected_;
    }

    StreamPublisher::Stats FFmpegSRTPublisher::GetStats() const
    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        return stats_;
    }

    bool FFmpegSRTPublisher::SetupSRTSocket()
    {
        // 简单实现，实际应该创建SRT套接字
        return true;
    }

    bool FFmpegSRTPublisher::ConfigureSRTOptions()
    {
        // 配置SRT选项
        return true;
    }

    bool FFmpegSRTPublisher::ConnectSRT()
    {
        // 连接到SRT服务器
        return true;
    }

    void FFmpegSRTPublisher::DisconnectSRT()
    {
        // 断开SRT连接
    }

    bool FFmpegSRTPublisher::SendSRTPacket(const EncodedPacket &packet)
    {
        (void)packet;
        // 发送SRT数据包
        return true;
    }

    void FFmpegSRTPublisher::SendingThreadFunc()
    {
        while (running_)
        {
            std::unique_ptr<EncodedPacket> packet;

            // 获取数据包
            {
                std::unique_lock<std::mutex> lock(queueMutex_);
                queueCondition_.wait(lock, [this]
                                     { return !packetQueue_.empty() || !running_ || !connected_; });

                if (!running_ || !connected_)
                {
                    break;
                }

                if (!packetQueue_.empty())
                {
                    packet = std::move(packetQueue_.front());
                    packetQueue_.pop();
                }
            }

            // 发送数据包
            if (packet)
            {
                if (!SendSRTPacket(*packet))
                {
                    // 发送失败
                    {
                        std::lock_guard<std::mutex> lock(statsMutex_);
                        stats_.droppedPackets++;
                    }
                }
            }
        }
    }

    void FFmpegSRTPublisher::CleanupSRT()
    {
        // 清理SRT资源
    }

    /*
    bool FFmpegSRTPublisher::SendAVPacket(const EncodedPacket &packet)
    {
        // 避免未引用参数警告
        (void)packet;

        // 实现发送AVPacket逻辑
        return false;
    }
    */

    // FFmpegUDPPublisher implementation
    FFmpegUDPPublisher::FFmpegUDPPublisher()
        : formatCtx_(nullptr), videoStream_(nullptr),
          initialized_(false), state_(StreamState::Disconnected), running_(false)
    {
        // FFmpegUtils::InitializeFFmpeg();
    }

    FFmpegUDPPublisher::~FFmpegUDPPublisher()
    {
        Shutdown();
    }

    bool FFmpegUDPPublisher::InitializeEx(const StreamConfigEx &config)
    {
        config_ = config;
        // maxQueueSize_ = config_.bufferSize; // 修复：移除不存在的字段
        initialized_ = true;
        return true;
    }

    bool FFmpegUDPPublisher::Initialize(const Config &config)
    {
        // 避免未引用参数警告
        (void)config;

        // 实际应该从基础配置创建扩展配置
        /*StreamConfigEx configEx;
        configEx.type = "udp";
        configEx.host = config.url;
        configEx.port = 1234; // 默认端口
        return InitializeEx(configEx);*/

        return false;
    }

    void FFmpegUDPPublisher::Shutdown()
    {
        if (!initialized_)
        {
            return;
        }

        // 停止运行
        running_ = false;
        state_ = StreamState::Disconnected;

        // 通知所有等待的线程
        queueCondition_.notify_all();

        // 等待发送线程结束
        if (sendingThread_.joinable())
        {
            sendingThread_.join();
        }

        // 清理资源
        // CleanupUDP();
        CleanupFFmpeg();

        initialized_ = false;
    }

    bool FFmpegUDPPublisher::Connect()
    {
        if (state_ != StreamState::Disconnected)
        {
            return false;
        }

        if (!initialized_)
        {
            return false;
        }

        // 设置UDP选项
        /*if (!SetupUDPSocket())
        {
            return false;
        }*/

        // connected_ = true;
        running_ = true;
        state_ = StreamState::Connected;

        // 启动发送线程
        sendingThread_ = std::thread(&FFmpegUDPPublisher::SendingThreadFunc, this);

        return true;
    }

    void FFmpegUDPPublisher::Disconnect()
    {
        state_ = StreamState::Disconnected;

        // 通知发送线程
        queueCondition_.notify_all();

        // 等待发送线程结束
        if (sendingThread_.joinable())
        {
            sendingThread_.join();
        }

        // 关闭UDP连接
        // DisconnectUDP();
    }

    bool FFmpegUDPPublisher::IsConnected() const
    {
        return state_ == StreamState::Connected;
        // return connected_;
    }

    bool FFmpegUDPPublisher::SendPacket(const EncodedPacket &packet)
    {
        if (state_ != StreamState::Connected || !running_)
        {
            return false;
        }

        // 创建数据包副本
        auto packetCopy = std::make_unique<EncodedPacket>();
        packetCopy->size = packet.size;
        packetCopy->pts = packet.pts;
        packetCopy->dts = packet.dts;
        packetCopy->timestamp = packet.timestamp;
        packetCopy->isKeyFrame = packet.isKeyFrame;

        if (packet.data && packet.size > 0)
        {
            packetCopy->data = new uint8_t[packet.size];
            memcpy(packetCopy->data, packet.data, packet.size);
        }

        {
            std::lock_guard<std::mutex> lock(queueMutex_);
            packetQueue_.push(std::move(packetCopy));
        }

        queueCondition_.notify_one();
        return true;
    }

    bool FFmpegUDPPublisher::SendMetadata(const std::string &metadata)
    {
        // 避免未引用参数警告
        (void)metadata;

        // 简单实现，实际应该发送元数据
        return state_ == StreamState::Connected;
        // return connected_;
    }

    StreamPublisher::Stats FFmpegUDPPublisher::GetStats() const
    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        return stats_;
    }

    bool FFmpegUDPPublisher::ConfigureUDPOptions()
    {
        // 配置UDP选项
        return true;
    }

    bool FFmpegUDPPublisher::SendUDPPacket(const EncodedPacket &packet)
    {
        (void)packet;
        // 发送UDP数据包
        return true;
    }

    void FFmpegUDPPublisher::SendingThreadFunc()
    {
        while (running_)
        {
            std::unique_ptr<EncodedPacket> packet;

            {
                std::unique_lock<std::mutex> lock(queueMutex_);
                queueCondition_.wait(lock, [this]
                                     { return !packetQueue_.empty() || !running_; });

                if (!running_ && packetQueue_.empty())
                {
                    break;
                }

                if (!packetQueue_.empty())
                {
                    packet = std::move(packetQueue_.front());
                    packetQueue_.pop();
                }
            }

            if (packet)
            {
                // 注释掉对不存在方法SendUDPPacket的调用
                // if (!SendUDPPacket(*packet))
                {
                    // 发送失败处理
                    // {
                    //     std::lock_guard<std::mutex> lock(statsMutex_);
                    //     stats_.droppedPackets++;
                    // }
                }

                // UpdateStats(*packet);
            }
        }
    }

    void FFmpegUDPPublisher::CleanupFFmpeg()
    {
        // 清理FFmpeg资源
    }

    /*
    bool FFmpegUDPPublisher::SendAVPacket(const EncodedPacket &packet)
    {
        // 避免未引用参数警告
        (void)packet;

        // 实现发送AVPacket逻辑
        return false;
    }
    */

    // FFmpegFilePublisher implementation
    FFmpegFilePublisher::FFmpegFilePublisher()
        : formatCtx_(nullptr), videoStream_(nullptr),
          initialized_(false), recording_(false), running_(false),
          startTime_(0), lastTimestamp_(0),
          segmentStartTime_(0), currentFileSize_(0), segmentIndex_(0)
    {
        // FFmpegUtils::InitializeFFmpeg();
    }

    FFmpegFilePublisher::~FFmpegFilePublisher()
    {
        Shutdown();
    }

    bool FFmpegFilePublisher::InitializeEx(const StreamConfigEx &config)
    {
        config_ = config;
        // maxQueueSize_ = config_.bufferSize; // 修复：移除不存在的字段
        initialized_ = true;
        return true;
    }

    bool FFmpegFilePublisher::Initialize(const Config &config)
    {
        StreamConfigEx extConfig;
        extConfig.url = config.url;
        extConfig.type = config.protocol; // 修复：使用type而不是protocol
        // 修复：移除不存在的字段，添加存在的字段
        return InitializeEx(extConfig);
    }

    void FFmpegFilePublisher::Shutdown()
    {
        Disconnect();

        // 停止写入线程
        running_ = false;
        queueCondition_.notify_all();

        if (writingThread_.joinable())
        {
            writingThread_.join();
        }

        // 关闭当前文件
        CloseCurrentFile();

        // 清理资源
        CleanupFFmpeg();
        CleanupFiles();

        initialized_ = false;
    }

    bool FFmpegFilePublisher::Connect()
    {
        if (!initialized_)
        {
            return false;
        }

        // 打开新文件
        if (!OpenNewFile())
        {
            return false;
        }

        recording_ = true;
        running_ = true;

        // 启动写入线程
        writingThread_ = std::thread(&FFmpegFilePublisher::WritingThreadFunc, this);

        return true;
    }

    void FFmpegFilePublisher::Disconnect()
    {
        recording_ = false;

        // 通知写入线程
        queueCondition_.notify_all();

        // 等待写入线程结束
        if (writingThread_.joinable())
        {
            writingThread_.join();
        }

        // 关闭当前文件
        CloseCurrentFile();
    }

    bool FFmpegFilePublisher::IsConnected() const
    {
        return recording_;
    }

    bool FFmpegFilePublisher::SendPacket(const EncodedPacket &packet)
    {
        if (!recording_ || !running_)
        {
            return false;
        }

        // 检查是否需要开始新段
        if (ShouldStartNewSegment(packet))
        {
            if (!StartNewSegment())
            {
                return false;
            }
        }

        // 创建数据包副本
        auto packetCopy = std::make_unique<EncodedPacket>();
        packetCopy->size = packet.size;
        packetCopy->pts = packet.pts;
        packetCopy->dts = packet.dts;
        packetCopy->timestamp = packet.timestamp;
        packetCopy->isKeyFrame = packet.isKeyFrame;

        if (packet.data && packet.size > 0)
        {
            packetCopy->data = new uint8_t[packet.size];
            memcpy(packetCopy->data, packet.data, packet.size);
        }

        // 添加到队列
        {
            std::lock_guard<std::mutex> lock(queueMutex_);

            if (packetQueue_.size() >= 100) // 修复：使用固定值
            {
                // 丢弃最旧的数据包
                packetQueue_.pop();
                {
                    std::lock_guard<std::mutex> statsLock(statsMutex_);
                    stats_.droppedPackets++;
                }
            }

            packetQueue_.push(std::move(packetCopy));
            {
                std::lock_guard<std::mutex> statsLock(statsMutex_);
                stats_.totalPackets++;
                stats_.totalBytes += packet.size;
            }
        }

        // 通知写入线程
        queueCondition_.notify_one();
        return true;
    }

    bool FFmpegFilePublisher::SendMetadata(const std::string &metadata)
    {
        // 避免未引用参数警告
        (void)metadata;

        // 简单实现，实际应该写入元数据
        return recording_;
    }

    StreamPublisher::Stats FFmpegFilePublisher::GetStats() const
    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        return stats_;
    }

    bool FFmpegFilePublisher::StartNewSegment()
    {
        // 关闭当前文件
        if (!CloseCurrentFile())
        {
            return false;
        }

        // 打开新文件
        if (!OpenNewFile())
        {
            return false;
        }

        segmentIndex_++;
        segmentStartTime_ = av_gettime();
        currentFileSize_ = 0;

        return true;
    }

    std::string FFmpegFilePublisher::GenerateFileName()
    {
        // 生成文件名
        std::stringstream ss;
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        ss << "recording_" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
           << "_" << segmentIndex_ << "." << "mp4"; // 修复：使用固定值

        return ss.str();
    }

    bool FFmpegFilePublisher::OpenNewFile()
    {
        currentFileName_ = GenerateFileName();
        completedFiles_.push_back(currentFileName_);

        // 创建输出格式上下文
        const AVOutputFormat *outputFormat = av_guess_format("mp4", nullptr, nullptr); // 修复：使用固定值
        if (!outputFormat)
        {
            return false;
        }

        int ret = avformat_alloc_output_context2(&formatCtx_, outputFormat, nullptr, currentFileName_.c_str());
        if (ret < 0)
        {
            // FFmpegUtils::LogFFmpegError(ret, "avformat_alloc_output_context2");
            return false;
        }

        // 打开输出文件
        if (!(outputFormat->flags & AVFMT_NOFILE))
        {
            ret = avio_open(&formatCtx_->pb, currentFileName_.c_str(), AVIO_FLAG_WRITE);
            if (ret < 0)
            {
                // FFmpegUtils::LogFFmpegError(ret, "avio_open");
                return false;
            }
        }

        return true;
    }

    bool FFmpegFilePublisher::CloseCurrentFile()
    {
        if (formatCtx_)
        {
            // 写入尾部
            av_write_trailer(formatCtx_);

            // 关闭文件
            if (!(formatCtx_->oformat->flags & AVFMT_NOFILE))
            {
                avio_closep(&formatCtx_->pb);
            }

            // 释放上下文
            avformat_free_context(formatCtx_);
            formatCtx_ = nullptr;
        }

        return true;
    }

    bool FFmpegFilePublisher::ShouldStartNewSegment(const EncodedPacket &packet)
    {
        (void)packet;
        // 简单实现，实际应该根据配置决定是否开始新段
        return false;
    }

    bool FFmpegFilePublisher::WriteAVPacket(const EncodedPacket &packet)
    {
        AVPacket *avPacket = CreateAVPacket(packet);
        if (!avPacket)
        {
            return false;
        }

        bool success = false;
        if (formatCtx_)
        {
            // 写入数据包
            int ret = av_interleaved_write_frame(formatCtx_, avPacket);
            if (ret >= 0)
            {
                success = true;
                currentFileSize_ += packet.size;
                UpdateStats(packet);
            }
            else
            {
                // FFmpegUtils::LogFFmpegError(ret, "av_interleaved_write_frame");
            }
        }

        av_packet_free(&avPacket);
        return success;
    }

    AVPacket *FFmpegFilePublisher::CreateAVPacket(const EncodedPacket &packet)
    {
        AVPacket *avPacket = av_packet_alloc();
        if (!avPacket)
        {
            return nullptr;
        }

        avPacket->data = packet.data;
        avPacket->size = static_cast<int>(packet.size);
        avPacket->pts = packet.pts;
        avPacket->dts = packet.dts;

        if (packet.isKeyFrame)
        {
            avPacket->flags |= AV_PKT_FLAG_KEY;
        }

        return avPacket;
    }

    void FFmpegFilePublisher::WritingThreadFunc()
    {
        while (running_)
        {
            std::unique_ptr<EncodedPacket> packet;

            // 获取数据包
            {
                std::unique_lock<std::mutex> lock(queueMutex_);
                queueCondition_.wait(lock, [this]
                                     { return !packetQueue_.empty() || !running_ || !recording_; });

                if (!running_ || !recording_)
                {
                    break;
                }

                if (!packetQueue_.empty())
                {
                    packet = std::move(packetQueue_.front());
                    packetQueue_.pop();
                }
            }

            // 写入数据包
            if (packet)
            {
                if (!WriteAVPacket(*packet))
                {
                    // 写入失败
                    {
                        std::lock_guard<std::mutex> lock(statsMutex_);
                        stats_.droppedPackets++;
                    }
                }
            }
        }
    }

    int64_t FFmpegFilePublisher::ConvertTimestamp(uint64_t timestamp)
    {
        // 转换时间戳到合适的格式
        return static_cast<int64_t>(timestamp);
    }

    void FFmpegFilePublisher::CleanupFFmpeg()
    {
        if (formatCtx_)
        {
            avformat_free_context(formatCtx_);
            formatCtx_ = nullptr;
        }

        videoStream_ = nullptr;
    }

    void FFmpegFilePublisher::CleanupFiles()
    {
        // 清理文件相关资源
        completedFiles_.clear();
    }

    void FFmpegFilePublisher::UpdateStats(const EncodedPacket &packet)
    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        stats_.totalPackets++;
        stats_.totalBytes += packet.size;
    }

    // FFmpegPublisherFactory implementation
    std::unique_ptr<StreamPublisher> FFmpegPublisherFactory::CreatePublisher(const StreamConfigEx &config)
    {
        if (config.type == "rtmp") // 修复：使用type而不是protocol
        {
            auto publisher = std::make_unique<FFmpegRTMPPublisher>();
            if (publisher->InitializeEx(config))
            {
                return std::move(publisher);
            }
        }
        else if (config.type == "srt") // 修复：使用type而不是protocol
        {
            auto publisher = std::make_unique<FFmpegSRTPublisher>();
            if (publisher->InitializeEx(config))
            {
                return std::move(publisher);
            }
        }
        else if (config.type == "file") // 修复：使用type而不是protocol
        {
            auto publisher = std::make_unique<FFmpegFilePublisher>();
            if (publisher->InitializeEx(config))
            {
                return std::move(publisher);
            }
        }

        return nullptr;
    }

} // namespace StreamCapture