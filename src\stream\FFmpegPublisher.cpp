#include "FFmpegPublisher.h"
#include "StreamCaptureController.h"
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>

extern "C" {
#include <libavutil/error.h>
#include <libavutil/time.h>
}

namespace StreamCapture {

// FFmpegRTMPPublisher 实现
FFmpegRTMPPublisher::FFmpegRTMPPublisher()
    : formatCtx_(nullptr), videoStream_(nullptr), codecCtx_(nullptr),
      initialized_(false), connected_(false), running_(false),
      shouldReconnect_(false), reconnectAttempts_(0), maxQueueSize_(100),
      startTime_(0), lastDts_(0), lastPts_(0) {
}

FFmpegRTMPPublisher::~FFmpegRTMPPublisher() {
    Shutdown();
}

bool FFmpegRTMPPublisher::Initialize(const Config& config) {
    // 转换为扩展配置
    StreamConfigEx extConfig;
    extConfig.id = "rtmp_default";
    extConfig.enabled = true;
    extConfig.type = "rtmp";
    extConfig.url = config.url;
    extConfig.reconnectInterval = 5000;
    extConfig.maxReconnectAttempts = 10;
    extConfig.enableAutoReconnect = true;
    extConfig.connectionTimeout = 10000;
    extConfig.sendTimeout = 5000;
    
    return InitializeEx(extConfig);
}

bool FFmpegRTMPPublisher::InitializeEx(const StreamConfigEx& config) {
    if (initialized_) {
        return false;
    }
    
    config_ = config;
    
    // 设置 FFmpeg 网络
    avformat_network_init();
    
    // 初始化统计
    ResetStats();
    
    initialized_ = true;
    return true;
}

void FFmpegRTMPPublisher::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    // 停止重连
    shouldReconnect_ = false;
    
    // 断开连接
    Disconnect();
    
    // 等待重连线程结束
    if (reconnectThread_.joinable()) {
        reconnectThread_.join();
    }
    
    // 清理资源
    CleanupFFmpeg();
    CleanupQueues();
    
    initialized_ = false;
}

bool FFmpegRTMPPublisher::Connect() {
    if (!initialized_ || connected_) {
        return false;
    }
    
    // 设置输出格式
    if (!SetupOutputFormat()) {
        return false;
    }
    
    // 设置视频流
    if (!SetupVideoStream()) {
        return false;
    }
    
    // 设置 RTMP 选项
    if (!SetupRTMPOptions()) {
        return false;
    }
    
    // 打开输出
    if (!OpenOutput()) {
        return false;
    }
    
    // 启动发送线程
    running_ = true;
    sendingThread_ = std::thread(&FFmpegRTMPPublisher::SendingThreadFunc, this);
    
    // 启动重连线程
    if (config_.enableAutoReconnect) {
        shouldReconnect_ = true;
        reconnectThread_ = std::thread(&FFmpegRTMPPublisher::ReconnectThreadFunc, this);
    }
    
    connected_ = true;
    state_ = StreamState::Connected;
    startTime_ = av_gettime();
    
    return true;
}

void FFmpegRTMPPublisher::Disconnect() {
    if (!connected_) {
        return;
    }
    
    // 停止发送线程
    running_ = false;
    queueCondition_.notify_all();
    
    if (sendingThread_.joinable()) {
        sendingThread_.join();
    }
    
    // 关闭输出
    if (formatCtx_) {
        if (formatCtx_->pb) {
            av_write_trailer(formatCtx_);
            avio_closep(&formatCtx_->pb);
        }
        avformat_free_context(formatCtx_);
        formatCtx_ = nullptr;
    }
    
    connected_ = false;
    state_ = StreamState::Disconnected;
}

bool FFmpegRTMPPublisher::IsConnected() const {
    return connected_;
}

bool FFmpegRTMPPublisher::SendPacket(const EncodedPacket& packet) {
    if (!connected_ || !running_) {
        return false;
    }
    
    // 创建数据包副本
    auto packetCopy = std::make_unique<EncodedPacket>();
    packetCopy->size = packet.size;
    packetCopy->data = new uint8_t[packet.size];
    memcpy(packetCopy->data, packet.data, packet.size);
    packetCopy->timestamp = packet.timestamp;
    packetCopy->dts = packet.dts;
    packetCopy->pts = packet.pts;
    packetCopy->isKeyFrame = packet.isKeyFrame;
    
    // 添加到发送队列
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        
        // 检查队列大小
        if (packetQueue_.size() >= maxQueueSize_) {
            // 丢弃最旧的非关键帧
            while (!packetQueue_.empty() && !packetQueue_.front()->isKeyFrame) {
                packetQueue_.pop();
                stats_.droppedPackets++;
            }
            
            // 如果队列仍然满，丢弃最旧的包
            if (packetQueue_.size() >= maxQueueSize_) {
                packetQueue_.pop();
                stats_.droppedPackets++;
            }
        }
        
        packetQueue_.push(std::move(packetCopy));
    }
    
    queueCondition_.notify_one();
    return true;
}

bool FFmpegRTMPPublisher::SendMetadata(const std::string& metadata) {
    if (!connected_) {
        return false;
    }
    
    // 设置流元数据
    if (formatCtx_) {
        av_dict_set(&formatCtx_->metadata, "title", metadata.c_str(), 0);
        return true;
    }
    
    return false;
}

StreamPublisher::Stats FFmpegRTMPPublisher::GetStats() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return stats_;
}

bool FFmpegRTMPPublisher::SetupOutputFormat() {
    int ret = avformat_alloc_output_context2(&formatCtx_, nullptr, "flv", config_.url.c_str());
    if (ret < 0) {
        HandleSendError(ret);
        return false;
    }
    
    return true;
}

bool FFmpegRTMPPublisher::SetupVideoStream() {
    if (!formatCtx_) {
        return false;
    }
    
    // 创建视频流
    videoStream_ = avformat_new_stream(formatCtx_, nullptr);
    if (!videoStream_) {
        return false;
    }
    
    videoStream_->id = formatCtx_->nb_streams - 1;
    
    // 设置编解码器参数（这些参数应该从编码器获取）
    AVCodecParameters* codecpar = videoStream_->codecpar;
    codecpar->codec_type = AVMEDIA_TYPE_VIDEO;
    codecpar->codec_id = AV_CODEC_ID_H264;
    codecpar->width = 1920;  // 应该从配置获取
    codecpar->height = 1080; // 应该从配置获取
    codecpar->format = AV_PIX_FMT_YUV420P;
    codecpar->bit_rate = 2000000; // 2Mbps
    
    // 设置时间基
    videoStream_->time_base = {1, 90000}; // 90kHz
    
    return true;
}

bool FFmpegRTMPPublisher::SetupRTMPOptions() {
    if (!formatCtx_) {
        return false;
    }
    
    AVDictionary* opts = nullptr;
    
    // 设置 RTMP 选项
    av_dict_set(&opts, "rtmp_live", "live", 0);
    av_dict_set(&opts, "rtmp_buffer", "1000", 0); // 1秒缓冲
    av_dict_set_int(&opts, "timeout", config_.connectionTimeout * 1000, 0); // 微秒
    
    // 应用选项
    formatCtx_->interrupt_callback.opaque = this;
    
    av_dict_free(&opts);
    return true;
}

bool FFmpegRTMPPublisher::OpenOutput() {
    if (!formatCtx_) {
        return false;
    }
    
    // 打开输出 URL
    int ret = avio_open(&formatCtx_->pb, config_.url.c_str(), AVIO_FLAG_WRITE);
    if (ret < 0) {
        HandleSendError(ret);
        return false;
    }
    
    // 写入文件头
    ret = avformat_write_header(formatCtx_, nullptr);
    if (ret < 0) {
        HandleSendError(ret);
        return false;
    }
    
    return true;
}

void FFmpegRTMPPublisher::SendingThreadFunc() {
    while (running_) {
        std::unique_ptr<EncodedPacket> packet;
        
        // 获取数据包
        {
            std::unique_lock<std::mutex> lock(queueMutex_);
            queueCondition_.wait(lock, [this] { return !packetQueue_.empty() || !running_; });
            
            if (!running_) {
                break;
            }
            
            packet = std::move(packetQueue_.front());
            packetQueue_.pop();
        }
        
        // 发送数据包
        if (packet) {
            bool success = SendAVPacket(*packet);
            UpdateStats(*packet, success);
            
            if (!success && IsRecoverableError(-1)) {
                // 连接错误，触发重连
                connected_ = false;
                state_ = StreamState::Reconnecting;
            }
        }
    }
}

bool FFmpegRTMPPublisher::SendAVPacket(const EncodedPacket& packet) {
    if (!formatCtx_ || !videoStream_) {
        return false;
    }
    
    AVPacket* avPacket = CreateAVPacket(packet);
    if (!avPacket) {
        return false;
    }
    
    bool success = WritePacketToStream(avPacket);
    
    av_packet_free(&avPacket);
    return success;
}

AVPacket* FFmpegRTMPPublisher::CreateAVPacket(const EncodedPacket& packet) {
    AVPacket* avPacket = av_packet_alloc();
    if (!avPacket) {
        return nullptr;
    }
    
    // 分配数据
    int ret = av_new_packet(avPacket, packet.size);
    if (ret < 0) {
        av_packet_free(&avPacket);
        return nullptr;
    }
    
    // 复制数据
    memcpy(avPacket->data, packet.data, packet.size);
    
    // 设置属性
    avPacket->stream_index = videoStream_->index;
    avPacket->flags = packet.isKeyFrame ? AV_PKT_FLAG_KEY : 0;
    
    // 转换时间戳
    UpdateTimestamps(avPacket, packet);
    
    return avPacket;
}

bool FFmpegRTMPPublisher::WritePacketToStream(AVPacket* avPacket) {
    if (!formatCtx_ || !avPacket) {
        return false;
    }
    
    // 写入数据包
    int ret = av_interleaved_write_frame(formatCtx_, avPacket);
    if (ret < 0) {
        HandleSendError(ret);
        return false;
    }
    
    return true;
}

void FFmpegRTMPPublisher::UpdateTimestamps(AVPacket* packet, const EncodedPacket& srcPacket) {
    if (!packet || !videoStream_) {
        return;
    }
    
    // 转换时间戳到流时间基
    int64_t pts = av_rescale_q(srcPacket.pts, {1, 90000}, videoStream_->time_base);
    int64_t dts = av_rescale_q(srcPacket.dts, {1, 90000}, videoStream_->time_base);
    
    // 确保时间戳单调递增
    if (pts <= lastPts_) {
        pts = lastPts_ + 1;
    }
    if (dts <= lastDts_) {
        dts = lastDts_ + 1;
    }
    
    packet->pts = pts;
    packet->dts = dts;
    packet->duration = av_rescale_q(1, {1, 30}, videoStream_->time_base); // 假设30fps
    
    lastPts_ = pts;
    lastDts_ = dts;
}

void FFmpegRTMPPublisher::HandleSendError(int errorCode) {
    std::string errorMsg = FFmpegUtils::GetErrorString(errorCode);
    // 这里可以添加日志记录
    // LOG_ERROR_F("RTMP send error: {} ({})", errorMsg, errorCode);
}

bool FFmpegRTMPPublisher::IsRecoverableError(int errorCode) {
    // 检查是否是可恢复的网络错误
    return errorCode == AVERROR(EPIPE) || 
           errorCode == AVERROR(ECONNRESET) || 
           errorCode == AVERROR(ETIMEDOUT);
}

void FFmpegRTMPPublisher::UpdateStats(const EncodedPacket& packet, bool success) {
    std::lock_guard<std::mutex> lock(statsMutex_);
    
    if (success) {
        stats_.totalPackets++;
        stats_.totalBytes += packet.size;
    } else {
        stats_.droppedPackets++;
    }
    
    // 计算比特率
    int64_t currentTime = av_gettime();
    if (startTime_ > 0) {
        int64_t elapsedTime = currentTime - startTime_;
        if (elapsedTime > 0) {
            stats_.avgBitrate = (float)(stats_.totalBytes * 8.0 * 1000000.0 / elapsedTime / 1000.0); // kbps
        }
    }
}

void FFmpegRTMPPublisher::ReconnectThreadFunc() {
    while (shouldReconnect_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        if (!connected_ && shouldReconnect_) {
            if (reconnectAttempts_ < config_.maxReconnectAttempts) {
                reconnectAttempts_++;
                
                // 尝试重连
                if (Reconnect()) {
                    reconnectAttempts_ = 0;
                    if (reconnectCallback_) {
                        reconnectCallback_(true);
                    }
                } else {
                    std::this_thread::sleep_for(std::chrono::milliseconds(config_.reconnectInterval));
                }
            } else {
                // 达到最大重连次数
                shouldReconnect_ = false;
                if (reconnectCallback_) {
                    reconnectCallback_(false);
                }
            }
        }
    }
}

bool FFmpegRTMPPublisher::Reconnect() {
    Disconnect();
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    return Connect();
}

void FFmpegRTMPPublisher::CleanupFFmpeg() {
    if (formatCtx_) {
        if (formatCtx_->pb) {
            avio_closep(&formatCtx_->pb);
        }
        avformat_free_context(formatCtx_);
        formatCtx_ = nullptr;
    }
    
    videoStream_ = nullptr;
    codecCtx_ = nullptr;
}

void FFmpegRTMPPublisher::CleanupQueues() {
    std::lock_guard<std::mutex> lock(queueMutex_);
    while (!packetQueue_.empty()) {
        packetQueue_.pop();
    }
}

} // namespace StreamCapture
