#pragma once

#include "Common.h"
#include "StreamPublisher.h"
#include <memory>
#include <vector>
#include <mutex>
#include <thread>
#include <queue>
#include <atomic>
#include <condition_variable>

namespace StreamCapture
{

    // 多流控制器
    class MultiStreamController
    {
    public:
        MultiStreamController();
        ~MultiStreamController();

        // 初始化和清理
        bool Initialize(const std::vector<StreamConfigEx> &configs);
        void Shutdown();

        // 数据包发送
        bool SendPacket(const EncodedPacket &packet);

        // 状态查询
        bool IsRunning() const { return running_; }
        size_t GetActiveStreamCount() const;

        // 统计信息
        struct Stats
        {
            uint64_t totalPacketsSent = 0;
            uint64_t totalBytesTransferred = 0;
            uint64_t droppedPackets = 0;
            uint32_t activeStreams = 0;
            double avgBitrate = 0.0;
        };

        Stats GetStats() const;

    private:
        // 流发布器管理
        std::vector<std::unique_ptr<StreamPublisher>> publishers_;
        std::vector<StreamConfigEx> configs_;

        // 线程管理
        std::atomic<bool> running_;
        std::thread processingThread_;

        // 数据包队列
        std::queue<std::unique_ptr<EncodedPacket>> packetQueue_;
        std::mutex queueMutex_;
        std::condition_variable queueCondition_;
        size_t maxQueueSize_;

        // 统计信息
        mutable std::mutex statsMutex_;
        Stats stats_;

        // 内部方法
        void ProcessingThreadFunc();
        void UpdateStats(size_t packetSize);
        void ResetStats();

        // 创建发布器
        std::unique_ptr<StreamPublisher> CreatePublisher(const StreamConfigEx &config);
    };

} // namespace StreamCapture
