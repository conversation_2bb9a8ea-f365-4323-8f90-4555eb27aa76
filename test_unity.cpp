#include <windows.h>
#include <d3d11.h>
#include <iostream>
#include <thread>
#include <chrono>

#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "dxgi.lib")

int main()
{
    std::cout << "Unity.exe Test Application Starting..." << std::endl;
    
    // 创建D3D11设备和上下文
    ID3D11Device* device = nullptr;
    ID3D11DeviceContext* context = nullptr;
    IDXGISwapChain* swapChain = nullptr;
    
    // 创建窗口
    HWND hwnd = CreateWindowA("STATIC", "Unity Test", WS_OVERLAPPEDWINDOW,
                             100, 100, 800, 600, nullptr, nullptr, GetModuleHandle(nullptr), nullptr);
    
    if (!hwnd) {
        std::cout << "Failed to create window" << std::endl;
        return -1;
    }
    
    ShowWindow(hwnd, SW_SHOW);
    UpdateWindow(hwnd);
    
    // 创建D3D11设备
    DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
    swapChainDesc.BufferCount = 1;
    swapChainDesc.BufferDesc.Width = 800;
    swapChainDesc.BufferDesc.Height = 600;
    swapChainDesc.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.OutputWindow = hwnd;
    swapChainDesc.SampleDesc.Count = 1;
    swapChainDesc.Windowed = TRUE;
    
    HRESULT hr = D3D11CreateDeviceAndSwapChain(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        0,
        nullptr,
        0,
        D3D11_SDK_VERSION,
        &swapChainDesc,
        &swapChain,
        &device,
        nullptr,
        &context
    );
    
    if (FAILED(hr)) {
        std::cout << "Failed to create D3D11 device: " << std::hex << hr << std::endl;
        return -1;
    }
    
    std::cout << "D3D11 device created successfully" << std::endl;
    std::cout << "Running for 30 seconds to test hook..." << std::endl;
    
    // 运行30秒，期间进行渲染
    auto startTime = std::chrono::steady_clock::now();
    MSG msg = {};
    
    while (std::chrono::steady_clock::now() - startTime < std::chrono::seconds(30))
    {
        // 处理消息
        while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE))
        {
            if (msg.message == WM_QUIT) break;
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        if (msg.message == WM_QUIT) break;
        
        // 简单的渲染
        float clearColor[4] = { 0.0f, 0.2f, 0.4f, 1.0f };
        ID3D11RenderTargetView* renderTargetView = nullptr;
        
        ID3D11Texture2D* backBuffer = nullptr;
        swapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (void**)&backBuffer);
        device->CreateRenderTargetView(backBuffer, nullptr, &renderTargetView);
        
        context->ClearRenderTargetView(renderTargetView, clearColor);
        swapChain->Present(1, 0);
        
        if (renderTargetView) renderTargetView->Release();
        if (backBuffer) backBuffer->Release();
        
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
    
    std::cout << "Test completed" << std::endl;
    
    // 清理
    if (context) context->Release();
    if (device) device->Release();
    if (swapChain) swapChain->Release();
    DestroyWindow(hwnd);
    
    return 0;
}
