﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{1E879877-8449-390F-9A11-8868C84013B4}"
	ProjectSection(ProjectDependencies) = postProject
		{9127D789-2686-32FC-BDDF-98FB512F1087} = {9127D789-2686-32FC-BDDF-98FB512F1087}
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA} = {B83C76B5-072D-3A4B-8333-6D3063F5D0BA}
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA} = {A3DC150F-89B7-3A6F-A77E-CFFC8B9942<PERSON>}
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1} = {880FBAB7-2098-33ED-9BA0-95C81247A1E1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{52013944-D804-3E14-AAA5-6ABABB7762ED}"
	ProjectSection(ProjectDependencies) = postProject
		{1E879877-8449-390F-9A11-8868C84013B4} = {1E879877-8449-390F-9A11-8868C84013B4}
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1} = {880FBAB7-2098-33ED-9BA0-95C81247A1E1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SimpleStreamTest", "SimpleStreamTest.vcxproj", "{9127D789-2686-32FC-BDDF-98FB512F1087}"
	ProjectSection(ProjectDependencies) = postProject
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1} = {880FBAB7-2098-33ED-9BA0-95C81247A1E1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "StreamCapture", "StreamCapture.vcxproj", "{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}"
	ProjectSection(ProjectDependencies) = postProject
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1} = {880FBAB7-2098-33ED-9BA0-95C81247A1E1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "StreamCaptureTest", "StreamCaptureTest.vcxproj", "{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}"
	ProjectSection(ProjectDependencies) = postProject
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1} = {880FBAB7-2098-33ED-9BA0-95C81247A1E1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{880FBAB7-2098-33ED-9BA0-95C81247A1E1}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1E879877-8449-390F-9A11-8868C84013B4}.Debug|x64.ActiveCfg = Debug|x64
		{1E879877-8449-390F-9A11-8868C84013B4}.Debug|x64.Build.0 = Debug|x64
		{1E879877-8449-390F-9A11-8868C84013B4}.Release|x64.ActiveCfg = Release|x64
		{1E879877-8449-390F-9A11-8868C84013B4}.Release|x64.Build.0 = Release|x64
		{1E879877-8449-390F-9A11-8868C84013B4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1E879877-8449-390F-9A11-8868C84013B4}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{1E879877-8449-390F-9A11-8868C84013B4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1E879877-8449-390F-9A11-8868C84013B4}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{52013944-D804-3E14-AAA5-6ABABB7762ED}.Debug|x64.ActiveCfg = Debug|x64
		{52013944-D804-3E14-AAA5-6ABABB7762ED}.Release|x64.ActiveCfg = Release|x64
		{52013944-D804-3E14-AAA5-6ABABB7762ED}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{52013944-D804-3E14-AAA5-6ABABB7762ED}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.Debug|x64.ActiveCfg = Debug|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.Debug|x64.Build.0 = Debug|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.Release|x64.ActiveCfg = Release|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.Release|x64.Build.0 = Release|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9127D789-2686-32FC-BDDF-98FB512F1087}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.Debug|x64.ActiveCfg = Debug|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.Debug|x64.Build.0 = Debug|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.Release|x64.ActiveCfg = Release|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.Release|x64.Build.0 = Release|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B83C76B5-072D-3A4B-8333-6D3063F5D0BA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.Debug|x64.ActiveCfg = Debug|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.Debug|x64.Build.0 = Debug|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.Release|x64.ActiveCfg = Release|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.Release|x64.Build.0 = Release|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A3DC150F-89B7-3A6F-A77E-CFFC8B9942CA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.Debug|x64.ActiveCfg = Debug|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.Debug|x64.Build.0 = Debug|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.Release|x64.ActiveCfg = Release|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.Release|x64.Build.0 = Release|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{880FBAB7-2098-33ED-9BA0-95C81247A1E1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {35628837-DAE2-3E2F-B7DF-25E3297D3CB1}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
