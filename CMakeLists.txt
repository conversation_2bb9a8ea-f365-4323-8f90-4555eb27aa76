cmake_minimum_required(VERSION 3.16)

# 设置 vcpkg 工具链（必须在 project 之前）
set(CMAKE_TOOLCHAIN_FILE "c:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake" CACHE STRING "")
# 指定 triplet（如 x64-windows）
set(VCPKG_TARGET_TRIPLET "x64-windows" CACHE STRING "")

project(StreamCapture VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows 7兼容性设置
if(WIN32)
  add_definitions(-DWINVER=0x0601 -D_WIN32_WINNT=0x0601)
  # 静态链接运行时库以提高兼容性
  set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
endif()

# 编译选项
if(MSVC)
  add_compile_options(/W4 /WX- /MP /utf-8)
  add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
  add_compile_options(-Wall -Wextra -Wpedantic)
  add_compile_options(-finput-charset=UTF-8 -fexec-charset=UTF-8)
endif()

#查找FFMPEG库
find_package(FFMPEG REQUIRED)
include_directories(${FFMPEG_INCLUDE_DIRS})
link_directories(${FFMPEG_LIBRARY_DIRS})


if(FFMPEG_FOUND)
  message(STATUS "FFMPEG版本: ${FFMPEG_VERSION}")
message(STATUS "包含路径: ${FFMPEG_INCLUDE_DIRS}")
else() 
  message(ERROR "未找到FFMPEG")
endif()

# 使用 vcpkg 方式查找 MinHook
find_package(minhook CONFIG QUIET)

if(minhook_FOUND)
  message(STATUS "MinHook: FOUND via vcpkg")
  set(MINHOOK_FOUND TRUE)
else()
  # 备用方案：手动查找
  find_path(MINHOOK_INCLUDE_DIR MinHook.h)
  find_library(MINHOOK_LIBRARY minhook)

  if(MINHOOK_INCLUDE_DIR AND MINHOOK_LIBRARY)
      set(MINHOOK_FOUND TRUE)
      message(STATUS "MinHook: FOUND via manual search")
  endif()
endif()

# 包含目录
include_directories(
  ${CMAKE_SOURCE_DIR}/include
)

# 如果使用手动查找的方式，添加包含目录
if(NOT FFMPEG_FOUND AND FFMPEG_FOUND)
  include_directories(
      ${AVCODEC_INCLUDE_DIR}
      ${AVFORMAT_INCLUDE_DIR}
      ${AVUTIL_INCLUDE_DIR}
      ${SWSCALE_INCLUDE_DIR}
  )
endif()

if(NOT minhook_FOUND AND MINHOOK_FOUND)
  include_directories(${MINHOOK_INCLUDE_DIR})
endif()

# 源文件
set(SOURCES
  src/main.cpp
  src/core/StreamCaptureController.cpp
  src/core/HookCore.cpp
  src/core/ConfigManager.cpp
  src/core/MediaServerManager.cpp
  src/utils/Win7Compatibility.cpp
  src/capture/FrameProcessor.cpp
  src/encoder/VideoEncoder.cpp
  src/encoder/FFmpegEncoder.cpp
  src/stream/StreamPublisher.cpp
  src/stream/FFmpegPublisher.cpp
  src/stream/MultiStreamController.cpp
)

# 头文件
set(HEADERS
  include/Common.h
  include/StreamCaptureController.h
  include/HookCore.h
  include/ConfigManager.h
  include/MediaServerManager.h
  include/Win7Compatibility.h
  include/FrameProcessor.h
  include/VideoEncoder.h
  include/FFmpegEncoder.h
  include/StreamPublisher.h
  include/FFmpegPublisher.h
  include/MultiStreamController.h
  include/StreamCaptureTest.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
  # Windows系统库
  kernel32
  user32
  gdi32
  d3d11
  dxgi
  psapi
  ws2_32
  wininet
  # MinHook
  minhook::minhook
)

# 链接 FFMPEG - 优先使用 vcpkg 目标
if(FFMPEG_FOUND)
  target_link_libraries(${PROJECT_NAME} ${FFMPEG_LIBRARIES}  )
elseif(FFMPEG_FOUND)
  # 使用手动找到的库
  target_link_libraries(${PROJECT_NAME}
      ${AVCODEC_LIBRARY}
      ${AVFORMAT_LIBRARY}
      ${AVUTIL_LIBRARY}
      ${SWSCALE_LIBRARY}
  )
endif()

# 链接 MinHook - 优先使用 vcpkg 目标
if(minhook_FOUND)
  target_link_libraries(${PROJECT_NAME} minhook::minhook)
elseif(MINHOOK_FOUND)
  # 使用手动找到的库
  target_link_libraries(${PROJECT_NAME} ${MINHOOK_LIBRARY})
endif()

# 创建示例配置
if(EXISTS ${CMAKE_SOURCE_DIR}/config/capture.json)
  configure_file(
      ${CMAKE_SOURCE_DIR}/config/capture.json
      ${CMAKE_BINARY_DIR}/config/capture.json
      COPYONLY
  )
endif()

# 开发者选项
option(BUILD_TESTS "Build test suite" OFF)
option(BUILD_EXAMPLES "Build examples" OFF)
option(ENABLE_PROFILING "Enable profiling" OFF)

if(BUILD_TESTS AND EXISTS ${CMAKE_SOURCE_DIR}/tests)
  enable_testing()
  add_subdirectory(tests)
endif()

if(BUILD_EXAMPLES AND EXISTS ${CMAKE_SOURCE_DIR}/examples)
  add_subdirectory(examples)
endif()

if(ENABLE_PROFILING)
  add_compile_definitions(ENABLE_PROFILING)
endif()

# 输出信息
message(STATUS "StreamCapture ${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")

if(WIN32)
  message(STATUS "Windows 7 compatibility: ENABLED")
endif()

# 简单测试程序
set(SIMPLE_TEST_SOURCES
  src/test/SimpleStreamTest.cpp
)

add_executable(SimpleStreamTest ${SIMPLE_TEST_SOURCES})
target_include_directories(SimpleStreamTest PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include)

# 完整测试程序
set(TEST_SOURCES
  src/test/StreamCaptureTest.cpp
  src/core/ConfigManager.cpp
  src/core/MediaServerManager.cpp
  src/encoder/FFmpegEncoder.cpp
  src/stream/FFmpegPublisher.cpp
)

add_executable(StreamCaptureTest ${TEST_SOURCES})
target_link_libraries(StreamCaptureTest ${LIBRARIES})
target_include_directories(StreamCaptureTest PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include)

# 设置测试程序输出目录
set_target_properties(StreamCaptureTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/redist_desk"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_CURRENT_SOURCE_DIR}/redist_desk"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_CURRENT_SOURCE_DIR}/redist_desk"
)

install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib/static
)

install(TARGETS StreamCaptureTest
    RUNTIME DESTINATION bin
)