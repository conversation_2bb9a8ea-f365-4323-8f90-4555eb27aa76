# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a simple launcher repository containing a batch script to run <PERSON> with specific proxy and Google Cloud Project configurations.

## Commands

- **Run <PERSON> with proxy settings**: `run.bat` - Sets proxy configuration and Google Cloud Project environment variables before launching Claude

## Environment Variables

The repository configures:
- `GOOGLE_CLOUD_PROJECT`: Set to `stately-vector-465005-d0`
- `https_proxy` and `http_proxy`: Both set to `http://127.0.0.1:10809` for local proxy configuration