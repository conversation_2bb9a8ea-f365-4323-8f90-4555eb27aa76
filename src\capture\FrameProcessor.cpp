#include "FrameProcessor.h"
#include "Win7Compatibility.h"
#include <algorithm>
#include <chrono>

#ifdef max
#undef max
#endif
#ifdef min
#undef min
#endif

namespace StreamCapture {

// FrameBuffer实现
FrameBuffer::FrameBuffer(size_t maxFrames) 
    : maxFrames_(maxFrames) {
    stats_.maxSize = maxFrames;
}

FrameBuffer::~FrameBuffer() {
    Clear();
}

bool FrameBuffer::PushFrame(std::unique_ptr<FrameData> frame) {
    if (!frame) return false;
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 如果缓冲区满了，丢弃最老的帧
    if (frames_.size() >= maxFrames_) {
        frames_.pop();
        stats_.droppedFrames++;
    }
    
    frames_.push(std::move(frame));
    stats_.totalPushed++;
    stats_.currentSize = frames_.size();
    
    condition_.notify_one();
    return true;
}

std::unique_ptr<FrameData> FrameBuffer::PopFrame(uint32_t timeoutMs) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    if (timeoutMs == 0) {
        // 非阻塞模式
        if (frames_.empty()) {
            return nullptr;
        }
    } else {
        // 阻塞模式，等待指定时间
        if (!condition_.wait_for(lock, std::chrono::milliseconds(timeoutMs),
                                 [this] { return !frames_.empty(); })) {
            return nullptr;
        }
    }
    
    auto frame = std::move(frames_.front());
    frames_.pop();
    stats_.totalPopped++;
    stats_.currentSize = frames_.size();
    
    return frame;
}

size_t FrameBuffer::GetSize() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return frames_.size();
}

size_t FrameBuffer::GetMaxSize() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return maxFrames_;
}

bool FrameBuffer::IsEmpty() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return frames_.empty();
}

bool FrameBuffer::IsFull() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return frames_.size() >= maxFrames_;
}

void FrameBuffer::Clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    while (!frames_.empty()) {
        frames_.pop();
    }
    stats_.currentSize = 0;
}

void FrameBuffer::SetMaxSize(size_t maxFrames) {
    std::lock_guard<std::mutex> lock(mutex_);
    maxFrames_ = maxFrames;
    stats_.maxSize = maxFrames;
    
    // 如果当前缓冲区超过新的最大值，移除多余的帧
    while (frames_.size() > maxFrames_) {
        frames_.pop();
        stats_.droppedFrames++;
    }
    stats_.currentSize = frames_.size();
}

FrameBuffer::Stats FrameBuffer::GetStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return stats_;
}

// ImageConverter实现
ImageConverter::ImageConverter() : initialized_(false) {
}

ImageConverter::~ImageConverter() {
    Shutdown();
}

bool ImageConverter::Initialize() {
    if (initialized_) return true;
    
    // 这里可以初始化硬件加速的图像转换库
    // 目前使用CPU实现
    
    initialized_ = true;
    return true;
}

void ImageConverter::Shutdown() {
    initialized_ = false;
}

bool ImageConverter::ConvertRGBAToNV12(const FrameData& input, FrameData& output) {
    if (!initialized_ || !input.data) return false;
    
    if (input.format != DXGI_FORMAT_R8G8B8A8_UNORM) {
        return false;
    }
    
    uint32_t width = input.width;
    uint32_t height = input.height;
    
    // NV12格式：Y平面 + UV交错平面
    size_t yPlaneSize = width * height;
    size_t uvPlaneSize = (width / 2) * (height / 2) * 2;
    size_t totalSize = yPlaneSize + uvPlaneSize;
    
    output.data = new uint8_t[totalSize];
    output.size = totalSize;
    output.width = width;
    output.height = height;
    output.stride = width;
    output.format = DXGI_FORMAT_NV12;
    output.timestamp = input.timestamp;
    output.frameIndex = input.frameIndex;
    
    ConvertRGBAToNV12_CPU(input.data, output.data, width, height, input.stride);
    
    return true;
}

bool ImageConverter::ConvertRGBAToI420(const FrameData& input, FrameData& output) {
    if (!initialized_ || !input.data) return false;
    
    if (input.format != DXGI_FORMAT_R8G8B8A8_UNORM) {
        return false;
    }
    
    uint32_t width = input.width;
    uint32_t height = input.height;
    
    // I420格式：Y + U + V 分离平面
    size_t yPlaneSize = width * height;
    size_t uPlaneSize = (width / 2) * (height / 2);
    size_t vPlaneSize = (width / 2) * (height / 2);
    size_t totalSize = yPlaneSize + uPlaneSize + vPlaneSize;
    
    output.data = new uint8_t[totalSize];
    output.size = totalSize;
    output.width = width;
    output.height = height;
    output.stride = width;
    output.format = DXGI_FORMAT_420_OPAQUE;  // 使用这个表示I420
    output.timestamp = input.timestamp;
    output.frameIndex = input.frameIndex;
    
    ConvertRGBAToI420_CPU(input.data, output.data, width, height, input.stride);
    
    return true;
}

bool ImageConverter::ConvertBGRAToRGBA(const FrameData& input, FrameData& output) {
    if (!initialized_ || !input.data) return false;
    
    if (input.format != DXGI_FORMAT_B8G8R8A8_UNORM) {
        return false;
    }
    
    size_t totalSize = input.size;
    output.data = new uint8_t[totalSize];
    output.size = totalSize;
    output.width = input.width;
    output.height = input.height;
    output.stride = input.stride;
    output.format = DXGI_FORMAT_R8G8B8A8_UNORM;
    output.timestamp = input.timestamp;
    output.frameIndex = input.frameIndex;
    
    // BGRA -> RGBA: 交换R和B通道
    const uint8_t* src = input.data;
    uint8_t* dst = output.data;
    
    for (size_t i = 0; i < totalSize; i += 4) {
        dst[i + 0] = src[i + 2];  // R = B
        dst[i + 1] = src[i + 1];  // G = G
        dst[i + 2] = src[i + 0];  // B = R
        dst[i + 3] = src[i + 3];  // A = A
    }
    
    return true;
}

bool ImageConverter::ResizeFrame(const FrameData& input, FrameData& output,
                                 uint32_t targetWidth, uint32_t targetHeight) {
    if (!initialized_ || !input.data) return false;
    
    size_t bytesPerPixel = GetBytesPerPixel(input.format);
    if (bytesPerPixel == 0) return false;
    
    size_t outputStride = targetWidth * bytesPerPixel;
    size_t totalSize = outputStride * targetHeight;
    
    output.data = new uint8_t[totalSize];
    output.size = totalSize;
    output.width = targetWidth;
    output.height = targetHeight;
    output.stride = static_cast<uint32_t>(outputStride);
    output.format = input.format;
    output.timestamp = input.timestamp;
    output.frameIndex = input.frameIndex;
    
    ResizeFrame_CPU(input.data, output.data,
                    input.width, input.height, input.stride,
                    targetWidth, targetHeight, static_cast<uint32_t>(outputStride),
                    bytesPerPixel);
    
    return true;
}

bool ImageConverter::CropFrame(const FrameData& input, FrameData& output,
                               uint32_t x, uint32_t y, uint32_t width, uint32_t height) {
    if (!initialized_ || !input.data) return false;
    
    // 检查裁剪区域是否有效
    if (x + width > input.width || y + height > input.height) {
        return false;
    }
    
    size_t bytesPerPixel = GetBytesPerPixel(input.format);
    if (bytesPerPixel == 0) return false;
    
    size_t outputStride = width * bytesPerPixel;
    size_t totalSize = outputStride * height;
    
    output.data = new uint8_t[totalSize];
    output.size = totalSize;
    output.width = width;
    output.height = height;
    output.stride = static_cast<uint32_t>(outputStride);
    output.format = input.format;
    output.timestamp = input.timestamp;
    output.frameIndex = input.frameIndex;
    
    // 逐行复制裁剪区域
    const uint8_t* src = input.data + (y * input.stride) + (x * bytesPerPixel);
    uint8_t* dst = output.data;
    
    for (uint32_t row = 0; row < height; ++row) {
        memcpy(dst, src, outputStride);
        src += input.stride;
        dst += outputStride;
    }
    
    return true;
}

bool ImageConverter::IsSupportedFormat(DXGI_FORMAT format) {
    switch (format) {
        case DXGI_FORMAT_R8G8B8A8_UNORM:
        case DXGI_FORMAT_B8G8R8A8_UNORM:
        case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
        case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
            return true;
        default:
            return false;
    }
}

size_t ImageConverter::GetBytesPerPixel(DXGI_FORMAT format) {
    switch (format) {
        case DXGI_FORMAT_R8G8B8A8_UNORM:
        case DXGI_FORMAT_B8G8R8A8_UNORM:
        case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
        case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
            return 4;
        // Remove DXGI_FORMAT_R8G8B8_UINT as it's not commonly supported
        default:
            return 0;
    }
}

std::string ImageConverter::FormatToString(DXGI_FORMAT format) {
    switch (format) {
        case DXGI_FORMAT_R8G8B8A8_UNORM: return "RGBA8_UNORM";
        case DXGI_FORMAT_B8G8R8A8_UNORM: return "BGRA8_UNORM";
        case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB: return "RGBA8_SRGB";
        case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB: return "BGRA8_SRGB";
        case DXGI_FORMAT_NV12: return "NV12";
        case DXGI_FORMAT_420_OPAQUE: return "I420";
        default: return "Unknown";
    }
}

void ImageConverter::ConvertRGBAToNV12_CPU(const uint8_t* rgba, uint8_t* nv12,
                                           uint32_t width, uint32_t height, uint32_t stride) {
    // Y平面
    uint8_t* yPlane = nv12;
    // UV交错平面
    uint8_t* uvPlane = nv12 + width * height;
    
    for (uint32_t y = 0; y < height; ++y) {
        for (uint32_t x = 0; x < width; ++x) {
            const uint8_t* pixel = rgba + (y * stride) + (x * 4);
            uint8_t r = pixel[0];
            uint8_t g = pixel[1];
            uint8_t b = pixel[2];
            
            // RGB to Y (ITU-R BT.601)
            int yValue = (66 * r + 129 * g + 25 * b + 128) >> 8;
            yPlane[y * width + x] = ClampToByte(yValue + 16);
        }
    }
    
    // UV平面 (2x2采样)
    for (uint32_t y = 0; y < height; y += 2) {
        for (uint32_t x = 0; x < width; x += 2) {
            // 从2x2像素块中采样
            const uint8_t* pixel = rgba + (y * stride) + (x * 4);
            uint8_t r = pixel[0];
            uint8_t g = pixel[1];
            uint8_t b = pixel[2];
            
            // RGB to UV (ITU-R BT.601)
            int uValue = (-38 * r - 74 * g + 112 * b + 128) >> 8;
            int vValue = (112 * r - 94 * g - 18 * b + 128) >> 8;
            
            size_t uvIndex = ((y / 2) * (width / 2) + (x / 2)) * 2;
            uvPlane[uvIndex + 0] = ClampToByte(uValue + 128);  // U
            uvPlane[uvIndex + 1] = ClampToByte(vValue + 128);  // V
        }
    }
}

void ImageConverter::ConvertRGBAToI420_CPU(const uint8_t* rgba, uint8_t* i420,
                                           uint32_t width, uint32_t height, uint32_t stride) {
    // I420: Y + U + V 分离平面
    uint8_t* yPlane = i420;
    uint8_t* uPlane = i420 + width * height;
    uint8_t* vPlane = i420 + width * height + (width / 2) * (height / 2);
    
    // Y平面
    for (uint32_t y = 0; y < height; ++y) {
        for (uint32_t x = 0; x < width; ++x) {
            const uint8_t* pixel = rgba + (y * stride) + (x * 4);
            uint8_t r = pixel[0];
            uint8_t g = pixel[1];
            uint8_t b = pixel[2];
            
            // RGB to Y
            int yValue = (66 * r + 129 * g + 25 * b + 128) >> 8;
            yPlane[y * width + x] = ClampToByte(yValue + 16);
        }
    }
    
    // U和V平面 (2x2采样)
    for (uint32_t y = 0; y < height; y += 2) {
        for (uint32_t x = 0; x < width; x += 2) {
            const uint8_t* pixel = rgba + (y * stride) + (x * 4);
            uint8_t r = pixel[0];
            uint8_t g = pixel[1];
            uint8_t b = pixel[2];
            
            // RGB to UV
            int uValue = (-38 * r - 74 * g + 112 * b + 128) >> 8;
            int vValue = (112 * r - 94 * g - 18 * b + 128) >> 8;
            
            size_t uvIndex = (y / 2) * (width / 2) + (x / 2);
            uPlane[uvIndex] = ClampToByte(uValue + 128);
            vPlane[uvIndex] = ClampToByte(vValue + 128);
        }
    }
}

void ImageConverter::ResizeFrame_CPU(const uint8_t* input, uint8_t* output,
                                     uint32_t inputWidth, uint32_t inputHeight, uint32_t inputStride,
                                     uint32_t outputWidth, uint32_t outputHeight, uint32_t outputStride,
                                     size_t bytesPerPixel) {
    // 简单的最近邻插值
    float xRatio = static_cast<float>(inputWidth) / outputWidth;
    float yRatio = static_cast<float>(inputHeight) / outputHeight;
    
    for (uint32_t y = 0; y < outputHeight; ++y) {
        for (uint32_t x = 0; x < outputWidth; ++x) {
            uint32_t srcX = static_cast<uint32_t>(x * xRatio);
            uint32_t srcY = static_cast<uint32_t>(y * yRatio);
            
            // 确保不超出边界
            srcX = std::min(srcX, inputWidth - 1);
            srcY = std::min(srcY, inputHeight - 1);
            
            const uint8_t* srcPixel = input + (srcY * inputStride) + (srcX * bytesPerPixel);
            uint8_t* dstPixel = output + (y * outputStride) + (x * bytesPerPixel);
            
            memcpy(dstPixel, srcPixel, bytesPerPixel);
        }
    }
}

uint8_t ImageConverter::ClampToByte(int value) {
    return static_cast<uint8_t>(std::max(0, std::min(255, value)));
}

// FrameProcessor实现
FrameProcessor::FrameProcessor() : running_(false) {
    QueryPerformanceFrequency(&perfFrequency_);
}

FrameProcessor::~FrameProcessor() {
    Shutdown();
}

bool FrameProcessor::Initialize(const Config& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (running_) {
        return false;
    }
    
    config_ = config;
    
    // 创建缓冲区
    inputBuffer_ = std::make_unique<FrameBuffer>(config.bufferSize);
    outputBuffer_ = std::make_unique<FrameBuffer>(config.bufferSize);
    
    // 创建转换器
    converter_ = std::make_unique<ImageConverter>();
    if (!converter_->Initialize()) {
        return false;
    }
    
    // 启动处理线程
    running_ = true;
    processingThread_ = std::thread(&FrameProcessor::ProcessingThread, this);
    
    return true;
}

void FrameProcessor::Shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    if (processingThread_.joinable()) {
        processingThread_.join();
    }
    
    if (converter_) {
        converter_->Shutdown();
        converter_.reset();
    }
    
    inputBuffer_.reset();
    outputBuffer_.reset();
}

bool FrameProcessor::ProcessFrame(std::unique_ptr<FrameData> inputFrame) {
    if (!running_ || !inputFrame) {
        return false;
    }
    
    stats_.inputFrames++;
    return inputBuffer_->PushFrame(std::move(inputFrame));
}

std::unique_ptr<FrameData> FrameProcessor::GetProcessedFrame(uint32_t timeoutMs) {
    if (!running_) {
        return nullptr;
    }
    
    return outputBuffer_->PopFrame(timeoutMs);
}

void FrameProcessor::UpdateConfig(const Config& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = config;
    
    if (inputBuffer_) {
        inputBuffer_->SetMaxSize(config.bufferSize);
    }
    if (outputBuffer_) {
        outputBuffer_->SetMaxSize(config.bufferSize);
    }
}

FrameProcessor::Config FrameProcessor::GetConfig() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return config_;
}

FrameProcessor::Stats FrameProcessor::GetStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    Stats currentStats = stats_;
    
    if (inputBuffer_) {
        auto bufferStats = inputBuffer_->GetStats();
        currentStats.bufferUtilization = (bufferStats.currentSize * 100) / bufferStats.maxSize;
    }
    
    return currentStats;
}

void FrameProcessor::SetProcessedFrameCallback(ProcessedFrameCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    callback_ = callback;
}

void FrameProcessor::ProcessingThread() {
    while (running_) {
        auto inputFrame = inputBuffer_->PopFrame(100); // 100ms超时
        if (!inputFrame) {
            continue;
        }
        
        LARGE_INTEGER startTime;
        QueryPerformanceCounter(&startTime);
        
        try {
            auto outputFrame = std::make_unique<FrameData>();
            if (ProcessSingleFrame(*inputFrame, *outputFrame)) {
                stats_.outputFrames++;
                
                // 发送到输出缓冲区
                if (!outputBuffer_->PushFrame(std::move(outputFrame))) {
                    stats_.droppedFrames++;
                }
                
                // 调用回调
                if (callback_) {
                    auto callbackFrame = std::make_unique<FrameData>();
                    if (ProcessSingleFrame(*inputFrame, *callbackFrame)) {
                        callback_(std::move(callbackFrame));
                    }
                }
            } else {
                stats_.droppedFrames++;
            }
        } catch (...) {
            stats_.droppedFrames++;
        }
        
        // 计算处理时间
        LARGE_INTEGER endTime;
        QueryPerformanceCounter(&endTime);
        float processingTime = static_cast<float>(endTime.QuadPart - startTime.QuadPart) / 
                               perfFrequency_.QuadPart * 1000.0f;
        stats_.avgProcessingTime = (stats_.avgProcessingTime * 0.9f) + (processingTime * 0.1f);
    }
}

bool FrameProcessor::ProcessSingleFrame(const FrameData& input, FrameData& output) {
    // 复制输入到临时变量
    FrameData tempFrame;
    tempFrame.width = input.width;
    tempFrame.height = input.height;
    tempFrame.format = input.format;
    tempFrame.stride = input.stride;
    tempFrame.timestamp = input.timestamp;
    tempFrame.frameIndex = input.frameIndex;
    tempFrame.size = input.size;
    tempFrame.data = new uint8_t[input.size];
    memcpy(tempFrame.data, input.data, input.size);
    
    // 应用裁剪
    if (config_.enableCrop) {
        FrameData croppedFrame;
        if (!converter_->CropFrame(tempFrame, croppedFrame,
                                   config_.cropX, config_.cropY,
                                   config_.cropWidth, config_.cropHeight)) {
            return false;
        }
        
        delete[] tempFrame.data;
        tempFrame = std::move(croppedFrame);
    }
    
    // 应用缩放
    if (config_.enableResize && config_.targetWidth > 0 && config_.targetHeight > 0) {
        FrameData resizedFrame;
        if (!converter_->ResizeFrame(tempFrame, resizedFrame,
                                     config_.targetWidth, config_.targetHeight)) {
            delete[] tempFrame.data;
            return false;
        }
        
        delete[] tempFrame.data;
        tempFrame = std::move(resizedFrame);
    }
    
    // 格式转换
    if (config_.outputFormat == "nv12") {
        if (!converter_->ConvertRGBAToNV12(tempFrame, output)) {
            delete[] tempFrame.data;
            return false;
        }
    } else if (config_.outputFormat == "i420") {
        if (!converter_->ConvertRGBAToI420(tempFrame, output)) {
            delete[] tempFrame.data;
            return false;
        }
    } else {
        // 直接复制
        output = std::move(tempFrame);
        return true;
    }
    
    delete[] tempFrame.data;
    return true;
}

} // namespace StreamCapture