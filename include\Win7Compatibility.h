#pragma once

#include "Common.h"

namespace StreamCapture {

class Win7Compatibility {
public:
    static Win7Compatibility& Instance();
    
    // 系统检测
    bool Initialize();
    SystemInfo GetSystemInfo() const { return systemInfo_; }
    bool IsWindows7() const { return systemInfo_.isWindows7; }
    
    // 兼容性API包装
    HANDLE SafeCreateRemoteThread(HANDLE hProcess, LPSECURITY_ATTRIBUTES lpThreadAttributes,
                                  SIZE_T dwStackSize, LPTHREAD_START_ROUTINE lpStartAddress,
                                  LPVOID lpParameter, DWORD dwCreationFlags, LPDWORD lpThreadId);
    
    LPVOID SafeVirtualAllocEx(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize,
                              DWORD flAllocationType, DWORD flProtect);
    
    BOOL SafeWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress,
                                LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten);
    
    // 内存管理
    void* AllocateAligned(size_t size, size_t alignment = 16);
    void FreeAligned(void* ptr);
    
    // 错误处理
    std::string GetLastErrorString() const;
    void HandleCrash(EXCEPTION_POINTERS* pExceptionInfo);
    
    // 性能监控
    bool IsLowMemory() const;
    size_t GetAvailableMemory() const;
    
private:
    Win7Compatibility() = default;
    ~Win7Compatibility() = default;
    
    void DetectSystemCapabilities();
    void SetupExceptionHandler();
    
    SystemInfo systemInfo_;
    mutable std::mutex mutex_;
    
    // 动态加载的API
    typedef HANDLE(WINAPI* CreateRemoteThreadProc)(HANDLE, LPSECURITY_ATTRIBUTES, SIZE_T, 
                                                   LPTHREAD_START_ROUTINE, LPVOID, DWORD, LPDWORD);
    CreateRemoteThreadProc createRemoteThreadProc_ = nullptr;
};

// RAII内存管理辅助类
class SafeMemory {
public:
    SafeMemory(size_t size, size_t alignment = 16)
        : ptr_(Win7Compatibility::Instance().AllocateAligned(size, alignment))
        , size_(size) {}
    
    ~SafeMemory() {
        if (ptr_) {
            Win7Compatibility::Instance().FreeAligned(ptr_);
        }
    }
    
    void* get() const { return ptr_; }
    size_t size() const { return size_; }
    
    // 禁止拷贝
    SafeMemory(const SafeMemory&) = delete;
    SafeMemory& operator=(const SafeMemory&) = delete;
    
    // 允许移动
    SafeMemory(SafeMemory&& other) noexcept
        : ptr_(other.ptr_), size_(other.size_) {
        other.ptr_ = nullptr;
        other.size_ = 0;
    }
    
    SafeMemory& operator=(SafeMemory&& other) noexcept {
        if (this != &other) {
            if (ptr_) Win7Compatibility::Instance().FreeAligned(ptr_);
            ptr_ = other.ptr_;
            size_ = other.size_;
            other.ptr_ = nullptr;
            other.size_ = 0;
        }
        return *this;
    }
    
private:
    void* ptr_;
    size_t size_;
};

} // namespace StreamCapture