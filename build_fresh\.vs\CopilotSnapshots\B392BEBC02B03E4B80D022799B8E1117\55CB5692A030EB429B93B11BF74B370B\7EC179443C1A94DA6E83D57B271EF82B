﻿#include "FFmpegEncoder.h"
#include "StreamCaptureController.h"
#include <chrono>
#include <algorithm>

namespace StreamCapture
{

    // FFmpeg 全局初始化
    bool FFmpegUtils::initialized_ = false;
    std::mutex FFmpegUtils::initMutex_;

    bool FFmpegUtils::InitializeFFmpeg()
    {
        std::lock_guard<std::mutex> lock(initMutex_);
        if (initialized_)
        {
            return true;
        }

        // 设置日志级别
        av_log_set_level(AV_LOG_WARNING);

        // 注册所有编解码器和格式
        avformat_network_init();

        initialized_ = true;
        return true;
    }

    void FFmpegUtils::CleanupFFmpeg()
    {
        std::lock_guard<std::mutex> lock(initMutex_);
        if (initialized_)
        {
            avformat_network_deinit();
            initialized_ = false;
        }
    }

    std::vector<std::string> FFmpegUtils::GetSupportedHardwareTypes()
    {
        std::vector<std::string> hwTypes;

        AVHWDeviceType type = AV_HWDEVICE_TYPE_NONE;
        while ((type = av_hwdevice_iterate_types(type)) != AV_HWDEVICE_TYPE_NONE)
        {
            const char *name = av_hwdevice_get_type_name(type);
            if (name)
            {
                hwTypes.emplace_back(name);
            }
        }

        return hwTypes;
    }

    bool FFmpegUtils::IsHardwareTypeSupported(const std::string& hwType)
    {
        try {
            AVHWDeviceType type = av_hwdevice_find_type_by_name(hwType.c_str());
            if (type == AV_HWDEVICE_TYPE_NONE) {
                return false;
            }

            // 尝试创建硬件设备上下文来验证支持
            AVBufferRef* deviceContext = nullptr;
            int ret = av_hwdevice_ctx_create(&deviceContext, type, nullptr, nullptr, 0);
            if (ret >= 0) {
                // 成功创建硬件设备上下文，说明硬件编码可用
                av_buffer_unref(&deviceContext);
                return true;
            }
        } catch (...) {
            // 忽略异常
        }
        
        return false;
    }

    std::vector<std::string> FFmpegUtils::GetSupportedCodecs()
    {
        std::vector<std::string> codecs;

        void *iter = nullptr;
        const AVCodec *codec = nullptr;

        while ((codec = av_codec_iterate(&iter)))
        {
            if (av_codec_is_encoder(codec) && codec->type == AVMEDIA_TYPE_VIDEO)
            {
                codecs.emplace_back(codec->name);
            }
        }

        return codecs;
    }

    bool FFmpegUtils::IsCodecSupported(const std::string &codec)
    {
        const AVCodec *avCodec = avcodec_find_encoder_by_name(codec.c_str());
        return avCodec != nullptr;
    }

    std::string FFmpegUtils::GetErrorString(int errorCode)
    {
        char errorBuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(errorCode, errorBuf, sizeof(errorBuf));
        return std::string(errorBuf);
    }

    void FFmpegUtils::LogFFmpegError(int errorCode, const std::string &operation)
    {
        std::string errorMsg = GetErrorString(errorCode);
        // 这里可以添加日志记录
        // LOG_ERROR_F("FFmpeg error in {}: {} ({})", operation, errorMsg, errorCode);
    }

    std::string FFmpegUtils::GetFFmpegVersion()
    {
        return av_version_info();
    }

    std::string FFmpegUtils::GetCodecVersion()
    {
        return std::to_string(avcodec_version());
    }

    // FFmpegEncoder 实现
    FFmpegEncoder::FFmpegEncoder()
        : codecCtx_(nullptr), codec_(nullptr), frame_(nullptr), hwFrame_(nullptr),
          packet_(nullptr), swsCtx_(nullptr), hwDeviceCtx_(nullptr),
          hwType_(AV_HWDEVICE_TYPE_NONE), useHardware_(false), initialized_(false),
          running_(false), frameCount_(0), startTime_(0), maxQueueSize_(10)
    {

        FFmpegUtils::InitializeFFmpeg();

        // 初始化时间基
        timeBase_ = {1, 90000}; // 90kHz 时间基
    }

    FFmpegEncoder::~FFmpegEncoder()
    {
        Shutdown();
    }

    bool FFmpegEncoder::InitializeEx(const EncoderConfigEx &config)
    {
        std::lock_guard<std::mutex> lock(statsMutex_);

        if (initialized_)
        {
            return false;
        }

        config_ = config;

        // 设置硬件加速
        if (config_.type == "hardware")
        {
            useHardware_ = true;
            if (!InitializeHardwareEncoder(config_.hardwareType))
            {
                // 硬件编码器初始化失败，回退到软件编码
                useHardware_ = false;
            }
        }

        // 设置编解码器
        if (!SetupCodec())
        {
            return false;
        }

        // 设置像素格式
        if (!SetupPixelFormat())
        {
            return false;
        }

        // 设置编码参数
        if (!SetupEncodingParameters())
        {
            return false;
        }

        // 分配帧
        if (!AllocateFrames())
        {
            return false;
        }

        // 打开编码器
        int ret = avcodec_open2(codecCtx_, codec_, nullptr);
        if (ret < 0)
        {
            HandleEncodingError(ret, "avcodec_open2");
            return false;
        }

        // 启动编码线程
        running_ = true;
        encodingThread_ = std::thread(&FFmpegEncoder::EncodingThreadFunc, this);

        initialized_ = true;
        startTime_ = av_gettime();

        ResetStats();

        return true;
    }

    void FFmpegEncoder::Shutdown()
    {
        if (!initialized_)
        {
            return;
        }

        // 停止编码线程
        running_ = false;
        inputCondition_.notify_all();

        if (encodingThread_.joinable())
        {
            encodingThread_.join();
        }

        // 清理资源
        CleanupFFmpeg();
        CleanupQueues();

        initialized_ = false;
    }

    bool FFmpegEncoder::EncodeFrame(const FrameData &frame)
    {
        if (!initialized_ || !running_)
        {
            return false;
        }

        // 创建帧副本
        auto frameCopy = std::make_unique<FrameData>();
        frameCopy->width = frame.width;
        frameCopy->height = frame.height;
        frameCopy->stride = frame.stride;
        frameCopy->format = frame.format;
        frameCopy->timestamp = frame.timestamp;
        frameCopy->frameIndex = frame.frameIndex;
        frameCopy->size = frame.size;

        if (frame.data && frame.size > 0)
        {
            frameCopy->data = new uint8_t[frame.size];
            memcpy(frameCopy->data, frame.data, frame.size);
        }

        // 添加到输入队列
        {
            std::lock_guard<std::mutex> lock(inputMutex_);

            // 检查队列大小
            if (inputQueue_.size() >= maxQueueSize_)
            {
                // 丢弃最旧的帧
                inputQueue_.pop();
                stats_.droppedFrames++;
            }

            inputQueue_.push(std::move(frameCopy));
            stats_.inputFrames++;
        }

        inputCondition_.notify_one();
        return true;
    }

    std::unique_ptr<EncodedPacket> FFmpegEncoder::GetEncodedPacket(uint32_t timeoutMs)
    {
        std::unique_lock<std::mutex> lock(outputMutex_);

        if (timeoutMs == 0)
        {
            // 非阻塞模式
            if (outputQueue_.empty())
            {
                return nullptr;
            }
        }
        else
        {
            // 阻塞模式
            auto timeout = std::chrono::milliseconds(timeoutMs);
            if (!outputCondition_.wait_for(lock, timeout, [this]
                                           { return !outputQueue_.empty(); }))
            {
                return nullptr;
            }
        }

        auto packet = std::move(outputQueue_.front());
        outputQueue_.pop();

        return packet;
    }

    bool FFmpegEncoder::SetBitrate(uint32_t bitrate)
    {
        if (!initialized_)
        {
            return false;
        }

        std::lock_guard<std::mutex> lock(statsMutex_);

        config_.bitrate = bitrate;

        if (codecCtx_)
        {
            codecCtx_->bit_rate = bitrate * 1000; // 转换为 bps
            return true;
        }

        return false;
    }

    bool FFmpegEncoder::ForceKeyFrame()
    {
        if (!initialized_)
        {
            return false;
        }

        // 在下一帧强制关键帧
        if (frame_)
        {
            frame_->pict_type = AV_PICTURE_TYPE_I;
            return true;
        }

        return false;
    }

    bool FFmpegEncoder::Flush()
    {
        if (!initialized_)
        {
            return false;
        }

        // 发送 NULL 帧来刷新编码器
        {
            std::lock_guard<std::mutex> lock(inputMutex_);
            inputQueue_.push(nullptr); // NULL 表示刷新
        }

        inputCondition_.notify_one();
        return true;
    }

    VideoEncoder::Stats FFmpegEncoder::GetStats() const
    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        return stats_;
    }

    bool FFmpegEncoder::InitializeHardwareEncoder(const std::string &hwType)
    {
        hwType_ = av_hwdevice_find_type_by_name(hwType.c_str());
        if (hwType_ == AV_HWDEVICE_TYPE_NONE)
        {
            return false;
        }

        // 创建硬件设备上下文
        int ret = av_hwdevice_ctx_create(&hwDeviceCtx_, hwType_, nullptr, nullptr, 0);
        if (ret < 0)
        {
            HandleEncodingError(ret, "av_hwdevice_ctx_create");
            return false;
        }

        return true;
    }

    bool FFmpegEncoder::SetupCodec()
    {
        std::string codecName;

        if (useHardware_)
        {
            // 硬件编码器名称
            if (config_.codec == "h264")
            {
                if (config_.hardwareType == "nvenc")
                {
                    codecName = "h264_nvenc";
                }
                else if (config_.hardwareType == "qsv")
                {
                    codecName = "h264_qsv";
                }
                else if (config_.hardwareType == "amf")
                {
                    codecName = "h264_amf";
                }
            }
            else if (config_.codec == "h265")
            {
                if (config_.hardwareType == "nvenc")
                {
                    codecName = "hevc_nvenc";
                }
                else if (config_.hardwareType == "qsv")
                {
                    codecName = "hevc_qsv";
                }
                else if (config_.hardwareType == "amf")
                {
                    codecName = "hevc_amf";
                }
            }
        }
        else
        {
            // 软件编码器名称
            if (config_.codec == "h264")
            {
                codecName = "libx264";
            }
            else if (config_.codec == "h265")
            {
                codecName = "libx265";
            }
        }

        if (codecName.empty())
        {
            return false;
        }

        codec_ = avcodec_find_encoder_by_name(codecName.c_str());
        if (!codec_)
        {
            return false;
        }

        codecCtx_ = avcodec_alloc_context3(codec_);
        if (!codecCtx_)
        {
            return false;
        }

        return true;
    }

    bool FFmpegEncoder::SetupPixelFormat()
    {
        if (!codecCtx_)
        {
            return false;
        }

        // 设置基本参数
        codecCtx_->width = config_.width;
        codecCtx_->height = config_.height;
        codecCtx_->time_base = {1, config_.fps};
        codecCtx_->framerate = {config_.fps, 1};

        if (useHardware_ && hwDeviceCtx_)
        {
            // 硬件编码像素格式
            codecCtx_->pix_fmt = GetHardwarePixelFormat(hwType_);
            codecCtx_->hw_device_ctx = av_buffer_ref(hwDeviceCtx_);
        }
        else
        {
            // 软件编码像素格式
            codecCtx_->pix_fmt = AV_PIX_FMT_YUV420P;
        }

        return true;
    }

    bool FFmpegEncoder::SetupEncodingParameters()
    {
        if (!codecCtx_)
        {
            return false;
        }

        // 基本编码参数
        codecCtx_->bit_rate = config_.bitrate * 1000; // 转换为 bps
        codecCtx_->gop_size = config_.keyframeInterval;
        codecCtx_->max_b_frames = config_.enableBFrames ? 2 : 0;

        // 设置编码器选项
        if (useHardware_)
        {
            return SetHardwareEncoderOptions();
        }
        else
        {
            return SetSoftwareEncoderOptions();
        }
    }

    bool FFmpegEncoder::SetHardwareEncoderOptions()
    {
        AVDictionary *opts = nullptr;

        if (config_.hardwareType == "nvenc")
        {
            // NVENC 选项
            av_dict_set(&opts, "preset", config_.preset.c_str(), 0);
            av_dict_set(&opts, "profile", config_.profile.c_str(), 0);
            av_dict_set(&opts, "rc", "cbr", 0);  // 恒定比特率
            av_dict_set(&opts, "delay", "0", 0); // 低延迟
            av_dict_set(&opts, "zerolatency", "1", 0);
        }
        else if (config_.hardwareType == "qsv")
        {
            // Intel QSV 选项
            av_dict_set(&opts, "preset", config_.preset.c_str(), 0);
            av_dict_set(&opts, "profile", config_.profile.c_str(), 0);
        }
        else if (config_.hardwareType == "amf")
        {
            // AMD AMF 选项
            av_dict_set(&opts, "quality", config_.preset.c_str(), 0);
            av_dict_set(&opts, "profile", config_.profile.c_str(), 0);
        }

        // 应用选项到编码器上下文
        AVDictionaryEntry *entry = nullptr;
        while ((entry = av_dict_get(opts, "", entry, AV_DICT_IGNORE_SUFFIX)))
        {
            av_opt_set(codecCtx_->priv_data, entry->key, entry->value, 0);
        }

        av_dict_free(&opts);
        return true;
    }

    bool FFmpegEncoder::SetSoftwareEncoderOptions()
    {
        AVDictionary *opts = nullptr;

        if (config_.codec == "h264")
        {
            // x264 选项
            av_dict_set(&opts, "preset", config_.preset.c_str(), 0);
            av_dict_set(&opts, "profile", config_.profile.c_str(), 0);
            av_dict_set(&opts, "tune", "zerolatency", 0);

            // 设置线程数
            av_dict_set_int(&opts, "threads", 0, 0); // 自动检测
        }
        else if (config_.codec == "h265")
        {
            // x265 选项
            av_dict_set(&opts, "preset", config_.preset.c_str(), 0);
            av_dict_set(&opts, "profile", config_.profile.c_str(), 0);
        }

        // 应用选项
        AVDictionaryEntry *entry = nullptr;
        while ((entry = av_dict_get(opts, "", entry, AV_DICT_IGNORE_SUFFIX)))
        {
            av_opt_set(codecCtx_->priv_data, entry->key, entry->value, 0);
        }

        av_dict_free(&opts);
        return true;
    }

    bool FFmpegEncoder::AllocateFrames()
    {
        // 分配输入帧
        frame_ = av_frame_alloc();
        if (!frame_)
        {
            return false;
        }

        frame_->format = codecCtx_->pix_fmt;
        frame_->width = codecCtx_->width;
        frame_->height = codecCtx_->height;

        int ret = av_frame_get_buffer(frame_, 0);
        if (ret < 0)
        {
            HandleEncodingError(ret, "av_frame_get_buffer");
            return false;
        }

        // 如果使用硬件编码，分配硬件帧
        if (useHardware_ && hwDeviceCtx_)
        {
            hwFrame_ = av_frame_alloc();
            if (!hwFrame_)
            {
                return false;
            }

            hwFrame_->format = codecCtx_->pix_fmt;
            hwFrame_->width = codecCtx_->width;
            hwFrame_->height = codecCtx_->height;

            ret = av_hwframe_get_buffer(codecCtx_->hw_frames_ctx, hwFrame_, 0);
            if (ret < 0)
            {
                // 硬件帧分配失败，继续使用软件帧
                av_frame_free(&hwFrame_);
                hwFrame_ = nullptr;
            }
        }

        // 分配数据包
        packet_ = av_packet_alloc();
        if (!packet_)
        {
            return false;
        }

        return true;
    }

    AVPixelFormat FFmpegEncoder::GetHardwarePixelFormat(AVHWDeviceType hwType)
    {
        switch (hwType)
        {
        case AV_HWDEVICE_TYPE_D3D11VA:
            return AV_PIX_FMT_D3D11;
        case AV_HWDEVICE_TYPE_CUDA:
            return AV_PIX_FMT_CUDA;
        case AV_HWDEVICE_TYPE_QSV:
            return AV_PIX_FMT_QSV;
        default:
            return AV_PIX_FMT_YUV420P;
        }
    }

    void FFmpegEncoder::EncodingThreadFunc()
    {
        while (running_)
        {
            std::unique_ptr<FrameData> frameData;

            // 获取输入帧
            {
                std::unique_lock<std::mutex> lock(inputMutex_);
                inputCondition_.wait(lock, [this]
                                     { return !inputQueue_.empty() || !running_; });

                if (!running_)
                {
                    break;
                }

                frameData = std::move(inputQueue_.front());
                inputQueue_.pop();
            }

            // 处理帧
            if (frameData)
            {
                // 转换并编码帧
                if (ConvertFrame(*frameData, frame_))
                {
                    AVFrame *encodeFrame = frame_;

                    // 如果使用硬件编码，转换到硬件帧
                    if (useHardware_ && hwFrame_)
                    {
                        if (ConvertToHardwareFrame(frame_, hwFrame_))
                        {
                            encodeFrame = hwFrame_;
                        }
                    }

                    // 设置时间戳
                    encodeFrame->pts = frameCount_++;

                    // 编码帧
                    EncodeAVFrame(encodeFrame);
                }
            }
            else
            {
                // NULL 帧表示刷新编码器
                EncodeAVFrame(nullptr);
            }
        }

        // 刷新编码器
        EncodeAVFrame(nullptr);
    }

    bool FFmpegEncoder::ConvertFrame(const FrameData &input, AVFrame *output)
    {
        if (!output || !input.data)
        {
            return false;
        }

        // 确保帧可写
        int ret = av_frame_make_writable(output);
        if (ret < 0)
        {
            return false;
        }

        // 简单的 RGBA 到 YUV420P 转换
        if (input.format == DXGI_FORMAT_R8G8B8A8_UNORM || input.format == DXGI_FORMAT_B8G8R8A8_UNORM)
        {
            // 创建 SwsContext 如果需要
            if (!swsCtx_)
            {
                swsCtx_ = sws_getContext(
                    input.width, input.height, AV_PIX_FMT_BGRA,
                    output->width, output->height, (AVPixelFormat)output->format,
                    SWS_BILINEAR, nullptr, nullptr, nullptr);

                if (!swsCtx_)
                {
                    return false;
                }
            }

            // 设置输入数据
            const uint8_t *srcData[4] = {input.data, nullptr, nullptr, nullptr};
            int srcLinesize[4] = {(int)input.stride, 0, 0, 0};

            // 转换
            ret = sws_scale(swsCtx_, srcData, srcLinesize, 0, input.height,
                            output->data, output->linesize);

            return ret > 0;
        }

        return false;
    }

    bool FFmpegEncoder::ConvertToHardwareFrame(AVFrame *swFrame, AVFrame *hwFrame)
    {
        if (!swFrame || !hwFrame)
        {
            return false;
        }

        // 将软件帧数据传输到硬件帧
        int ret = av_hwframe_transfer_data(hwFrame, swFrame, 0);
        if (ret < 0)
        {
            return false;
        }

        // 复制时间戳和其他属性
        hwFrame->pts = swFrame->pts;
        hwFrame->pict_type = swFrame->pict_type;

        return true;
    }

    bool FFmpegEncoder::EncodeAVFrame(AVFrame *frame)
    {
        if (!codecCtx_ || !packet_)
        {
            return false;
        }

        // 发送帧到编码器
        int ret = avcodec_send_frame(codecCtx_, frame);
        if (ret < 0)
        {
            HandleEncodingError(ret, "avcodec_send_frame");
            return false;
        }

        // 接收编码后的数据包
        while (ret >= 0)
        {
            ret = avcodec_receive_packet(codecCtx_, packet_);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
            {
                break;
            }
            else if (ret < 0)
            {
                HandleEncodingError(ret, "avcodec_receive_packet");
                return false;
            }

            // 创建输出数据包
            auto encodedPacket = CreatePacketFromAVPacket(packet_);
            if (encodedPacket)
            {
                // 添加到输出队列
                {
                    std::lock_guard<std::mutex> lock(outputMutex_);
                    outputQueue_.push(std::move(encodedPacket));
                }
                outputCondition_.notify_one();
            }

            av_packet_unref(packet_);
        }

        return true;
    }

    std::unique_ptr<EncodedPacket> FFmpegEncoder::CreatePacketFromAVPacket(AVPacket *avPacket)
    {
        if (!avPacket || !avPacket->data || avPacket->size <= 0)
        {
            return nullptr;
        }

        auto packet = std::make_unique<EncodedPacket>();
        packet->size = avPacket->size;
        packet->data = new uint8_t[packet->size];
        memcpy(packet->data, avPacket->data, packet->size);

        // 转换时间戳
        packet->pts = avPacket->pts;
        packet->dts = avPacket->dts;
        packet->timestamp = av_rescale_q(avPacket->pts, timeBase_, {1, 1000000}); // 转换为微秒
        packet->isKeyFrame = (avPacket->flags & AV_PKT_FLAG_KEY) != 0;

        // 更新统计
        UpdateStats(packet->isKeyFrame, packet->size);

        return packet;
    }

    void FFmpegEncoder::HandleEncodingError(int errorCode, const std::string &operation)
    {
        std::string errorMsg = GetFFmpegErrorString(errorCode);
        // 这里可以添加日志记录
        // LOG_ERROR_F("FFmpeg encoding error in {}: {} ({})", operation, errorMsg, errorCode);
    }

    std::string FFmpegEncoder::GetFFmpegErrorString(int errorCode)
    {
        char errorBuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(errorCode, errorBuf, sizeof(errorBuf));
        return std::string(errorBuf);
    }

    void FFmpegEncoder::UpdateStats(bool isKeyFrame, size_t packetSize)
    {
        std::lock_guard<std::mutex> lock(statsMutex_);

        stats_.outputPackets++;
        stats_.totalBytes += packetSize;

        // 计算平均比特率
        int64_t currentTime = av_gettime();
        if (startTime_ > 0)
        {
            int64_t elapsedTime = currentTime - startTime_;
            if (elapsedTime > 0)
            {
                stats_.avgBitrate = (float)(stats_.totalBytes * 8.0 * 1000000.0 / elapsedTime / 1000.0); // kbps
            }
        }
    }

    void FFmpegEncoder::ResetStats()
    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        stats_ = Stats{};
    }

    void FFmpegEncoder::CleanupFFmpeg()
    {
        if (swsCtx_)
        {
            sws_freeContext(swsCtx_);
            swsCtx_ = nullptr;
        }

        if (packet_)
        {
            av_packet_free(&packet_);
        }

        if (hwFrame_)
        {
            av_frame_free(&hwFrame_);
        }

        if (frame_)
        {
            av_frame_free(&frame_);
        }

        if (codecCtx_)
        {
            avcodec_free_context(&codecCtx_);
        }

        if (hwDeviceCtx_)
        {
            av_buffer_unref(&hwDeviceCtx_);
        }
    }

    void FFmpegEncoder::CleanupQueues()
    {
        {
            std::lock_guard<std::mutex> lock(inputMutex_);
            while (!inputQueue_.empty())
            {
                inputQueue_.pop();
            }
        }

        {
            std::lock_guard<std::mutex> lock(outputMutex_);
            while (!outputQueue_.empty())
            {
                outputQueue_.pop();
            }
        }
    }

} // namespace StreamCapture